<template>
  <div id="offer-manager-component" class="offer-manager">
    <PageHeader title="Usuários matriculados">
      <template #actions>
        <BackButton @click="goBack" />
      </template>
    </PageHeader>

    <!-- <PERSON><PERSON><PERSON><PERSON> da página e botão de Matricular novo usuário -->
    <div style="display: flex; align-items: center; margin-bottom: 20px; gap: 10px">
      <div style="width: 240px">
        <HierarchicalSelect v-model="selectedPageView" :options="pageViewOptions" @navigate="handlePageViewChange" />
      </div>
      <CustomButton v-if="!classDetails || classDetails?.operational_cycle !== 2" variant="primary"
        label="Matricular usuários" @click="addNewUser" />
    </div>

    <!-- Filtros -->
    <div class="filters-section mb-3">
      <div class="row">
        <!-- Filtro por Nome -->
        <div class="col-md-4">
          <div class="filter-input-container position-relative">
            <label for="name-filter" class="form-label text-muted small">Filtrar por nome</label>
            <input
              id="name-filter"
              type="text"
              class="form-control"
              placeholder="Buscar..."
              v-model="nameSearchInput"
              @input="handleNameInput"
              @focus="showNameDropdown = nameOptions.length > 0"
            />
            <!-- Dropdown de opções de nome -->
            <div
              v-if="showNameDropdown && nameOptions.length > 0"
              class="dropdown-menu show position-absolute w-100"
              style="top: 100%; z-index: 1000; max-height: 200px; overflow-y: auto;"
            >
              <button
                v-for="option in nameOptions"
                :key="option.id"
                type="button"
                class="dropdown-item"
                @click="selectNameOption(option)"
              >
                {{ option.label }}
              </button>
            </div>
          </div>
        </div>

        <!-- Filtro por CPF -->
        <div class="col-md-4">
          <div class="filter-input-container position-relative">
            <label for="cpf-filter" class="form-label text-muted small">Filtrar por CPF</label>
            <input
              id="cpf-filter"
              type="text"
              class="form-control"
              placeholder="Buscar..."
              v-model="cpfSearchInput"
              @input="handleCpfInput"
              @focus="showCpfDropdown = cpfOptions.length > 0"
            />
            <!-- Dropdown de opções de CPF -->
            <div
              v-if="showCpfDropdown && cpfOptions.length > 0"
              class="dropdown-menu show position-absolute w-100"
              style="top: 100%; z-index: 1000; max-height: 200px; overflow-y: auto;"
            >
              <button
                v-for="option in cpfOptions"
                :key="option.id"
                type="button"
                class="dropdown-item"
                @click="selectCpfOption(option)"
              >
                {{ option.label }}
              </button>
            </div>
          </div>
        </div>

        <!-- Filtro por E-mail -->
        <div class="col-md-4">
          <div class="filter-input-container position-relative">
            <label for="email-filter" class="form-label text-muted small">Filtrar por E-mail</label>
            <input
              id="email-filter"
              type="text"
              class="form-control"
              placeholder="Buscar..."
              v-model="emailSearchInput"
              @input="handleEmailInput"
              @focus="showEmailDropdown = emailOptions.length > 0"
            />
            <!-- Dropdown de opções de email -->
            <div
              v-if="showEmailDropdown && emailOptions.length > 0"
              class="dropdown-menu show position-absolute w-100"
              style="top: 100%; z-index: 1000; max-height: 200px; overflow-y: auto;"
            >
              <button
                v-for="option in emailOptions"
                :key="option.id"
                type="button"
                class="dropdown-item"
                @click="selectEmailOption(option)"
              >
                {{ option.label }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <FilterTags>
      <!-- Tags para usuários selecionados no filtro -->
      <FilterTag v-for="user in filteredUsers" :key="user.id"
        @remove="removeFilter(user.id || user.value)">
        {{ user.label }}
      </FilterTag>
    </FilterTags>

    <!-- Mensagem de Erro -->
    <div class="alert alert-danger" v-if="error">
      <i class="fas fa-exclamation-circle"></i>
      {{ error }}
    </div>

    <!-- Tabela -->
    <div class="table-container">
      <CustomTable :headers="tableHeaders" :items="enrolments" :sort-by="sortBy" :sort-desc="sortDesc"
        @sort="handleTableSort">
        <template #header-select>
          <div class="checkbox-container">
            <input type="checkbox" :checked="allSelected" :indeterminate="someSelected && !allSelected"
              @change="toggleSelectAll" class="custom-checkbox" />
          </div>
        </template>
        <template #item-select="{ item }">
          <div class="checkbox-container">
            <input type="checkbox" :checked="isSelected(item.id)" @change="toggleSelectUser(item.id)"
              class="custom-checkbox" />
          </div>
        </template>
        <template #item-fullName="{ item }">
          <a class="user-name-container" :href="`/user/view.php?id=${item.id}`"
            :title="'Ver perfil de ' + item.fullName">
            <UserAvatar :full-name="item.fullName" :size="36" />
            <span class="user-name-link">{{ item.fullName }}</span>
          </a>
        </template>
        <template #item-email="{ item }">
          {{ item.email }}
        </template>
        <template #item-cpf="{ item }">
          {{ item.cpf }}
        </template>
        <template #item-roles="{ item }">
          <RoleSelector :userId="item.id" :offeruserenrolid="item.offeruserenrolid" :currentRole="item.roles"
            :offerclassid="offerclassid" @success="handleRoleUpdateSuccess" @error="handleRoleUpdateError"
            @reload-table="reloadTable" />
        </template>
        <template #item-groups="{ item }">
          {{ item.groups }}
        </template>
        <template #item-startDate="{ item }">
          {{ item.startDate }}
        </template>
        <template #item-endDate="{ item }">
          {{ item.endDate }}
        </template>
        <template #item-deadline="{ item }">
          {{ item.deadline }}
        </template>
        <template #item-progress="{ item }">
          <div class="progress-container">
            <div class="progress-bar" :style="{ width: item.progress }"></div>
            <span class="progress-text">{{ item.progress }}</span>
          </div>
        </template>
        <template #item-situation="{ item }">
          {{ item.situationName }}
        </template>
        <template #item-grade="{ item }">
          {{ item.grade }}
        </template>
        <template #item-status="{ item }">
          <div class="status-container">
            <span class="status-tag badge" :class="item.status === 0 ? 'badge-success' : 'badge-danger'">
              {{ item.statusName }}
            </span>

            <div class="status-actions">
              <button class="btn-information" @click="showEnrollmentDetails(item)" title="Informações da matrícula">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                  class="custom-icon">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="16" x2="12" y2="12"></line>
                  <line x1="12" y1="8" x2="12.01" y2="8"></line>
                </svg>
              </button>
              <button class="btn-settings" @click="editUser(item)" title="Editar matrícula">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"
                  class="custom-icon">
                  <circle cx="12" cy="12" r="3"></circle>
                  <path
                    d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z">
                  </path>
                </svg>
              </button>
            </div>
          </div>
        </template>
      </CustomTable>
    </div>

    <!-- Paginação -->
    <Pagination v-model:current-page="currentPage" v-model:per-page="perPage" :total="totalEnrolments"
      :loading="loading" />

    <!-- Ações para usuários selecionados - sempre visível -->
    <div class="selected-users-actions">
      <div class="bulk-actions-container">
        <label for="bulk-actions">Com usuários selecionados...</label>
        <select id="bulk-actions" class="form-control bulk-select" v-model="selectedBulkAction"
          @change="handleBulkAction">
          <option value="">Escolher...</option>
          <optgroup label="Comunicação">
            <option value="message">Enviar uma mensagem</option>
            <option value="note">Escrever uma nova anotação</option>
          </optgroup>
          <optgroup label="Baixar dados da tabela como:">
            <option value="download_csv">
              Valores separados por vírgula (.csv)
            </option>
            <option value="download_xlsx">Microsoft excel (.xlsx)</option>
            <option value="download_html">Tabela HTML</option>
            <option value="download_json">
              JavaScript Object Notation (.json)
            </option>
            <option value="download_ods">OpenDocument (.ods)</option>
            <option value="download_pdf">
              Formato de documento portável (.pdf)
            </option>
          </optgroup>
          <optgroup label="Inscrições">
            <option value="edit_enrolment">
              Editar matrículas de usuários selecionados
            </option>
            <option value="delete_enrolment">
              Excluir matrículas de usuários selecionados
            </option>
          </optgroup>
        </select>
      </div>
    </div>

    <!-- Botão de Matricular novo usuário abaixo do select -->
    <div class="bottom-enroll-button" v-if="!classDetails || classDetails?.operational_cycle !== 2">
      <CustomButton variant="primary" label="Matricular usuários" @click="addNewUser" />
    </div>

    <!-- Modal de Detalhes da Matrícula -->
    <EnrollmentDetailsModal :show="showEnrollmentModal" :user="selectedUser"
      :course-name="classDetails?.course_fullname || ''" @close="closeEnrollmentModal" />

    <!-- Modal de Matrícula de Usuários -->
    <EnrolmentModalNew :show="showEnrolmentModal" :offerclassid="offerclassid" :roles="roleOptions"
      @close="closeEnrolmentModal" @success="handleEnrolmentSuccess" />

    <EditEnrollmentModal :show="showEditEnrollmentModal" :user="selectedUser" :offerclassid="offerclassid"
      @close="closeEditEnrollmentModal" @success="handleEditEnrollmentSuccess" @error="handleEditEnrollmentError" />

    <!-- Modal de Edição de Matrícula -->
    <BulkEditEnrollmentModal :show="showBulkEditEnrollmentModal" :users="selectedUsers
      .map((id) => enrolments.find((offer) => offer.id === id))
      .filter(Boolean)
      " :offerclassid="offerclassid" @close="this.showBulkEditEnrollmentModal = false"
      @success="handleBulkEditEnrollmentSuccess" @error="handleBulkEditEnrollmentError" />

    <BulkDeleteEnrollmentModal :show="showBulkDeleteEnrollmentModal" :users="selectedUsers
      .map((id) => enrolments.find((offer) => offer.id === id))
      .filter(Boolean)
      " :offerclassid="offerclassid" @close="showBulkDeleteEnrollmentModal = false"
      @confirm="confirmeBulkDeleteEnrollment" @error="handleBulkDeleteEnrollmentError" />

    <LFLoading :is-loading="loading" />

    <!-- Toast para mensagens -->
    <Toast :show="showToast" :message="toastMessage" :type="toastType" :duration="3000" />
  </div>
</template>

<script>

import {
  fetchEnrolments,
  deleteEnrolmentBulk,
  searchEnrolledUsers,
} from "@/services/enrolment";

import {
  getClass,
  getCourseRoles,
} from "@/services/offer";

import Toast from "@/components/Toast.vue";

// Importação dos componentes
import CustomTable from "@/components/CustomTable.vue";
import CustomSelect from "@/components/CustomSelect.vue";
import HierarchicalSelect from "@/components/HierarchicalSelect.vue";
import CustomInput from "@/components/CustomInput.vue";
import CustomCheckbox from "@/components/CustomCheckbox.vue";
import CustomButton from "@/components/CustomButton.vue";
import FilterSection from "@/components/FilterSection.vue";
import FilterRow from "@/components/FilterRow.vue";
import FilterGroup from "@/components/FilterGroup.vue";
import FilterActions from "@/components/FilterActions.vue";
import FilterTag from "@/components/FilterTag.vue";
import FilterTags from "@/components/FilterTags.vue";
import Pagination from "@/components/Pagination.vue";
import PageHeader from "@/components/PageHeader.vue";
import ConfirmationModal from "@/components/ConfirmationModal.vue";
import Autocomplete from "@/components/Autocomplete.vue";
import EnrolmentModalNew from "@/components/EnrolmentModalNew.vue";
import EnrollmentDetailsModal from "@/components/EnrollmentDetailsModal.vue";
import EditEnrollmentModal from "@/components/EditEnrollmentModal.vue";
import BulkEditEnrollmentModal from "@/components/BulkEditEnrollmentModal.vue";
import BulkDeleteEnrollmentModal from "@/components/BulkDeleteEnrollmentModal.vue";
import BackButton from "@/components/BackButton.vue";
import UserAvatar from "@/components/UserAvatar.vue";
import RoleSelector from "@/components/RoleSelector.vue";
import LFLoading from "@/components/LFLoading.vue";
import { useRouter } from "vue-router";

export default {
  name: "RegisteredUsers",

  components: {
    CustomTable,
    CustomSelect,
    HierarchicalSelect,
    CustomInput,
    CustomCheckbox,
    CustomButton,
    FilterSection,
    FilterRow,
    FilterGroup,
    FilterActions,
    FilterTag,
    FilterTags,
    Pagination,
    PageHeader,
    ConfirmationModal,
    Autocomplete,
    EnrolmentModalNew,
    EnrollmentDetailsModal,
    Toast,
    EditEnrollmentModal,
    BulkEditEnrollmentModal,
    BulkDeleteEnrollmentModal,
    BackButton,
    UserAvatar,
    RoleSelector,
    LFLoading,
  },

  data() {
    return {
      offerid: null,
      offerclassid: null,
      offercourseid: null,
      courseid: null,
      courseContextId: null,

      filteredUsers: [],
      nameOptions: [],
      cpfOptions: [],
      emailOptions: [],

      // Estados dos inputs de filtro
      nameSearchInput: '',
      cpfSearchInput: '',
      emailSearchInput: '',

      // Estados dos dropdowns
      showNameDropdown: false,
      showCpfDropdown: false,
      showEmailDropdown: false,

      // Timers para debounce
      nameDebounceTimer: null,
      cpfDebounceTimer: null,
      emailDebounceTimer: null,

      tableHeaders: [
        { text: "", value: "select", sortable: false, width: "50px" },
        {
          text: "NOME/SOBRENOME",
          value: "fullName",
          sortable: true,
          width: "220px",
        },
        { text: "E-MAIL", value: "email", sortable: true },
        { text: "CPF", value: "cpf", sortable: true },
        { text: "PAPÉIS", value: "roles", sortable: false },
        { text: "GRUPOS", value: "groups", sortable: false },
        {
          text: "DATA INÍCIO DA MATRÍCULA",
          value: "startDate",
          sortable: true,
        },
        { text: "DATA FIM DA MATRÍCULA", value: "endDate", sortable: true },
        { text: "PRAZO DE CONCLUSÃO", value: "deadline", sortable: true },
        { text: "PROGRESSO", value: "progress", sortable: false },
        { text: "SITUAÇÃO DE MATRÍCULA", value: "situation", sortable: true },
        { text: "NOTA", value: "grade", sortable: false },
        { text: "ESTADO", value: "status", sortable: true },
      ],

      enrolments: [],
      totalEnrolments: 0,
      loading: false,
      error: null,

      currentPage: 1,
      perPage: 10,

      sortBy: "fullName",
      sortDesc: false,

      showBulkDeleteEnrollmentModal: false,

      showEnrollmentModal: false,
      selectedUser: null,

      showEnrolmentModal: false,
      roleOptions: [],

      showEditEnrollmentModal: false,

      showBulkEditEnrollmentModal: false,

      showToast: false,
      toastMessage: "",
      toastType: "success",
      toastTimeout: null,

      classDetails: {},

      selectedUsers: [],
      selectedBulkAction: "",

      selectedPageView: "usuarios_matriculados",
      pageViewOptions: [
        {
          value: "matriculas",
          label: "Matrículas",
          children: [
            { value: "usuarios_matriculados", label: "Usuários matriculados" },
          ],
        },
        {
          value: "grupos",
          label: "Grupos",
          children: [
            { value: "grupos", label: "Grupos" },
            { value: "agrupamentos", label: "Agrupamentos" },
            { value: "visao_geral", label: "Visão geral" },
          ],
        },
        {
          value: "permissoes",
          label: "Permissões",
          children: [
            { value: "permissoes", label: "Permissões" },
            { value: "outros_usuarios", label: "Outros usuários" },
            { value: "verificar_permissoes", label: "Verificar permissões" },
          ],
        },
      ],
    };
  },

  setup() {
    const router = useRouter();
    return { router };
  },

  async created() {
    this.offerclassid = this.offerclassid ?? this.$route.params.offerclassid

    if (!this.offerclassid) {
      throw new Error('ID da turma não foi definido.');
    }

    this.offerclassid = parseInt(this.offerclassid)

    const response = await getClass(this.offerclassid);

    if (response.error) {
      throw new Error('Erro ao requisitar informações da turma');
    }

    this.classDetails = response.data;
    this.offerid = parseInt(this.classDetails?.offerid)
    this.offercourseid = parseInt(this.classDetails?.offercourseid)
    this.corseid = this.classDetails?.courseid
    this.courseContextId = this.classDetails?.course_context_id

    await this.loadRoles();
    await this.loadRegisteredUsers();
  },

  mounted() {
    // Adicionar event listener para fechar dropdowns ao clicar fora
    document.addEventListener('click', this.handleDocumentClick);
  },

  beforeUnmount() {
    // Remover event listener
    document.removeEventListener('click', this.handleDocumentClick);

    // Limpar timers
    if (this.nameDebounceTimer) clearTimeout(this.nameDebounceTimer);
    if (this.cpfDebounceTimer) clearTimeout(this.cpfDebounceTimer);
    if (this.emailDebounceTimer) clearTimeout(this.emailDebounceTimer);
  },

  computed: {
    allSelected() {
      return (
        this.enrolments.length > 0 &&
        this.selectedUsers.length === this.enrolments.length
      );
    },

    someSelected() {
      return this.selectedUsers.length > 0 && !this.allSelected;
    },

    excludedUserIds() {
      return this.filteredUsers.map(user => user.id || user.value)
    }
  },

  watch: {
    perPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentPage = 1;
        this.selectedUsers = [];

        this.loadRegisteredUsers();
      }
    },
    currentPage(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.loadRegisteredUsers();
      }
    }
  },

  methods: {
    async loadRegisteredUsers() {

      this.loading = true;
      this.error = null;

      let userids = [];

      if (this.filteredUsers.length > 0) {
        userids = this.excludedUserIds;
      }

      const params = {
        offerclassid: this.offerclassid,
        userids: userids,
        page: this.currentPage,
        perpage: this.perPage,
        orderby: this.mapSortFieldToBackend(this.sortBy || "fullName"),
        direction: this.sortDesc ? "DESC" : "ASC",
      };

      const response = await fetchEnrolments(params);

      if (response.data) {
        const responseData = response.data;

        if (Array.isArray(responseData.enrolments)) {
          const deadline = await this.calculateDeadline();

          this.enrolments = responseData.enrolments.map((enrolment) => ({
            id: enrolment.userid,
            offeruserenrolid: enrolment.offeruserenrolid,
            fullName: enrolment.fullname,
            email: enrolment.email,
            cpf: enrolment.cpf,
            enrol: enrolment.enrol,
            roles: this.formatRoles(enrolment.roles),
            groups: enrolment.groups,
            timecreated: enrolment.timecreated,
            createdDate: this.formatDateTime(enrolment.timecreated),
            timestart: enrolment.timestart,
            timeend: enrolment.timeend,
            startDate: this.formatDate(enrolment.timestart),
            endDate: this.formatDate(enrolment.timeend),
            deadline:
              deadline == null
                ? "Imilitado"
                : deadline === 1
                  ? "1 dia"
                  : `${deadline} dias`,
            progress: this.formatProgress(enrolment.progress),
            situation: enrolment.situation,
            situationName: enrolment.situation_name,
            grade: enrolment.grade || "-",
            status: enrolment.status,
            statusName:
              enrolment.status !== undefined
                ? enrolment.status === 0
                  ? "Ativo"
                  : "Suspenso"
                : "-",
          }));

          this.totalEnrolments = responseData.total || this.enrolments.length;
        }
      } else {
        this.enrolments = [];
        this.totalEnrolments = 0;
      }
      this.loading = false;
    },

    // Métodos auxiliares para formatar os dados
    formatDate(timestamp) {
      if (!timestamp || timestamp === 0) return "-";
      const date = new Date(timestamp * 1000);
      return date.toLocaleDateString("pt-BR");
    },

    formatDateTime(timestamp, options = {}) {
      if (!timestamp || timestamp === 0) return "-";

      if (Object.keys(options).length === 0) {
        options = {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
          hour: "2-digit",
          minute: "2-digit",
        };
      }
      const date = new Date(timestamp * 1000);
      return date.toLocaleString("pt-BR", options);
    },

    async calculateDeadline() {

      const optionalFields = this.classDetails.optional_fields;
      const classEnrolPeriod = optionalFields.enrolperiod;
      const enableEnrolPeriod = optionalFields.enableenrolperiod;
      const enableEndDate = optionalFields.enableenddate;
      const startDateStr = this.classDetails.startdate;
      const endDateStr = optionalFields.enddate;

      let enrolPeriod;

      if (!enableEndDate && !enableEnrolPeriod) {
        return null;
      }

      if (classEnrolPeriod === 0 && enableEnrolPeriod === false) {
        if (startDateStr && endDateStr) {
          const startDate = new Date(startDateStr);
          const endDate = new Date(endDateStr);

          const diffTime = endDate - startDate;

          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          enrolPeriod = diffDays;
        } else {
          enrolPeriod = null;
        }
      } else {
        enrolPeriod = classEnrolPeriod;
      }

      return enrolPeriod;
    },

    formatProgress(progress) {
      if (progress === null || progress === undefined) return "-";
      return Math.round(progress) + "%";
    },

    // Método para formatar os papéis (roles)
    formatRoles(roles) {
      if (!roles || roles === "-") return "-";

      // Se for uma string, dividir por vírgula
      if (typeof roles === "string") {
        return roles
          .split(",")
          .join(", ");
      }

      // Se for um array de objetos com propriedade name (formato da API)
      if (
        Array.isArray(roles) &&
        roles.length > 0 &&
        typeof roles[0] === "object" &&
        roles[0].name
      ) {
        return roles.map((role) => role.name).join(", ");
      }

      // Se for um array de outros tipos
      if (Array.isArray(roles)) {
        return roles
          .join(", ");
      }

      // Para qualquer outro caso, retornar um valor padrão
      return "-";
    },

    // Métodos para carregar opções dos autocompletes
    async loadNameOptions(searchString) {
      if (!searchString || searchString.length < 3) {
        this.nameOptions = [];
        this.showNameDropdown = false;
        return;
      }

      try {
        const response = await searchEnrolledUsers({
          offerclassid: this.offerclassid,
          fieldstring: "name",
          searchstring: searchString,
          excludeduserids: this.excludedUserIds,
        });

        if (response.data) {
          console.log(response.data)
          this.nameOptions = response.data.map((user) => ({
            id: user.id,
            value: user.id,
            label: user.fullname,
          }));
          this.showNameDropdown = this.nameOptions.length > 0;
        } else {
          this.nameOptions = [];
          this.showNameDropdown = false;
        }
      } catch (error) {
        this.nameOptions = [];
        this.showNameDropdown = false;
      }
    },

    async loadCpfOptions(searchString) {
      if (!searchString || searchString.length < 3) {
        this.cpfOptions = [];
        this.showCpfDropdown = false;
        return;
      }

      try {
        const response = await searchEnrolledUsers({
          offerclassid: this.offerclassid,
          fieldstring: "username",
          searchstring: searchString,
          excludeduserids: this.excludedUserIds,
        });

        if (response.data) {
          this.cpfOptions = response.data.map((user) => ({
            id: user.id,
            value: user.id,
            label: user.fullname,
          }));
          this.showCpfDropdown = this.cpfOptions.length > 0;
        } else {
          this.cpfOptions = [];
          this.showCpfDropdown = false;
        }
      } catch (error) {
        this.cpfOptions = [];
        this.showCpfDropdown = false;
      }
    },

    async loadEmailOptions(searchString) {
      if (!searchString || searchString.length < 3) {
        this.emailOptions = [];
        this.showEmailDropdown = false;
        return;
      }

      try {
        const response = await searchEnrolledUsers({
          offerclassid: this.offerclassid,
          fieldstring: "email",
          searchstring: searchString,
          excludeduserids: this.excludedUserIds,
        });

        if (!response.error && response.data) {
          this.emailOptions = response.data.map((user) => ({
            id: user.id,
            value: user.id,
            label: user.fullname,
          }));
          this.showEmailDropdown = this.emailOptions.length > 0;
        } else {
          this.emailOptions = [];
          this.showEmailDropdown = false;
        }
      } catch (error) {
        this.emailOptions = [];
        this.showEmailDropdown = false;
      }
    },

    // Métodos para controlar os inputs de filtro
    handleNameInput() {
      if (this.nameDebounceTimer) {
        clearTimeout(this.nameDebounceTimer);
      }

      this.clearAllFilterOptions();

      if (this.nameSearchInput.length >= 3) {
        this.nameDebounceTimer = setTimeout(() => {
          this.loadNameOptions(this.nameSearchInput);
        }, 500);
      } else {
        this.showNameDropdown = false;
      }
    },

    handleCpfInput() {
      if (this.cpfDebounceTimer) {
        clearTimeout(this.cpfDebounceTimer);
      }

      this.clearAllFilterOptions();

      if (this.cpfSearchInput.length >= 3) {
        this.cpfDebounceTimer = setTimeout(() => {
          this.loadCpfOptions(this.cpfSearchInput);
        }, 500);
      } else {
        this.showCpfDropdown = false;
      }
    },

    handleEmailInput() {
      if (this.emailDebounceTimer) {
        clearTimeout(this.emailDebounceTimer);
      }

      this.clearAllFilterOptions();

      if (this.emailSearchInput.length >= 3) {
        this.emailDebounceTimer = setTimeout(() => {
          this.loadEmailOptions(this.emailSearchInput);
        }, 500);
      } else {
        this.showEmailDropdown = false;
      }
    },

    // Métodos para selecionar opções dos dropdowns
    selectNameOption(option) {
      this.filteredUsers.push({
        id: option.id,
        value: option.value,
        label: option.label,
        type: 'name'
      });
      this.nameSearchInput = '';
      this.clearAllFilterOptions();
      this.showNameDropdown = false;
      this.loadRegisteredUsers();
    },

    selectCpfOption(option) {
      this.filteredUsers.push({
        id: option.id,
        value: option.value,
        label: option.label,
        type: 'cpf'
      });
      this.cpfSearchInput = '';
      this.clearAllFilterOptions();
      this.showCpfDropdown = false;
      this.loadRegisteredUsers();
    },

    selectEmailOption(option) {
      this.filteredUsers.push({
        id: option.id,
        value: option.value,
        label: option.label,
        type: 'email'
      });
      this.emailSearchInput = '';
      this.clearAllFilterOptions();
      this.showEmailDropdown = false;
      this.loadRegisteredUsers();
    },

    // Métodos para fechar dropdowns ao clicar fora
    closeDropdowns() {
      this.showNameDropdown = false;
      this.showCpfDropdown = false;
      this.showEmailDropdown = false;
    },

    clearAllFilterOptions() {
      this.nameOptions = [];
      this.cpfOptions = [];
      this.emailOptions = [];
    },

    handleDocumentClick(event) {
      const filterContainers = document.querySelectorAll('.filter-input-container');
      let clickedOutside = true;

      filterContainers.forEach(container => {
        if (container.contains(event.target)) {
          clickedOutside = false;
        }
      });

      if (clickedOutside) {
        this.closeDropdowns();
      }
    },

    /**
     * Remove um usuário da lista de filtros baseado no índice ou ID do usuário
     * @param {number|string} indexOrUserId - Índice do usuário na lista filteredUsers ou ID do usuário
     */
    removeFilter(userid) {
      const userIndex = this.filteredUsers.findIndex(user =>
        user.id === userid || user.value === userid
      );

      if (userIndex !== -1) {
        this.filteredUsers.splice(userIndex, 1);
      }
      this.loadRegisteredUsers()
    },

    // Métodos para manipular a seleção de usuários
    toggleSelectAll() {
      if (this.allSelected) {
        this.selectedUsers = [];
      } else {
        this.selectedUsers = this.enrolments.map((user) => user.id);
      }
    },

    toggleSelectUser(userId) {
      const index = this.selectedUsers.indexOf(userId);
      if (index === -1) {
        this.selectedUsers.push(userId);
      } else {
        this.selectedUsers.splice(index, 1);
      }
    },

    isSelected(userId) {
      return this.selectedUsers.includes(userId);
    },

    async handleTableSort({ sortBy, sortDesc }) {
      this.sortBy = sortBy;
      this.sortDesc = sortDesc;

      await this.loadRegisteredUsers();
    },

    mapSortFieldToBackend(frontendField) {
      const fieldMapping = {
        'fullName': 'fullname',
        'email': 'email',
        'cpf': 'cpf',
        'startDate': 'startdate',
        'endDate': 'enddate',
        'deadline': 'enrolperiod',
        'situation': 'situation',
        'status': 'status'
      };

      return fieldMapping[frontendField] || "fullname";
    },

    addNewUser() {

      if (this.classDetails && this.classDetails?.operational_cycle === 2) {
        this.error =
          "Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";
        return;
      }

      this.showEnrolmentModal = true;
    },

    closeEnrolmentModal() {
      this.showEnrolmentModal = false;
    },

    async goBack() {
      this.router.push({
        name: "editar-oferta",
        params: { id: this.offerid },
      });
    },

    // Método para redirecionar para o perfil do usuário
    viewUserProfile(userId) {
      if (!userId) {
        return;
      }

      // Construir a URL para o perfil do usuário
      const url = `/user/view.php?id=${userId}&course=${this.courseid}`;

      // Redirecionar para a URL
      window.location.href = url;
    },

    // Método para lidar com a mudança de visualização da página
    async handlePageViewChange(value) {

      let offerclassid = this.offerclassid;
      let courseId = this.courseid;
      let courseContextId = this.courseContextId;

      // Mapear os valores para as rotas correspondentes
      const routeMap = {
        usuarios_matriculados: `/local/offermanager/new-subscribed-users/${offerclassid}`,
        grupos: `/group/index.php?id=${courseId}`,
        agrupamentos: `/group/groupings.php?id=${courseId}`,
        visao_geral: `/user/index.php?id=${courseId}`,
        permissoes: `/admin/roles/permissions.php?contextid=${courseContextId}`,
        outros_usuarios: `/enrol/otherusers.php?id=${courseId}`,
        verificar_permissoes: `/admin/roles/check.php?contextid=${courseContextId}`,
      };

      if (routeMap[value]) {
        window.location.href = routeMap[value];
      }
    },

    async handleEnrolmentSuccess() {
      await this.loadRegisteredUsers();
    },

    async loadRoles() {
      const response = await getCourseRoles(this.offercourseid);

      if (response.error) {
        throw new Error('Erro ao requisitar papéis do curso');
      }

      this.roleOptions = response.data.map((role) => ({
        value: role.id,
        label: role.name,
      }));
    },

    showEnrollmentDetails(user) {
      // Formatar os dados do usuário para o modal de detalhes da matrícula
      this.selectedUser = {
        fullName: user.fullName,
        enrol: user.enrol || "Inscrições manuais",
        status: user.status || 0,
        statusName: user.statusName || "Ativo",
        startDate: user.startDate || "Não disponível",
        createdDate: user.createdDate || user.startDate || "Não disponível",
      };

      // Mostrar o modal
      this.showEnrollmentModal = true;
    },

    closeEnrollmentModal() {
      this.showEnrollmentModal = false;
      this.selectedUser = null;
    },

    closeEditEnrollmentModal() {
      this.showEditEnrollmentModal = false;
      this.selectedUser = null;
    },

    async handleEditEnrollmentSuccess(data) {
      // Mostrar mensagem de sucesso
      this.showSuccessMessage("Matrícula editada com sucesso.");

      // Se temos o ID do papel, atualizar o nome do papel na tabela
      if (data.roleid) {
        // Encontrar o nome do papel correspondente ao ID
        let roleName = null;

        // Verificar se temos o roleOptions disponível
        if (this.roleOptions && this.roleOptions.length > 0) {
          const role = this.roleOptions.find(
            (r) => r.value === String(data.roleid)
          );
          if (role) {
            roleName = role.name;
          }
        }

        // Se não conseguimos obter o nome do papel, não atualizamos esse campo
        if (!roleName) {
          await this.loadRegisteredUsers();
          this.showEditEnrollmentModal = false;
          this.selectedUser = null;
          return;
        }

        // Atualizar o papel do usuário na tabela
        const userIndex = this.enrolments.findIndex(
          (user) => user.id === data.userId
        );
        if (userIndex !== -1) {
          // Atualizar o papel e o estado do usuário na tabela apenas se temos os valores
          if (roleName) {
            this.enrolments[userIndex].roles = roleName;
          }

          if (data.status !== undefined) {
            this.enrolments[userIndex].status = data.status;
            // Atualizar o nome do estado apenas se temos o status
            if (data.status === 1) {
              this.enrolments[userIndex].statusName = "Ativo";
            } else if (data.status === 0) {
              this.enrolments[userIndex].statusName = "Suspenso";
            }
          }

          // Se temos timestamps de início e fim, atualizar também
          if (data.timestart) {
            const startDate = new Date(data.timestart * 1000);
            this.enrolments[userIndex].startDate =
              startDate.toLocaleDateString("pt-BR");
          }

          if (data.timeend) {
            const endDate = new Date(data.timeend * 1000);
            this.enrolments[userIndex].endDate =
              endDate.toLocaleDateString("pt-BR");
          }
        } else {

          await this.loadRegisteredUsers();
        }
      } else {
        await this.loadRegisteredUsers();
      }

      this.showEditEnrollmentModal = false;
      this.selectedUser = null;
    },

    handleEditEnrollmentError(errorMessage) {
      // Mostrar mensagem de erro
      this.showErrorMessage(
        errorMessage ||
        "Não foi possível editar a matrícula. Por favor, tente novamente."
      );

      // Não fechar o modal para permitir que o usuário tente novamente
    },

    /**
     * Manipula o sucesso na atualização do papel do usuário
     * @param {Object} data Dados da atualização
     */
    handleRoleUpdateSuccess(data) {
      // Mostrar mensagem de sucesso
      this.showSuccessMessage("Papel atualizado com sucesso.");

      // Atualizar o papel do usuário na tabela
      const userIndex = this.enrolments.findIndex(
        (user) => user.id === data.userId
      );
      if (userIndex !== -1) {
        // Atualizar o papel do usuário na tabela
        this.enrolments[userIndex].roles = data.roleName;
      } else {
        // Se não encontramos o usuário na tabela, recarregar a lista completa
        this.reloadTable();
      }
    },

    /**
     * Manipula o erro na atualização do papel do usuário
     * @param {string} message Mensagem de erro
     */
    handleRoleUpdateError(message) {
      this.showErrorMessage(
        message || "Ocorreu um erro ao atualizar o papel do usuário."
      );
    },

    /**
     * Recarrega a tabela de usuários matriculados
     */
    reloadTable() {
      this.loadRegisteredUsers();
    },

    editUser(user) {
      // Extrair o ID do papel a partir do nome do papel
      let roleid = null;
      if (user.roles) {
        // Tentar obter o roleid a partir dos dados da API
        if (user.roleid) {
          roleid = user.roleid;
        }
      }

      // Preparar os dados do usuário para o modal de edição
      this.selectedUser = {
        id: user.id, // ID do usuário
        offeruserenrolid: user.offeruserenrolid, // ID da matrícula (importante para edição)
        fullName: user.fullName,
        enrol: user.enrol,
        status: user.status,
        statusName: user.statusName,
        roles: user.roles, // Nome do papel
        roleid: roleid, // ID do papel (sem valor padrão)
        startDate: user.startDate,
        timestart: user.timestart,
        timeend: user.timeend,
        createdDate: user.createdDate || "-",
      };

      // Mostrar o modal de edição
      this.showEditEnrollmentModal = true;
    },

    async confirmeBulkDeleteEnrollment() {
      this.loading = true;

      const offeruserenrolids = [];

      for (const userId of this.selectedUsers) {
        const user = this.enrolments.find((offer) => offer.id === userId);
        if (user && user.offeruserenrolid) {
          offeruserenrolids.push(user.offeruserenrolid);
        }
      }

      if (offeruserenrolids.length === 0) {
        this.showErrorMessage(
          "Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."
        );
        this.loading = false;
        return;
      }

      const processingMessage = `Processando exclusão de ${offeruserenrolids.length} matrícula(s)...`;
      this.showSuccessMessage(processingMessage);

      const results = await deleteEnrolmentBulk(offeruserenrolids);

      if (results && results.length > 0) {
        const successCount = results.filter(
          (result) => result.operation_status
        ).length;
        const failCount = results.length - successCount;

        if (successCount > 0) {
          this.showSuccessMessage(
            `${successCount} matrícula(s) cancelada(s) com sucesso.${failCount > 0 ? ` ${failCount} matrícula(s) não puderam ser canceladas.` : ""}`
          );

          await this.loadRegisteredUsers();

          this.selectedUsers = [];
        } else {
          this.showErrorMessage(
            "Não foi possível cancelar as matrículas. Por favor, tente novamente."
          );
        }
      } else {

        this.showSuccessMessage(
          `${offeruserenrolids.length} matrícula(s) cancelada(s) com sucesso.`
        );

        await this.loadRegisteredUsers();

        // Limpar a seleção de usuários
        this.selectedUsers = [];
      }

      this.showBulkDeleteEnrollmentModal = false;
      this.loading = false;
    },

    handleBulkAction() {
      if (!this.selectedBulkAction) return;

      // Verificar se temos usuários selecionados
      if (this.selectedUsers.length === 0) {
        this.showWarningMessage(
          "Por favor, selecione pelo menos um usuário para realizar esta ação."
        );
        this.selectedBulkAction = "";
        return;
      }

      switch (this.selectedBulkAction) {
        case "message":
          this.sendMessage();
          break;
        case "note":
          this.writeNote();
          break;
        case "download_csv":
          this.downloadData("csv");
          break;
        case "download_xlsx":
          this.downloadData("xlsx");
          break;
        case "download_html":
          this.downloadData("html");
          break;
        case "download_json":
          this.downloadData("json");
          break;
        case "download_ods":
          this.downloadData("ods");
          break;
        case "download_pdf":
          this.downloadData("pdf");
          break;
        case "edit_enrolment":
          this.editEnrolments();
          break;
        case "delete_enrolment":
          this.bulkDeleteEnrollment();
          break;
      }

      // Reset the select after action
      this.selectedBulkAction = "";
    },

    sendMessage() {

      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showErrorMessage(
          "Por favor, selecione pelo menos um usuário para enviar mensagem."
        );
        return;
      }

      this.showSendMessageModal(this.selectedUsers);
    },

    /**
     * Mostra o modal de envio de mensagens usando a API do Moodle
     *
     * @param {Array} userIds - IDs dos usuários para enviar mensagem
     */
    showSendMessageModal(userIds) {
      if (typeof window.require !== "function") {
        this.showErrorMessage(
          "Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde."
        );
        return;
      }

      // Usar o módulo core_message/message_send_bulk do Moodle para mostrar o modal
      window.require(
        ["core_message/message_send_bulk"],
        (BulkSender) => {
          if (typeof BulkSender.showModal !== "function") {
            this.showErrorMessage(
              "Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde."
            );
            return;
          }

          // Mostrar o modal de mensagens
          BulkSender.showModal(userIds, () => {
            // Resetar o select após o envio da mensagem
            this.selectedBulkAction = "";
          });
        },
        (error) => {
          this.showErrorMessage(
            "Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde."
          );
        }
      );
    },

    writeNote() {
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showWarningMessage(
          "Por favor, selecione pelo menos um usuário para escrever anotação."
        );
        return;
      }

      this.showAddNoteModal(this.courseid, this.selectedUsers);
    },

    /**
     * Mostra o modal de adição de anotações usando a API do Moodle
     *
     * @param {Number} courseId - ID do curso
     * @param {Array} userIds - IDs dos usuários para adicionar anotação
     */
    showAddNoteModal(courseId, userIds) {
      // Verificar se o módulo necessário está disponível
      if (typeof window.require !== "function") {
        this.showErrorMessage(
          "Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde."
        );
        return;
      }

      // Usar o módulo core_user/local/participants/bulkactions do Moodle para mostrar o modal
      window.require(
        ["core_user/local/participants/bulkactions"],
        (BulkActions) => {
          if (typeof BulkActions.showAddNote !== "function") {
            this.showErrorMessage(
              "Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde."
            );
            return;
          }

          // Obter os nomes dos estados de anotação
          const noteStateNames = {
            personal: "Pessoal",
            course: "Curso",
            site: "Site",
          };

          // Mostrar o modal de anotações
          BulkActions.showAddNote(courseId, userIds, noteStateNames, "")
            .then((modal) => {
              modal.getRoot().on("hidden.bs.modal", () => {
                this.selectedBulkAction = "";
              });

              return modal;
            })
            .catch((error) => {
              this.showErrorMessage(
                "Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde."
              );
            });
        },
        (error) => {
          this.showErrorMessage(
            "Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde."
          );
        }
      );
    },

    downloadData(format) {
      if (this.selectedUsers.length === 0) return;

      this.prepareLocalDownload(format);
    },

    prepareLocalDownload(format) {

      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showWarningMessage("Nenhum usuário selecionado para download.");
        return;
      }

      const data = [];

      if (
        Array.isArray(this.selectedUsers) &&
        this.selectedUsers.every((id) => typeof id === "number")
      ) {
        for (const userId of this.selectedUsers) {
          const user = this.enrolments.find((offer) => offer.id === userId);
          if (user) {
            const userData = {
              ID: user.id || "",
              Nome: user.fullName || user.name || "",
              Email: user.email || "",
              CPF: user.cpf || "",
              Papéis: user.roles || "",
              Grupos: user.groups || "",
              "Data de Início": user.startDate || "",
              "Data de Término": user.endDate || "",
              Prazo: user.deadline || "",
              Progresso: user.progress || "",
              Situação: user.situationName || user.situation || "",
              Nota: user.grade || "",
              Estado: user.statusName || "",
            };

            data.push(userData);
          }
        }
      } else {
        for (const user of this.selectedUsers) {

          if (typeof user === "number") {
            const userObj = this.enrolments.find((offer) => offer.id === user);
            if (userObj) {
              const userData = {
                ID: userObj.id || "",
                Nome: userObj.fullName || userObj.name || "",
                Email: userObj.email || "",
                CPF: userObj.cpf || "",
                Papéis: userObj.roles || "",
                Grupos: userObj.groups || "",
                "Data de Início": userObj.startDate || "",
                "Data de Término": userObj.endDate || "",
                Prazo: userObj.deadline || "",
                Progresso: userObj.progress || "",
                Situação: userObj.situationName || userObj.situation || "",
                Nota: userObj.grade || "",
                Estado: userObj.statusName || "",
              };

              data.push(userData);
            }
          } else if (typeof user === "object" && user !== null) {

            const userData = {
              ID: user.id || "",
              Nome: user.fullName || user.name || "",
              Email: user.email || "",
              CPF: user.cpf || "",
              Papéis: user.roles || "",
              Grupos: user.groups || "",
              "Data de Início": user.startDate || "",
              "Data de Término": user.endDate || "",
              Prazo: user.deadline || "",
              Progresso: user.progress || "",
              Situação: user.situationName || user.situation || "",
              Nota: user.grade || "",
              Estado: user.statusName || "",
            };

            data.push(userData);
          }
        }
      }

      // Verificar se há dados para download
      if (data.length === 0) {
        this.showErrorMessage("Nenhum dado disponível para download.");
        return;
      }

      switch (format) {
        case "csv":
          this.downloadCSV(data);
          break;
        case "xlsx":
          this.downloadXLSX(data);
          break;
        case "html":
          this.downloadHTML(data);
          break;
        case "json":
          this.downloadJSON(data);
          break;
        case "ods":
          this.downloadODS(data);
          break;
        case "pdf":
          this.downloadPDF(data);
          break;
        default:
          this.showErrorMessage("Formato de download não suportado.");
          break;
      }
    },

    downloadCSV(data) {
      // Implementação para download de CSV
      if (data.length === 0) return;

      // Usar a codificação UTF-8 com BOM para garantir que caracteres especiais sejam exibidos corretamente
      const BOM = "\uFEFF";
      const headers = Object.keys(data[0]);

      // Formatar os cabeçalhos para melhor legibilidade
      const formattedHeaders = headers.map((header) => {
        // Converter camelCase para Title Case com espaços
        return header
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase())
          .trim();
      });

      // Criar o conteúdo do CSV
      const csvContent =
        BOM +
        [
          formattedHeaders.join(","),
          ...data.map((row) =>
            headers
              .map((header) => {
                // Escapar aspas e garantir que todos os campos estejam entre aspas
                const value = row[header] || "";
                return `"${String(value).replace(/"/g, '""')}"`;
              })
              .join(",")
          ),
        ].join("\n");

      // Criar o blob e fazer o download
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },

    downloadXLSX(data) {
      if (data.length === 0) return;

      // Usar a codificação UTF-8 com BOM para garantir que caracteres especiais sejam exibidos corretamente no Excel
      const BOM = "\uFEFF";
      const headers = Object.keys(data[0]);

      // Criar o conteúdo do CSV
      const csvContent =
        BOM +
        [
          headers.join(","),
          ...data.map((row) =>
            headers
              .map((header) => {
                // Escapar aspas e garantir que todos os campos estejam entre aspas
                const value = row[header] || "";
                return `"${String(value).replace(/"/g, '""')}"`;
              })
              .join(",")
          ),
        ].join("\n");

      // Criar o blob e fazer o download
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // Mostrar uma mensagem informativa
      this.showSuccessMessage(
        "Download concluído. O arquivo CSV pode ser aberto no Excel."
      );
    },

    downloadHTML(data) {
      if (data.length === 0) return;

      const headers = Object.keys(data[0]);

      const formattedHeaders = [];
      for (let i = 0; i < headers.length; i++) {
        const formatted = headers[i]
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase())
          .trim();
        formattedHeaders.push(formatted);
      }

      let tableHeaders = "";
      for (let i = 0; i < formattedHeaders.length; i++) {
        tableHeaders += "<th>" + formattedHeaders[i] + "</th>";
      }

      let tableRows = "";
      for (let i = 0; i < data.length; i++) {
        let rowHtml = "<tr>";
        for (let j = 0; j < headers.length; j++) {
          rowHtml += "<td>" + (data[i][headers[j]] || "") + "</td>";
        }
        rowHtml += "</tr>";
        tableRows += rowHtml;
      }

      const htmlStart =
        '<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>';
      const styles =
        "<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>";
      const bodyStart = "</head><body><h1>Usuários Matriculados</h1>";
      const tableStart = "<table><thead><tr>";
      const tableMiddle = "</tr></thead><tbody>";
      const tableEnd = "</tbody></table>";
      const footer =
        '<div class="footer">Gerado em ' +
        new Date().toLocaleString() +
        "</div>";
      const htmlEnd = "</body></html>";

      const htmlContent =
        htmlStart +
        styles +
        bodyStart +
        tableStart +
        tableHeaders +
        tableMiddle +
        tableRows +
        tableEnd +
        footer +
        htmlEnd;

      const blob = new Blob([htmlContent], {
        type: "text/html;charset=utf-8;",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.html");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // Mostrar uma mensagem informativa
      this.showSuccessMessage(
        "Download concluído. O arquivo HTML foi salvo com sucesso."
      );
    },

    downloadJSON(data) {
      // Implementação para download de JSON
      if (data.length === 0) return;

      // Criar o conteúdo JSON formatado
      const jsonContent = JSON.stringify(data, null, 2);

      // Criar o blob e fazer o download
      const blob = new Blob([jsonContent], {
        type: "application/json;charset=utf-8;",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.json");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },

    downloadODS(data) {
      if (data.length === 0) return;

      const BOM = "\uFEFF";
      const headers = Object.keys(data[0]);

      let csvRows = [];

      csvRows.push(headers.join(","));

      data.forEach((row) => {
        const values = headers.map((header) => {
          const value = row[header] || "";
          return '"' + String(value).replace(/"/g, '""') + '"';
        });
        csvRows.push(values.join(","));
      });

      const csvContent = BOM + csvRows.join("\n");

      const blob = new Blob([csvContent], {
        type: "text/csv;charset=utf-8;",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "usuarios_matriculados.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.showSuccessMessage(
        "Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS."
      );
    },

    downloadPDF(data) {
      if (data.length === 0) return;

      this.downloadHTML(data);

      this.showSuccessMessage(
        "Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."
      );
    },

    editEnrolments() {
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showErrorMessage(
          "Por favor, selecione pelo menos um usuário para editar matrícula."
        );
        return;
      }

      if (this.selectedUsers.length === 1) {
        const userId = this.selectedUsers[0];
        const user = this.enrolments.find((offer) => offer.id === userId);
        if (user) {
          this.editUser(user);
        } else {
          this.showErrorMessage(
            "Usuário não encontrado. Por favor, tente novamente."
          );
        }
      } else {
        this.showBulkEditEnrollmentModal = true;
      }
    },

    async handleBulkEditEnrollmentSuccess(data) {

      this.showSuccessMessage(
        data.message || "Matrículas editadas com sucesso."
      );

      await this.loadRegisteredUsers();

      this.selectedUsers = [];
      this.showBulkEditEnrollmentModal = false;
    },

    handleBulkEditEnrollmentError(errorMessage) {
      const defaulMessage =
        "Não foi possível editar as matrículas. Por favor, tente novamente.";
      this.showErrorMessage(errorMessage || defaulMessage);
    },

    bulkDeleteEnrollment() {
      if (!this.selectedUsers || this.selectedUsers.length === 0) {
        this.showWarningMessage(
          "Por favor, selecione pelo menos um usuário para excluir matrícula."
        );
        return;
      }

      this.showBulkDeleteEnrollmentModal = true;
    },

    handleBulkDeleteEnrollmentError(errorMessage) {
      const defaulMessage =
        "Não foi possível excluir as matrículas. Por favor, tente novamente.";
      this.showErrorMessage(errorMessage || defaulMessage);
    },

    /**
     * Exibe uma mensagem de sucesso usando o Toast
     * @param {string} message Mensagem a ser exibida
     */
    showSuccessMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "success";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    /**
     * Exibe uma mensagem de erro usando o Toast
     * @param {string} message Mensagem a ser exibida
     */
    showErrorMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "error";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },

    showWarningMessage(message) {
      // Limpa qualquer timeout anterior para esconder o toast
      if (this.toastTimeout) {
        clearTimeout(this.toastTimeout);
        this.toastTimeout = null;
      }

      // Garante que o toast esteja escondido antes de mostrar novamente
      this.showToast = false;

      // Usa nextTick para garantir que o DOM seja atualizado antes de mostrar
      this.$nextTick(() => {
        this.toastMessage = message;
        this.toastType = "warning";
        this.showToast = true;

        // Define um novo timeout para esconder o toast
        this.toastTimeout = setTimeout(() => {
          this.showToast = false;
        }, 3000);
      });
    },
  },
};
</script>

<style src="@/assets/scss/RegisteredUsers.scss" lang="scss" scoped></style>
