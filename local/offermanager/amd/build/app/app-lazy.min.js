define("local_offermanager/app/app-lazy",function(){"use strict";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function er(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const tt={}.NODE_ENV!=="production"?Object.freeze({}):{},Dn={}.NODE_ENV!=="production"?Object.freeze([]):[],St=()=>{},Bg=()=>!1,to=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ni=e=>e.startsWith("onUpdate:"),pt=Object.assign,Ka=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},$g=Object.prototype.hasOwnProperty,Ye=(e,t)=>$g.call(e,t),me=Array.isArray,Kr=e=>so(e)==="[object Map]",xn=e=>so(e)==="[object Set]",Mc=e=>so(e)==="[object Date]",xe=e=>typeof e=="function",ct=e=>typeof e=="string",Is=e=>typeof e=="symbol",Ze=e=>e!==null&&typeof e=="object",Ya=e=>(Ze(e)||xe(e))&&xe(e.then)&&xe(e.catch),Pc=Object.prototype.toString,so=e=>Pc.call(e),Qa=e=>so(e).slice(8,-1),kc=e=>so(e)==="[object Object]",Za=e=>ct(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ro=er(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),jg=er("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),oi=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Hg=/-(\w)/g,Kt=oi(e=>e.replace(Hg,(t,s)=>s?s.toUpperCase():"")),qg=/\B([A-Z])/g,Or=oi(e=>e.replace(qg,"-$1").toLowerCase()),Yr=oi(e=>e.charAt(0).toUpperCase()+e.slice(1)),Qr=oi(e=>e?`on${Yr(e)}`:""),Sr=(e,t)=>!Object.is(e,t),On=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},ii=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},ai=e=>{const t=parseFloat(e);return isNaN(t)?e:t},zg=e=>{const t=ct(e)?Number(e):NaN;return isNaN(t)?e:t};let Vc;const no=()=>Vc||(Vc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function us(e){if(me(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],n=ct(i)?Yg(i):us(i);if(n)for(const a in n)t[a]=n[a]}return t}else if(ct(e)||Ze(e))return e}const Wg=/;(?![^(]*\))/g,Gg=/:([^]+)/,Kg=/\/\*[^]*?\*\//g;function Yg(e){const t={};return e.replace(Kg,"").split(Wg).forEach(s=>{if(s){const i=s.split(Gg);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function he(e){let t="";if(ct(e))t=e;else if(me(e))for(let s=0;s<e.length;s++){const i=he(e[s]);i&&(t+=i+" ")}else if(Ze(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Qg="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",Zg="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",Jg="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",Xg=er(Qg),e_=er(Zg),t_=er(Jg),s_=er("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Rc(e){return!!e||e===""}function r_(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=oo(e[i],t[i]);return s}function oo(e,t){if(e===t)return!0;let s=Mc(e),i=Mc(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=Is(e),i=Is(t),s||i)return e===t;if(s=me(e),i=me(t),s||i)return s&&i?r_(e,t):!1;if(s=Ze(e),i=Ze(t),s||i){if(!s||!i)return!1;const n=Object.keys(e).length,a=Object.keys(t).length;if(n!==a)return!1;for(const u in e){const c=e.hasOwnProperty(u),h=t.hasOwnProperty(u);if(c&&!h||!c&&h||!oo(e[u],t[u]))return!1}}return String(e)===String(t)}function Ja(e,t){return e.findIndex(s=>oo(s,t))}const Lc=e=>!!(e&&e.__v_isRef===!0),G=e=>ct(e)?e:e==null?"":me(e)||Ze(e)&&(e.toString===Pc||!xe(e.toString))?Lc(e)?G(e.value):JSON.stringify(e,Uc,2):String(e),Uc=(e,t)=>Lc(t)?Uc(e,t.value):Kr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,n],a)=>(s[Xa(i,a)+" =>"]=n,s),{})}:xn(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>Xa(s))}:Is(t)?Xa(t):Ze(t)&&!me(t)&&!kc(t)?String(t):t,Xa=(e,t="")=>{var s;return Is(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Hs(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let cs;class Fc{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=cs,!t&&cs&&(this.index=(cs.scopes||(cs.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=cs;try{return cs=this,t()}finally{cs=s}}else({}).NODE_ENV!=="production"&&Hs("cannot run an inactive effect scope.")}on(){cs=this}off(){cs=this.parent}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function n_(e){return new Fc(e)}function o_(){return cs}let st;const el=new WeakSet;class Bc{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,cs&&cs.active&&cs.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,el.has(this)&&(el.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||jc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Gc(this),Hc(this);const t=st,s=Ms;st=this,Ms=!0;try{return this.fn()}finally{({}).NODE_ENV!=="production"&&st!==this&&Hs("Active effect was not restored correctly - this is likely a Vue internal bug."),qc(this),st=t,Ms=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)nl(t);this.deps=this.depsTail=void 0,Gc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?el.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){rl(this)&&this.run()}get dirty(){return rl(this)}}let $c=0,io,ao;function jc(e,t=!1){if(e.flags|=8,t){e.next=ao,ao=e;return}e.next=io,io=e}function tl(){$c++}function sl(){if(--$c>0)return;if(ao){let t=ao;for(ao=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;io;){let t=io;for(io=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function Hc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function qc(e){let t,s=e.depsTail,i=s;for(;i;){const n=i.prevDep;i.version===-1?(i===s&&(s=n),nl(i),i_(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=n}e.deps=t,e.depsTail=s}function rl(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(zc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function zc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===lo))return;e.globalVersion=lo;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!rl(e)){e.flags&=-3;return}const s=st,i=Ms;st=e,Ms=!0;try{Hc(e);const n=e.fn(e._value);(t.version===0||Sr(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{st=s,Ms=i,qc(e),e.flags&=-3}}function nl(e,t=!1){const{dep:s,prevSub:i,nextSub:n}=e;if(i&&(i.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=i,e.nextSub=void 0),{}.NODE_ENV!=="production"&&s.subsHead===e&&(s.subsHead=n),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let a=s.computed.deps;a;a=a.nextDep)nl(a,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function i_(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Ms=!0;const Wc=[];function tr(){Wc.push(Ms),Ms=!1}function sr(){const e=Wc.pop();Ms=e===void 0?!0:e}function Gc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=st;st=void 0;try{t()}finally{st=s}}}let lo=0;class a_{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ol{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,{}.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!st||!Ms||st===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==st)s=this.activeLink=new a_(st,this),st.deps?(s.prevDep=st.depsTail,st.depsTail.nextDep=s,st.depsTail=s):st.deps=st.depsTail=s,Kc(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=st.depsTail,s.nextDep=void 0,st.depsTail.nextDep=s,st.depsTail=s,st.deps===s&&(st.deps=i)}return{}.NODE_ENV!=="production"&&st.onTrack&&st.onTrack(pt({effect:st},t)),s}trigger(t){this.version++,lo++,this.notify(t)}notify(t){tl();try{if({}.NODE_ENV!=="production")for(let s=this.subsHead;s;s=s.nextSub)s.sub.onTrigger&&!(s.sub.flags&8)&&s.sub.onTrigger(pt({effect:s.sub},t));for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{sl()}}}function Kc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Kc(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),{}.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}}const il=new WeakMap,Zr=Symbol({}.NODE_ENV!=="production"?"Object iterate":""),al=Symbol({}.NODE_ENV!=="production"?"Map keys iterate":""),uo=Symbol({}.NODE_ENV!=="production"?"Array iterate":"");function Tt(e,t,s){if(Ms&&st){let i=il.get(e);i||il.set(e,i=new Map);let n=i.get(s);n||(i.set(s,n=new ol),n.map=i,n.key=s),{}.NODE_ENV!=="production"?n.track({target:e,type:t,key:s}):n.track()}}function qs(e,t,s,i,n,a){const u=il.get(e);if(!u){lo++;return}const c=h=>{h&&({}.NODE_ENV!=="production"?h.trigger({target:e,type:t,key:s,newValue:i,oldValue:n,oldTarget:a}):h.trigger())};if(tl(),t==="clear")u.forEach(c);else{const h=me(e),m=h&&Za(s);if(h&&s==="length"){const p=Number(i);u.forEach((_,w)=>{(w==="length"||w===uo||!Is(w)&&w>=p)&&c(_)})}else switch((s!==void 0||u.has(void 0))&&c(u.get(s)),m&&c(u.get(uo)),t){case"add":h?m&&c(u.get("length")):(c(u.get(Zr)),Kr(e)&&c(u.get(al)));break;case"delete":h||(c(u.get(Zr)),Kr(e)&&c(u.get(al)));break;case"set":Kr(e)&&c(u.get(Zr));break}}sl()}function Sn(e){const t=Ie(e);return t===e?t:(Tt(t,"iterate",uo),Yt(e)?t:t.map(Ht))}function li(e){return Tt(e=Ie(e),"iterate",uo),e}const l_={__proto__:null,[Symbol.iterator](){return ll(this,Symbol.iterator,Ht)},concat(...e){return Sn(this).concat(...e.map(t=>me(t)?Sn(t):t))},entries(){return ll(this,"entries",e=>(e[1]=Ht(e[1]),e))},every(e,t){return rr(this,"every",e,t,void 0,arguments)},filter(e,t){return rr(this,"filter",e,t,s=>s.map(Ht),arguments)},find(e,t){return rr(this,"find",e,t,Ht,arguments)},findIndex(e,t){return rr(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return rr(this,"findLast",e,t,Ht,arguments)},findLastIndex(e,t){return rr(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return rr(this,"forEach",e,t,void 0,arguments)},includes(...e){return ul(this,"includes",e)},indexOf(...e){return ul(this,"indexOf",e)},join(e){return Sn(this).join(e)},lastIndexOf(...e){return ul(this,"lastIndexOf",e)},map(e,t){return rr(this,"map",e,t,void 0,arguments)},pop(){return co(this,"pop")},push(...e){return co(this,"push",e)},reduce(e,...t){return Yc(this,"reduce",e,t)},reduceRight(e,...t){return Yc(this,"reduceRight",e,t)},shift(){return co(this,"shift")},some(e,t){return rr(this,"some",e,t,void 0,arguments)},splice(...e){return co(this,"splice",e)},toReversed(){return Sn(this).toReversed()},toSorted(e){return Sn(this).toSorted(e)},toSpliced(...e){return Sn(this).toSpliced(...e)},unshift(...e){return co(this,"unshift",e)},values(){return ll(this,"values",Ht)}};function ll(e,t,s){const i=li(e),n=i[t]();return i!==e&&!Yt(e)&&(n._next=n.next,n.next=()=>{const a=n._next();return a.value&&(a.value=s(a.value)),a}),n}const u_=Array.prototype;function rr(e,t,s,i,n,a){const u=li(e),c=u!==e&&!Yt(e),h=u[t];if(h!==u_[t]){const _=h.apply(e,a);return c?Ht(_):_}let m=s;u!==e&&(c?m=function(_,w){return s.call(this,Ht(_),w,e)}:s.length>2&&(m=function(_,w){return s.call(this,_,w,e)}));const p=h.call(u,m,i);return c&&n?n(p):p}function Yc(e,t,s,i){const n=li(e);let a=s;return n!==e&&(Yt(e)?s.length>3&&(a=function(u,c,h){return s.call(this,u,c,h,e)}):a=function(u,c,h){return s.call(this,u,Ht(c),h,e)}),n[t](a,...i)}function ul(e,t,s){const i=Ie(e);Tt(i,"iterate",uo);const n=i[t](...s);return(n===-1||n===!1)&&pi(s[0])?(s[0]=Ie(s[0]),i[t](...s)):n}function co(e,t,s=[]){tr(),tl();const i=Ie(e)[t].apply(e,s);return sl(),sr(),i}const c_=er("__proto__,__v_isRef,__isVue"),Qc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Is));function d_(e){Is(e)||(e=String(e));const t=Ie(this);return Tt(t,"has",e),t.hasOwnProperty(e)}class Zc{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,a=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return a;if(s==="__v_raw")return i===(n?a?nd:rd:a?sd:td).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const u=me(t);if(!n){let h;if(u&&(h=l_[s]))return h;if(s==="hasOwnProperty")return d_}const c=Reflect.get(t,s,Dt(t)?t:i);return(Is(s)?Qc.has(s):c_(s))||(n||Tt(t,"get",s),a)?c:Dt(c)?u&&Za(s)?c:c.value:Ze(c)?n?id(c):fi(c):c}}class Jc extends Zc{constructor(t=!1){super(!1,t)}set(t,s,i,n){let a=t[s];if(!this._isShallow){const h=nr(a);if(!Yt(i)&&!nr(i)&&(a=Ie(a),i=Ie(i)),!me(t)&&Dt(a)&&!Dt(i))return h?!1:(a.value=i,!0)}const u=me(t)&&Za(s)?Number(s)<t.length:Ye(t,s),c=Reflect.set(t,s,i,Dt(t)?t:n);return t===Ie(n)&&(u?Sr(i,a)&&qs(t,"set",s,i,a):qs(t,"add",s,i)),c}deleteProperty(t,s){const i=Ye(t,s),n=t[s],a=Reflect.deleteProperty(t,s);return a&&i&&qs(t,"delete",s,void 0,n),a}has(t,s){const i=Reflect.has(t,s);return(!Is(s)||!Qc.has(s))&&Tt(t,"has",s),i}ownKeys(t){return Tt(t,"iterate",me(t)?"length":Zr),Reflect.ownKeys(t)}}class Xc extends Zc{constructor(t=!1){super(!0,t)}set(t,s){return{}.NODE_ENV!=="production"&&Hs(`Set operation on key "${String(s)}" failed: target is readonly.`,t),!0}deleteProperty(t,s){return{}.NODE_ENV!=="production"&&Hs(`Delete operation on key "${String(s)}" failed: target is readonly.`,t),!0}}const f_=new Jc,h_=new Xc,p_=new Jc(!0),m_=new Xc(!0),cl=e=>e,ui=e=>Reflect.getPrototypeOf(e);function g_(e,t,s){return function(...i){const n=this.__v_raw,a=Ie(n),u=Kr(a),c=e==="entries"||e===Symbol.iterator&&u,h=e==="keys"&&u,m=n[e](...i),p=s?cl:t?fl:Ht;return!t&&Tt(a,"iterate",h?al:Zr),{next(){const{value:_,done:w}=m.next();return w?{value:_,done:w}:{value:c?[p(_[0]),p(_[1])]:p(_),done:w}},[Symbol.iterator](){return this}}}}function ci(e){return function(...t){if({}.NODE_ENV!=="production"){const s=t[0]?`on key "${t[0]}" `:"";Hs(`${Yr(e)} operation ${s}failed: target is readonly.`,Ie(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function __(e,t){const s={get(n){const a=this.__v_raw,u=Ie(a),c=Ie(n);e||(Sr(n,c)&&Tt(u,"get",n),Tt(u,"get",c));const{has:h}=ui(u),m=t?cl:e?fl:Ht;if(h.call(u,n))return m(a.get(n));if(h.call(u,c))return m(a.get(c));a!==u&&a.get(n)},get size(){const n=this.__v_raw;return!e&&Tt(Ie(n),"iterate",Zr),Reflect.get(n,"size",n)},has(n){const a=this.__v_raw,u=Ie(a),c=Ie(n);return e||(Sr(n,c)&&Tt(u,"has",n),Tt(u,"has",c)),n===c?a.has(n):a.has(n)||a.has(c)},forEach(n,a){const u=this,c=u.__v_raw,h=Ie(c),m=t?cl:e?fl:Ht;return!e&&Tt(h,"iterate",Zr),c.forEach((p,_)=>n.call(a,m(p),m(_),u))}};return pt(s,e?{add:ci("add"),set:ci("set"),delete:ci("delete"),clear:ci("clear")}:{add(n){!t&&!Yt(n)&&!nr(n)&&(n=Ie(n));const a=Ie(this);return ui(a).has.call(a,n)||(a.add(n),qs(a,"add",n,n)),this},set(n,a){!t&&!Yt(a)&&!nr(a)&&(a=Ie(a));const u=Ie(this),{has:c,get:h}=ui(u);let m=c.call(u,n);m?{}.NODE_ENV!=="production"&&ed(u,c,n):(n=Ie(n),m=c.call(u,n));const p=h.call(u,n);return u.set(n,a),m?Sr(a,p)&&qs(u,"set",n,a,p):qs(u,"add",n,a),this},delete(n){const a=Ie(this),{has:u,get:c}=ui(a);let h=u.call(a,n);h?{}.NODE_ENV!=="production"&&ed(a,u,n):(n=Ie(n),h=u.call(a,n));const m=c?c.call(a,n):void 0,p=a.delete(n);return h&&qs(a,"delete",n,void 0,m),p},clear(){const n=Ie(this),a=n.size!==0,u={}.NODE_ENV!=="production"?Kr(n)?new Map(n):new Set(n):void 0,c=n.clear();return a&&qs(n,"clear",void 0,void 0,u),c}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=g_(n,e,t)}),s}function di(e,t){const s=__(e,t);return(i,n,a)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?i:Reflect.get(Ye(s,n)&&n in i?s:i,n,a)}const v_={get:di(!1,!1)},y_={get:di(!1,!0)},b_={get:di(!0,!1)},w_={get:di(!0,!0)};function ed(e,t,s){const i=Ie(s);if(i!==s&&t.call(e,i)){const n=Qa(e);Hs(`Reactive ${n} contains both the raw and reactive versions of the same object${n==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const td=new WeakMap,sd=new WeakMap,rd=new WeakMap,nd=new WeakMap;function E_(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function C_(e){return e.__v_skip||!Object.isExtensible(e)?0:E_(Qa(e))}function fi(e){return nr(e)?e:hi(e,!1,f_,v_,td)}function od(e){return hi(e,!1,p_,y_,sd)}function id(e){return hi(e,!0,h_,b_,rd)}function zs(e){return hi(e,!0,m_,w_,nd)}function hi(e,t,s,i,n){if(!Ze(e))return{}.NODE_ENV!=="production"&&Hs(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=n.get(e);if(a)return a;const u=C_(e);if(u===0)return e;const c=new Proxy(e,u===2?i:s);return n.set(e,c),c}function Jr(e){return nr(e)?Jr(e.__v_raw):!!(e&&e.__v_isReactive)}function nr(e){return!!(e&&e.__v_isReadonly)}function Yt(e){return!!(e&&e.__v_isShallow)}function pi(e){return e?!!e.__v_raw:!1}function Ie(e){const t=e&&e.__v_raw;return t?Ie(t):e}function dl(e){return!Ye(e,"__v_skip")&&Object.isExtensible(e)&&ii(e,"__v_skip",!0),e}const Ht=e=>Ze(e)?fi(e):e,fl=e=>Ze(e)?id(e):e;function Dt(e){return e?e.__v_isRef===!0:!1}function ad(e){return ld(e,!1)}function D_(e){return ld(e,!0)}function ld(e,t){return Dt(e)?e:new x_(e,t)}class x_{constructor(t,s){this.dep=new ol,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Ie(t),this._value=s?t:Ht(t),this.__v_isShallow=s}get value(){return{}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||Yt(t)||nr(t);t=i?t:Ie(t),Sr(t,s)&&(this._rawValue=t,this._value=i?t:Ht(t),{}.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:s}):this.dep.trigger())}}function Tr(e){return Dt(e)?e.value:e}const O_={get:(e,t,s)=>t==="__v_raw"?e:Tr(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const n=e[t];return Dt(n)&&!Dt(s)?(n.value=s,!0):Reflect.set(e,t,s,i)}};function ud(e){return Jr(e)?e:new Proxy(e,O_)}class S_{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new ol(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=lo-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&st!==this)return jc(this,!0),!0}get value(){const t={}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return zc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):{}.NODE_ENV!=="production"&&Hs("Write operation failed: computed value is readonly")}}function T_(e,t,s=!1){let i,n;xe(e)?i=e:(i=e.get,n=e.set);const a=new S_(i,n,s);return{}.NODE_ENV!=="production"&&t&&!s&&(a.onTrack=t.onTrack,a.onTrigger=t.onTrigger),a}const mi={},gi=new WeakMap;let Xr;function N_(e,t=!1,s=Xr){if(s){let i=gi.get(s);i||gi.set(s,i=[]),i.push(e)}else({}).NODE_ENV!=="production"&&!t&&Hs("onWatcherCleanup() was called when there was no active watcher to associate with.")}function A_(e,t,s=tt){const{immediate:i,deep:n,once:a,scheduler:u,augmentJob:c,call:h}=s,m=Z=>{(s.onWarn||Hs)("Invalid watch source: ",Z,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},p=Z=>n?Z:Yt(Z)||n===!1||n===0?or(Z,1):or(Z);let _,w,D,V,F=!1,te=!1;if(Dt(e)?(w=()=>e.value,F=Yt(e)):Jr(e)?(w=()=>p(e),F=!0):me(e)?(te=!0,F=e.some(Z=>Jr(Z)||Yt(Z)),w=()=>e.map(Z=>{if(Dt(Z))return Z.value;if(Jr(Z))return p(Z);if(xe(Z))return h?h(Z,2):Z();({}).NODE_ENV!=="production"&&m(Z)})):xe(e)?t?w=h?()=>h(e,2):e:w=()=>{if(D){tr();try{D()}finally{sr()}}const Z=Xr;Xr=_;try{return h?h(e,3,[V]):e(V)}finally{Xr=Z}}:(w=St,{}.NODE_ENV!=="production"&&m(e)),t&&n){const Z=w,fe=n===!0?1/0:n;w=()=>or(Z(),fe)}const I=o_(),se=()=>{_.stop(),I&&I.active&&Ka(I.effects,_)};if(a&&t){const Z=t;t=(...fe)=>{Z(...fe),se()}}let K=te?new Array(e.length).fill(mi):mi;const ye=Z=>{if(!(!(_.flags&1)||!_.dirty&&!Z))if(t){const fe=_.run();if(n||F||(te?fe.some((ve,Ae)=>Sr(ve,K[Ae])):Sr(fe,K))){D&&D();const ve=Xr;Xr=_;try{const Ae=[fe,K===mi?void 0:te&&K[0]===mi?[]:K,V];h?h(t,3,Ae):t(...Ae),K=fe}finally{Xr=ve}}}else _.run()};return c&&c(ye),_=new Bc(w),_.scheduler=u?()=>u(ye,!1):ye,V=Z=>N_(Z,!1,_),D=_.onStop=()=>{const Z=gi.get(_);if(Z){if(h)h(Z,4);else for(const fe of Z)fe();gi.delete(_)}},{}.NODE_ENV!=="production"&&(_.onTrack=s.onTrack,_.onTrigger=s.onTrigger),t?i?ye(!0):K=_.run():u?u(ye.bind(null,!0),!0):_.run(),se.pause=_.pause.bind(_),se.resume=_.resume.bind(_),se.stop=se,se}function or(e,t=1/0,s){if(t<=0||!Ze(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Dt(e))or(e.value,t,s);else if(me(e))for(let i=0;i<e.length;i++)or(e[i],t,s);else if(xn(e)||Kr(e))e.forEach(i=>{or(i,t,s)});else if(kc(e)){for(const i in e)or(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&or(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const en=[];function _i(e){en.push(e)}function vi(){en.pop()}let hl=!1;function Q(e,...t){if(hl)return;hl=!0,tr();const s=en.length?en[en.length-1].component:null,i=s&&s.appContext.config.warnHandler,n=I_();if(i)Tn(i,s,11,[e+t.map(a=>{var u,c;return(c=(u=a.toString)==null?void 0:u.call(a))!=null?c:JSON.stringify(a)}).join(""),s&&s.proxy,n.map(({vnode:a})=>`at <${Ui(s,a.type)}>`).join(`
`),n]);else{const a=[`[Vue warn]: ${e}`,...t];n.length&&a.push(`
`,...M_(n)),console.warn(...a)}sr(),hl=!1}function I_(){let e=en[en.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function M_(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...P_(s))}),t}function P_({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,n=` at <${Ui(e.component,e.type,i)}`,a=">"+s;return e.props?[n,...k_(e.props),a]:[n+a]}function k_(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...cd(i,e[i]))}),s.length>3&&t.push(" ..."),t}function cd(e,t,s){return ct(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:Dt(t)?(t=cd(e,Ie(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):xe(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Ie(t),s?t:[`${e}=`,t])}function V_(e,t){({}).NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?Q(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&Q(`${t} is NaN - the duration expression might be incorrect.`))}const pl={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function Tn(e,t,s,i){try{return i?e(...i):e()}catch(n){fo(n,t,s)}}function Ps(e,t,s,i){if(xe(e)){const n=Tn(e,t,s,i);return n&&Ya(n)&&n.catch(a=>{fo(a,t,s)}),n}if(me(e)){const n=[];for(let a=0;a<e.length;a++)n.push(Ps(e[a],t,s,i));return n}else({}).NODE_ENV!=="production"&&Q(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function fo(e,t,s,i=!0){const n=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||tt;if(t){let c=t.parent;const h=t.proxy,m={}.NODE_ENV!=="production"?pl[s]:`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const p=c.ec;if(p){for(let _=0;_<p.length;_++)if(p[_](e,h,m)===!1)return}c=c.parent}if(a){tr(),Tn(a,null,10,[e,h,m]),sr();return}}R_(e,s,n,i,u)}function R_(e,t,s,i=!0,n=!1){if({}.NODE_ENV!=="production"){const a=pl[t];if(s&&_i(s),Q(`Unhandled error${a?` during execution of ${a}`:""}`),s&&vi(),i)throw e;console.error(e)}else{if(n)throw e;console.error(e)}}const Qt=[];let Ws=-1;const Nn=[];let Nr=null,An=0;const dd=Promise.resolve();let yi=null;const L_=100;function ml(e){const t=yi||dd;return e?t.then(this?e.bind(this):e):t}function U_(e){let t=Ws+1,s=Qt.length;for(;t<s;){const i=t+s>>>1,n=Qt[i],a=ho(n);a<e||a===e&&n.flags&2?t=i+1:s=i}return t}function bi(e){if(!(e.flags&1)){const t=ho(e),s=Qt[Qt.length-1];!s||!(e.flags&2)&&t>=ho(s)?Qt.push(e):Qt.splice(U_(t),0,e),e.flags|=1,fd()}}function fd(){yi||(yi=dd.then(gd))}function hd(e){me(e)?Nn.push(...e):Nr&&e.id===-1?Nr.splice(An+1,0,e):e.flags&1||(Nn.push(e),e.flags|=1),fd()}function pd(e,t,s=Ws+1){for({}.NODE_ENV!=="production"&&(t=t||new Map);s<Qt.length;s++){const i=Qt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid||{}.NODE_ENV!=="production"&&gl(t,i))continue;Qt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function md(e){if(Nn.length){const t=[...new Set(Nn)].sort((s,i)=>ho(s)-ho(i));if(Nn.length=0,Nr){Nr.push(...t);return}for(Nr=t,{}.NODE_ENV!=="production"&&(e=e||new Map),An=0;An<Nr.length;An++){const s=Nr[An];({}).NODE_ENV!=="production"&&gl(e,s)||(s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2)}Nr=null,An=0}}const ho=e=>e.id==null?e.flags&2?-1:1/0:e.id;function gd(e){({}).NODE_ENV!=="production"&&(e=e||new Map);const t={}.NODE_ENV!=="production"?s=>gl(e,s):St;try{for(Ws=0;Ws<Qt.length;Ws++){const s=Qt[Ws];if(s&&!(s.flags&8)){if({}.NODE_ENV!=="production"&&t(s))continue;s.flags&4&&(s.flags&=-2),Tn(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2)}}}finally{for(;Ws<Qt.length;Ws++){const s=Qt[Ws];s&&(s.flags&=-2)}Ws=-1,Qt.length=0,md(e),yi=null,(Qt.length||Nn.length)&&gd(e)}}function gl(e,t){const s=e.get(t)||0;if(s>L_){const i=t.i,n=i&&jl(i.type);return fo(`Maximum recursive updates exceeded${n?` in component <${n}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,s+1),!1}let ks=!1;const wi=new Map;({}).NODE_ENV!=="production"&&(no().__VUE_HMR_RUNTIME__={createRecord:_l(_d),rerender:_l($_),reload:_l(j_)});const tn=new Map;function F_(e){const t=e.type.__hmrId;let s=tn.get(t);s||(_d(t,e.type),s=tn.get(t)),s.instances.add(e)}function B_(e){tn.get(e.type.__hmrId).instances.delete(e)}function _d(e,t){return tn.has(e)?!1:(tn.set(e,{initialDef:Ei(t),instances:new Set}),!0)}function Ei(e){return Af(e)?e.__vccOpts:e}function $_(e,t){const s=tn.get(e);s&&(s.initialDef.render=t,[...s.instances].forEach(i=>{t&&(i.render=t,Ei(i.type).render=t),i.renderCache=[],ks=!0,i.update(),ks=!1}))}function j_(e,t){const s=tn.get(e);if(!s)return;t=Ei(t),vd(s.initialDef,t);const i=[...s.instances];for(let n=0;n<i.length;n++){const a=i[n],u=Ei(a.type);let c=wi.get(u);c||(u!==s.initialDef&&vd(u,t),wi.set(u,c=new Set)),c.add(a),a.appContext.propsCache.delete(a.type),a.appContext.emitsCache.delete(a.type),a.appContext.optionsCache.delete(a.type),a.ceReload?(c.add(a),a.ceReload(t.styles),c.delete(a)):a.parent?bi(()=>{ks=!0,a.parent.update(),ks=!1,c.delete(a)}):a.appContext.reload?a.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),a.root.ce&&a!==a.root&&a.root.ce._removeChildStyle(u)}hd(()=>{wi.clear()})}function vd(e,t){pt(e,t);for(const s in e)s!=="__file"&&!(s in t)&&delete e[s]}function _l(e){return(t,s)=>{try{return e(t,s)}catch(i){console.error(i),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Gs,po=[],vl=!1;function mo(e,...t){Gs?Gs.emit(e,...t):vl||po.push({event:e,args:t})}function yd(e,t){var s,i;Gs=e,Gs?(Gs.enabled=!0,po.forEach(({event:n,args:a})=>Gs.emit(n,...a)),po=[]):typeof window<"u"&&window.HTMLElement&&!((i=(s=window.navigator)==null?void 0:s.userAgent)!=null&&i.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(a=>{yd(a,t)}),setTimeout(()=>{Gs||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,vl=!0,po=[])},3e3)):(vl=!0,po=[])}function H_(e,t){mo("app:init",e,t,{Fragment:Me,Text:wo,Comment:wt,Static:Eo})}function q_(e){mo("app:unmount",e)}const z_=yl("component:added"),bd=yl("component:updated"),W_=yl("component:removed"),G_=e=>{Gs&&typeof Gs.cleanupBuffer=="function"&&!Gs.cleanupBuffer(e)&&W_(e)};/*! #__NO_SIDE_EFFECTS__ */function yl(e){return t=>{mo(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const K_=wd("perf:start"),Y_=wd("perf:end");function wd(e){return(t,s,i)=>{mo(e,t.appContext.app,t.uid,t,s,i)}}function Q_(e,t,s){mo("component:emit",e.appContext.app,e,t,s)}let xt=null,Ed=null;function Ci(e){const t=xt;return xt=e,Ed=e&&e.type.__scopeId||null,t}function Ne(e,t=xt,s){if(!t||e._n)return e;const i=(...n)=>{i._d&&bf(-1);const a=Ci(t);let u;try{u=e(...n)}finally{Ci(a),i._d&&bf(1)}return{}.NODE_ENV!=="production"&&bd(t),u};return i._n=!0,i._c=!0,i._d=!0,i}function Cd(e){jg(e)&&Q("Do not use built-in directive ids as custom directive id: "+e)}function lt(e,t){if(xt===null)return{}.NODE_ENV!=="production"&&Q("withDirectives can only be used inside render functions."),e;const s=Li(xt),i=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[a,u,c,h=tt]=t[n];a&&(xe(a)&&(a={mounted:a,updated:a}),a.deep&&or(u),i.push({dir:a,instance:s,value:u,oldValue:void 0,arg:c,modifiers:h}))}return e}function sn(e,t,s,i){const n=e.dirs,a=t&&t.dirs;for(let u=0;u<n.length;u++){const c=n[u];a&&(c.oldValue=a[u].value);let h=c.dir[i];h&&(tr(),Ps(h,s,8,[e.el,c,e,t]),sr())}}const Dd=Symbol("_vte"),xd=e=>e.__isTeleport,rn=e=>e&&(e.disabled||e.disabled===""),Od=e=>e&&(e.defer||e.defer===""),Sd=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Td=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,bl=(e,t)=>{const s=e&&e.to;if(ct(s))if(t){const i=t(s);return{}.NODE_ENV!=="production"&&!i&&!rn(e)&&Q(`Failed to locate Teleport target with selector "${s}". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`),i}else return{}.NODE_ENV!=="production"&&Q("Current renderer does not support string target for Teleports. (missing querySelector renderer option)"),null;else return{}.NODE_ENV!=="production"&&!s&&!rn(e)&&Q(`Invalid Teleport target: ${s}`),s},Nd={name:"Teleport",__isTeleport:!0,process(e,t,s,i,n,a,u,c,h,m){const{mc:p,pc:_,pbc:w,o:{insert:D,querySelector:V,createText:F,createComment:te}}=m,I=rn(t.props);let{shapeFlag:se,children:K,dynamicChildren:ye}=t;if({}.NODE_ENV!=="production"&&ks&&(h=!1,ye=null),e==null){const Z=t.el={}.NODE_ENV!=="production"?te("teleport start"):F(""),fe=t.anchor={}.NODE_ENV!=="production"?te("teleport end"):F("");D(Z,s,i),D(fe,s,i);const ve=(ae,A)=>{se&16&&(n&&n.isCE&&(n.ce._teleportTarget=ae),p(K,ae,A,n,a,u,c,h))},Ae=()=>{const ae=t.target=bl(t.props,V),A=Ad(ae,t,F,D);ae?(u!=="svg"&&Sd(ae)?u="svg":u!=="mathml"&&Td(ae)&&(u="mathml"),I||(ve(ae,A),xi(t,!1))):{}.NODE_ENV!=="production"&&!I&&Q("Invalid Teleport target on mount:",ae,`(${typeof ae})`)};I&&(ve(s,fe),xi(t,!0)),Od(t.props)?Jt(()=>{Ae(),t.el.__isMounted=!0},a):Ae()}else{if(Od(t.props)&&!e.el.__isMounted){Jt(()=>{Nd.process(e,t,s,i,n,a,u,c,h,m),delete e.el.__isMounted},a);return}t.el=e.el,t.targetStart=e.targetStart;const Z=t.anchor=e.anchor,fe=t.target=e.target,ve=t.targetAnchor=e.targetAnchor,Ae=rn(e.props),ae=Ae?s:fe,A=Ae?Z:ve;if(u==="svg"||Sd(fe)?u="svg":(u==="mathml"||Td(fe))&&(u="mathml"),ye?(w(e.dynamicChildren,ye,ae,n,a,u,c),bo(e,t,!0)):h||_(e,t,ae,A,n,a,u,c,!1),I)Ae?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Di(t,s,Z,m,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const be=t.target=bl(t.props,V);be?Di(t,be,null,m,0):{}.NODE_ENV!=="production"&&Q("Invalid Teleport target on update:",fe,`(${typeof fe})`)}else Ae&&Di(t,fe,ve,m,1);xi(t,I)}},remove(e,t,s,{um:i,o:{remove:n}},a){const{shapeFlag:u,children:c,anchor:h,targetStart:m,targetAnchor:p,target:_,props:w}=e;if(_&&(n(m),n(p)),a&&n(h),u&16){const D=a||!rn(w);for(let V=0;V<c.length;V++){const F=c[V];i(F,t,s,D,!!F.dynamicChildren)}}},move:Di,hydrate:Z_};function Di(e,t,s,{o:{insert:i},m:n},a=2){a===0&&i(e.targetAnchor,t,s);const{el:u,anchor:c,shapeFlag:h,children:m,props:p}=e,_=a===2;if(_&&i(u,t,s),(!_||rn(p))&&h&16)for(let w=0;w<m.length;w++)n(m[w],t,s,2);_&&i(c,t,s)}function Z_(e,t,s,i,n,a,{o:{nextSibling:u,parentNode:c,querySelector:h,insert:m,createText:p}},_){const w=t.target=bl(t.props,h);if(w){const D=rn(t.props),V=w._lpa||w.firstChild;if(t.shapeFlag&16)if(D)t.anchor=_(u(e),t,c(e),s,i,n,a),t.targetStart=V,t.targetAnchor=V&&u(V);else{t.anchor=u(e);let F=V;for(;F;){if(F&&F.nodeType===8){if(F.data==="teleport start anchor")t.targetStart=F;else if(F.data==="teleport anchor"){t.targetAnchor=F,w._lpa=t.targetAnchor&&u(t.targetAnchor);break}}F=u(F)}t.targetAnchor||Ad(w,t,p,m),_(V&&u(V),t,w,s,i,n,a)}xi(t,D)}return t.anchor&&u(t.anchor)}const J_=Nd;function xi(e,t){const s=e.ctx;if(s&&s.ut){let i,n;for(t?(i=e.el,n=e.anchor):(i=e.targetStart,n=e.targetAnchor);i&&i!==n;)i.nodeType===1&&i.setAttribute("data-v-owner",s.uid),i=i.nextSibling;s.ut()}}function Ad(e,t,s,i){const n=t.targetStart=s(""),a=t.targetAnchor=s("");return n[Dd]=a,e&&(i(n,e),i(a,e)),a}const Ar=Symbol("_leaveCb"),Oi=Symbol("_enterCb");function X_(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Bd(()=>{e.isMounted=!0}),$d(()=>{e.isUnmounting=!0}),e}const ws=[Function,Array],Id={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:ws,onEnter:ws,onAfterEnter:ws,onEnterCancelled:ws,onBeforeLeave:ws,onLeave:ws,onAfterLeave:ws,onLeaveCancelled:ws,onBeforeAppear:ws,onAppear:ws,onAfterAppear:ws,onAppearCancelled:ws},Md=e=>{const t=e.subTree;return t.component?Md(t.component):t},ev={name:"BaseTransition",props:Id,setup(e,{slots:t}){const s=Vi(),i=X_();return()=>{const n=t.default&&Rd(t.default(),!0);if(!n||!n.length)return;const a=Pd(n),u=Ie(e),{mode:c}=u;if({}.NODE_ENV!=="production"&&c&&c!=="in-out"&&c!=="out-in"&&c!=="default"&&Q(`invalid <transition> mode: ${c}`),i.isLeaving)return El(a);const h=Vd(a);if(!h)return El(a);let m=wl(h,u,i,s,_=>m=_);h.type!==wt&&go(h,m);let p=s.subTree&&Vd(s.subTree);if(p&&p.type!==wt&&!ln(h,p)&&Md(s).type!==wt){let _=wl(p,u,i,s);if(go(p,_),c==="out-in"&&h.type!==wt)return i.isLeaving=!0,_.afterLeave=()=>{i.isLeaving=!1,s.job.flags&8||s.update(),delete _.afterLeave,p=void 0},El(a);c==="in-out"&&h.type!==wt?_.delayLeave=(w,D,V)=>{const F=kd(i,p);F[String(p.key)]=p,w[Ar]=()=>{D(),w[Ar]=void 0,delete m.delayedLeave,p=void 0},m.delayedLeave=()=>{V(),delete m.delayedLeave,p=void 0}}:p=void 0}else p&&(p=void 0);return a}}};function Pd(e){let t=e[0];if(e.length>1){let s=!1;for(const i of e)if(i.type!==wt){if({}.NODE_ENV!=="production"&&s){Q("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}if(t=i,s=!0,{}.NODE_ENV==="production")break}}return t}const tv=ev;function kd(e,t){const{leavingVNodes:s}=e;let i=s.get(t.type);return i||(i=Object.create(null),s.set(t.type,i)),i}function wl(e,t,s,i,n){const{appear:a,mode:u,persisted:c=!1,onBeforeEnter:h,onEnter:m,onAfterEnter:p,onEnterCancelled:_,onBeforeLeave:w,onLeave:D,onAfterLeave:V,onLeaveCancelled:F,onBeforeAppear:te,onAppear:I,onAfterAppear:se,onAppearCancelled:K}=t,ye=String(e.key),Z=kd(s,e),fe=(ae,A)=>{ae&&Ps(ae,i,9,A)},ve=(ae,A)=>{const be=A[1];fe(ae,A),me(ae)?ae.every(ue=>ue.length<=1)&&be():ae.length<=1&&be()},Ae={mode:u,persisted:c,beforeEnter(ae){let A=h;if(!s.isMounted)if(a)A=te||h;else return;ae[Ar]&&ae[Ar](!0);const be=Z[ye];be&&ln(e,be)&&be.el[Ar]&&be.el[Ar](),fe(A,[ae])},enter(ae){let A=m,be=p,ue=_;if(!s.isMounted)if(a)A=I||m,be=se||p,ue=K||_;else return;let Ge=!1;const _t=ae[Oi]=mt=>{Ge||(Ge=!0,mt?fe(ue,[ae]):fe(be,[ae]),Ae.delayedLeave&&Ae.delayedLeave(),ae[Oi]=void 0)};A?ve(A,[ae,_t]):_t()},leave(ae,A){const be=String(e.key);if(ae[Oi]&&ae[Oi](!0),s.isUnmounting)return A();fe(w,[ae]);let ue=!1;const Ge=ae[Ar]=_t=>{ue||(ue=!0,A(),_t?fe(F,[ae]):fe(V,[ae]),ae[Ar]=void 0,Z[be]===e&&delete Z[be])};Z[be]=e,D?ve(D,[ae,Ge]):Ge()},clone(ae){const A=wl(ae,t,s,i,n);return n&&n(A),A}};return Ae}function El(e){if(_o(e))return e=Ks(e),e.children=null,e}function Vd(e){if(!_o(e))return xd(e.type)&&e.children?Pd(e.children):e;if({}.NODE_ENV!=="production"&&e.component)return e.component.subTree;const{shapeFlag:t,children:s}=e;if(s){if(t&16)return s[0];if(t&32&&xe(s.default))return s.default()}}function go(e,t){e.shapeFlag&6&&e.component?(e.transition=t,go(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Rd(e,t=!1,s){let i=[],n=0;for(let a=0;a<e.length;a++){let u=e[a];const c=s==null?u.key:String(s)+String(u.key!=null?u.key:a);u.type===Me?(u.patchFlag&128&&n++,i=i.concat(Rd(u.children,t,c))):(t||u.type!==wt)&&i.push(c!=null?Ks(u,{key:c}):u)}if(n>1)for(let a=0;a<i.length;a++)i[a].patchFlag=-2;return i}/*! #__NO_SIDE_EFFECTS__ */function Ld(e,t){return xe(e)?(()=>pt({name:e.name},t,{setup:e}))():e}function Ud(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const sv=new WeakSet;function Si(e,t,s,i,n=!1){if(me(e)){e.forEach((V,F)=>Si(V,t&&(me(t)?t[F]:t),s,i,n));return}if(In(i)&&!n){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&Si(e,t,s,i.component.subTree);return}const a=i.shapeFlag&4?Li(i.component):i.el,u=n?null:a,{i:c,r:h}=e;if({}.NODE_ENV!=="production"&&!c){Q("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");return}const m=t&&t.r,p=c.refs===tt?c.refs={}:c.refs,_=c.setupState,w=Ie(_),D=_===tt?()=>!1:V=>({}).NODE_ENV!=="production"&&(Ye(w,V)&&!Dt(w[V])&&Q(`Template ref "${V}" used on a non-ref value. It will not work in the production build.`),sv.has(w[V]))?!1:Ye(w,V);if(m!=null&&m!==h&&(ct(m)?(p[m]=null,D(m)&&(_[m]=null)):Dt(m)&&(m.value=null)),xe(h))Tn(h,c,12,[u,p]);else{const V=ct(h),F=Dt(h);if(V||F){const te=()=>{if(e.f){const I=V?D(h)?_[h]:p[h]:h.value;n?me(I)&&Ka(I,a):me(I)?I.includes(a)||I.push(a):V?(p[h]=[a],D(h)&&(_[h]=p[h])):(h.value=[a],e.k&&(p[e.k]=h.value))}else V?(p[h]=u,D(h)&&(_[h]=u)):F?(h.value=u,e.k&&(p[e.k]=u)):{}.NODE_ENV!=="production"&&Q("Invalid template ref type:",h,`(${typeof h})`)};u?(te.id=-1,Jt(te,s)):te()}else({}).NODE_ENV!=="production"&&Q("Invalid template ref type:",h,`(${typeof h})`)}}no().requestIdleCallback,no().cancelIdleCallback;const In=e=>!!e.type.__asyncLoader,_o=e=>e.type.__isKeepAlive;function rv(e,t){Fd(e,"a",t)}function nv(e,t){Fd(e,"da",t)}function Fd(e,t,s=Nt){const i=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Ti(t,i,s),s){let n=s.parent;for(;n&&n.parent;)_o(n.parent.vnode)&&ov(i,t,s,n),n=n.parent}}function ov(e,t,s,i){const n=Ti(t,e,i,!0);jd(()=>{Ka(i[t],n)},s)}function Ti(e,t,s=Nt,i=!1){if(s){const n=s[e]||(s[e]=[]),a=t.__weh||(t.__weh=(...u)=>{tr();const c=xo(s),h=Ps(t,s,e,u);return c(),sr(),h});return i?n.unshift(a):n.push(a),a}else if({}.NODE_ENV!=="production"){const n=Qr(pl[e].replace(/ hook$/,""));Q(`${n} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const ir=e=>(t,s=Nt)=>{(!Oo||e==="sp")&&Ti(e,(...i)=>t(...i),s)},iv=ir("bm"),Bd=ir("m"),av=ir("bu"),lv=ir("u"),$d=ir("bum"),jd=ir("um"),uv=ir("sp"),cv=ir("rtg"),dv=ir("rtc");function fv(e,t=Nt){Ti("ec",e,t)}const Cl="components",hv="directives";function ee(e,t){return Hd(Cl,e,!0,t)||e}const pv=Symbol.for("v-ndc");function mv(e){return Hd(hv,e)}function Hd(e,t,s=!0,i=!1){const n=xt||Nt;if(n){const a=n.type;if(e===Cl){const c=jl(a,!1);if(c&&(c===t||c===Kt(t)||c===Yr(Kt(t))))return a}const u=qd(n[e]||a[e],t)||qd(n.appContext[e],t);if(!u&&i)return a;if({}.NODE_ENV!=="production"&&s&&!u){const c=e===Cl?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";Q(`Failed to resolve ${e.slice(0,-1)}: ${t}${c}`)}return u}else({}).NODE_ENV!=="production"&&Q(`resolve${Yr(e.slice(0,-1))} can only be used in render() or setup().`)}function qd(e,t){return e&&(e[t]||e[Kt(t)]||e[Yr(Kt(t))])}function dt(e,t,s,i){let n;const a=s&&s[i],u=me(e);if(u||ct(e)){const c=u&&Jr(e);let h=!1;c&&(h=!Yt(e),e=li(e)),n=new Array(e.length);for(let m=0,p=e.length;m<p;m++)n[m]=t(h?Ht(e[m]):e[m],m,void 0,a&&a[m])}else if(typeof e=="number"){({}).NODE_ENV!=="production"&&!Number.isInteger(e)&&Q(`The v-for range expect an integer value but got ${e}.`),n=new Array(e);for(let c=0;c<e;c++)n[c]=t(c+1,c,void 0,a&&a[c])}else if(Ze(e))if(e[Symbol.iterator])n=Array.from(e,(c,h)=>t(c,h,void 0,a&&a[h]));else{const c=Object.keys(e);n=new Array(c.length);for(let h=0,m=c.length;h<m;h++){const p=c[h];n[h]=t(e[p],p,h,a&&a[h])}}else n=[];return s&&(s[i]=n),n}function Vt(e,t,s={},i,n){if(xt.ce||xt.parent&&In(xt.parent)&&xt.parent.ce)return t!=="default"&&(s.name=t),O(),Rt(Me,null,[k("slot",s,i&&i())],64);let a=e[t];({}).NODE_ENV!=="production"&&a&&a.length>1&&(Q("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),a=()=>[]),a&&a._c&&(a._d=!1),O();const u=a&&zd(a(s)),c=s.key||u&&u.key,h=Rt(Me,{key:(c&&!Is(c)?c:`_${t}`)+(!u&&i?"_fb":"")},u||(i?i():[]),u&&e._===1?64:-2);return!n&&h.scopeId&&(h.slotScopeIds=[h.scopeId+"-s"]),a&&a._c&&(a._d=!0),h}function zd(e){return e.some(t=>an(t)?!(t.type===wt||t.type===Me&&!zd(t.children)):!0)?e:null}const Dl=e=>e?Of(e)?Li(e):Dl(e.parent):null,nn=pt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>({}).NODE_ENV!=="production"?zs(e.props):e.props,$attrs:e=>({}).NODE_ENV!=="production"?zs(e.attrs):e.attrs,$slots:e=>({}).NODE_ENV!=="production"?zs(e.slots):e.slots,$refs:e=>({}).NODE_ENV!=="production"?zs(e.refs):e.refs,$parent:e=>Dl(e.parent),$root:e=>Dl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Tl(e),$forceUpdate:e=>e.f||(e.f=()=>{bi(e.update)}),$nextTick:e=>e.n||(e.n=ml.bind(e.proxy)),$watch:e=>Kv.bind(e)}),xl=e=>e==="_"||e==="$",Ol=(e,t)=>e!==tt&&!e.__isScriptSetup&&Ye(e,t),Wd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:n,props:a,accessCache:u,type:c,appContext:h}=e;if({}.NODE_ENV!=="production"&&t==="__isVue")return!0;let m;if(t[0]!=="$"){const D=u[t];if(D!==void 0)switch(D){case 1:return i[t];case 2:return n[t];case 4:return s[t];case 3:return a[t]}else{if(Ol(i,t))return u[t]=1,i[t];if(n!==tt&&Ye(n,t))return u[t]=2,n[t];if((m=e.propsOptions[0])&&Ye(m,t))return u[t]=3,a[t];if(s!==tt&&Ye(s,t))return u[t]=4,s[t];Sl&&(u[t]=0)}}const p=nn[t];let _,w;if(p)return t==="$attrs"?(Tt(e.attrs,"get",""),{}.NODE_ENV!=="production"&&Pi()):{}.NODE_ENV!=="production"&&t==="$slots"&&Tt(e,"get",t),p(e);if((_=c.__cssModules)&&(_=_[t]))return _;if(s!==tt&&Ye(s,t))return u[t]=4,s[t];if(w=h.config.globalProperties,Ye(w,t))return w[t];({}).NODE_ENV!=="production"&&xt&&(!ct(t)||t.indexOf("__v")!==0)&&(n!==tt&&xl(t[0])&&Ye(n,t)?Q(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===xt&&Q(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,s){const{data:i,setupState:n,ctx:a}=e;return Ol(n,t)?(n[t]=s,!0):{}.NODE_ENV!=="production"&&n.__isScriptSetup&&Ye(n,t)?(Q(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):i!==tt&&Ye(i,t)?(i[t]=s,!0):Ye(e.props,t)?({}.NODE_ENV!=="production"&&Q(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?({}.NODE_ENV!=="production"&&Q(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):({}.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:s}):a[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:n,propsOptions:a}},u){let c;return!!s[u]||e!==tt&&Ye(e,u)||Ol(t,u)||(c=a[0])&&Ye(c,u)||Ye(i,u)||Ye(nn,u)||Ye(n.config.globalProperties,u)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:Ye(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};({}).NODE_ENV!=="production"&&(Wd.ownKeys=e=>(Q("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function gv(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(nn).forEach(s=>{Object.defineProperty(t,s,{configurable:!0,enumerable:!1,get:()=>nn[s](e),set:St})}),t}function _v(e){const{ctx:t,propsOptions:[s]}=e;s&&Object.keys(s).forEach(i=>{Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>e.props[i],set:St})})}function vv(e){const{ctx:t,setupState:s}=e;Object.keys(Ie(s)).forEach(i=>{if(!s.__isScriptSetup){if(xl(i[0])){Q(`setup() return property ${JSON.stringify(i)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s[i],set:St})}})}function Gd(e){return me(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function yv(){const e=Object.create(null);return(t,s)=>{e[s]?Q(`${t} property "${s}" is already defined in ${e[s]}.`):e[s]=t}}let Sl=!0;function bv(e){const t=Tl(e),s=e.proxy,i=e.ctx;Sl=!1,t.beforeCreate&&Kd(t.beforeCreate,e,"bc");const{data:n,computed:a,methods:u,watch:c,provide:h,inject:m,created:p,beforeMount:_,mounted:w,beforeUpdate:D,updated:V,activated:F,deactivated:te,beforeDestroy:I,beforeUnmount:se,destroyed:K,unmounted:ye,render:Z,renderTracked:fe,renderTriggered:ve,errorCaptured:Ae,serverPrefetch:ae,expose:A,inheritAttrs:be,components:ue,directives:Ge,filters:_t}=t,mt={}.NODE_ENV!=="production"?yv():null;if({}.NODE_ENV!=="production"){const[Oe]=e.propsOptions;if(Oe)for(const we in Oe)mt("Props",we)}if(m&&wv(m,i,mt),u)for(const Oe in u){const we=u[Oe];xe(we)?({}.NODE_ENV!=="production"?Object.defineProperty(i,Oe,{value:we.bind(s),configurable:!0,enumerable:!0,writable:!0}):i[Oe]=we.bind(s),{}.NODE_ENV!=="production"&&mt("Methods",Oe)):{}.NODE_ENV!=="production"&&Q(`Method "${Oe}" has type "${typeof we}" in the component definition. Did you reference the function correctly?`)}if(n){({}).NODE_ENV!=="production"&&!xe(n)&&Q("The data option must be a function. Plain object usage is no longer supported.");const Oe=n.call(s,s);if({}.NODE_ENV!=="production"&&Ya(Oe)&&Q("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!Ze(Oe))({}).NODE_ENV!=="production"&&Q("data() should return an object.");else if(e.data=fi(Oe),{}.NODE_ENV!=="production")for(const we in Oe)mt("Data",we),xl(we[0])||Object.defineProperty(i,we,{configurable:!0,enumerable:!0,get:()=>Oe[we],set:St})}if(Sl=!0,a)for(const Oe in a){const we=a[Oe],Ut=xe(we)?we.bind(s,s):xe(we.get)?we.get.bind(s,s):St;({}).NODE_ENV!=="production"&&Ut===St&&Q(`Computed property "${Oe}" has no getter.`);const es=!xe(we)&&xe(we.set)?we.set.bind(s):{}.NODE_ENV!=="production"?()=>{Q(`Write operation failed: computed property "${Oe}" is readonly.`)}:St,yt=Ls({get:Ut,set:es});Object.defineProperty(i,Oe,{enumerable:!0,configurable:!0,get:()=>yt.value,set:ce=>yt.value=ce}),{}.NODE_ENV!=="production"&&mt("Computed",Oe)}if(c)for(const Oe in c)Yd(c[Oe],i,s,Oe);if(h){const Oe=xe(h)?h.call(s):h;Reflect.ownKeys(Oe).forEach(we=>{Ai(we,Oe[we])})}p&&Kd(p,e,"c");function ft(Oe,we){me(we)?we.forEach(Ut=>Oe(Ut.bind(s))):we&&Oe(we.bind(s))}if(ft(iv,_),ft(Bd,w),ft(av,D),ft(lv,V),ft(rv,F),ft(nv,te),ft(fv,Ae),ft(dv,fe),ft(cv,ve),ft($d,se),ft(jd,ye),ft(uv,ae),me(A))if(A.length){const Oe=e.exposed||(e.exposed={});A.forEach(we=>{Object.defineProperty(Oe,we,{get:()=>s[we],set:Ut=>s[we]=Ut})})}else e.exposed||(e.exposed={});Z&&e.render===St&&(e.render=Z),be!=null&&(e.inheritAttrs=be),ue&&(e.components=ue),Ge&&(e.directives=Ge),ae&&Ud(e)}function wv(e,t,s=St){me(e)&&(e=Nl(e));for(const i in e){const n=e[i];let a;Ze(n)?"default"in n?a=Vs(n.from||i,n.default,!0):a=Vs(n.from||i):a=Vs(n),Dt(a)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>a.value,set:u=>a.value=u}):t[i]=a,{}.NODE_ENV!=="production"&&s("Inject",i)}}function Kd(e,t,s){Ps(me(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function Yd(e,t,s,i){let n=i.includes(".")?pf(s,i):()=>s[i];if(ct(e)){const a=t[e];xe(a)?Pn(n,a):{}.NODE_ENV!=="production"&&Q(`Invalid watch handler specified by key "${e}"`,a)}else if(xe(e))Pn(n,e.bind(s));else if(Ze(e))if(me(e))e.forEach(a=>Yd(a,t,s,i));else{const a=xe(e.handler)?e.handler.bind(s):t[e.handler];xe(a)?Pn(n,a,e):{}.NODE_ENV!=="production"&&Q(`Invalid watch handler specified by key "${e.handler}"`,a)}else({}).NODE_ENV!=="production"&&Q(`Invalid watch option: "${i}"`,e)}function Tl(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:n,optionsCache:a,config:{optionMergeStrategies:u}}=e.appContext,c=a.get(t);let h;return c?h=c:!n.length&&!s&&!i?h=t:(h={},n.length&&n.forEach(m=>Ni(h,m,u,!0)),Ni(h,t,u)),Ze(t)&&a.set(t,h),h}function Ni(e,t,s,i=!1){const{mixins:n,extends:a}=t;a&&Ni(e,a,s,!0),n&&n.forEach(u=>Ni(e,u,s,!0));for(const u in t)if(i&&u==="expose")({}).NODE_ENV!=="production"&&Q('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=Ev[u]||s&&s[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const Ev={data:Qd,props:Zd,emits:Zd,methods:vo,computed:vo,beforeCreate:Zt,created:Zt,beforeMount:Zt,mounted:Zt,beforeUpdate:Zt,updated:Zt,beforeDestroy:Zt,beforeUnmount:Zt,destroyed:Zt,unmounted:Zt,activated:Zt,deactivated:Zt,errorCaptured:Zt,serverPrefetch:Zt,components:vo,directives:vo,watch:Dv,provide:Qd,inject:Cv};function Qd(e,t){return t?e?function(){return pt(xe(e)?e.call(this,this):e,xe(t)?t.call(this,this):t)}:t:e}function Cv(e,t){return vo(Nl(e),Nl(t))}function Nl(e){if(me(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Zt(e,t){return e?[...new Set([].concat(e,t))]:t}function vo(e,t){return e?pt(Object.create(null),e,t):t}function Zd(e,t){return e?me(e)&&me(t)?[...new Set([...e,...t])]:pt(Object.create(null),Gd(e),Gd(t??{})):t}function Dv(e,t){if(!e)return t;if(!t)return e;const s=pt(Object.create(null),e);for(const i in t)s[i]=Zt(e[i],t[i]);return s}function Jd(){return{app:null,config:{isNativeTag:Bg,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let xv=0;function Ov(e,t){return function(i,n=null){xe(i)||(i=pt({},i)),n!=null&&!Ze(n)&&({}.NODE_ENV!=="production"&&Q("root props passed to app.mount() must be an object."),n=null);const a=Jd(),u=new WeakSet,c=[];let h=!1;const m=a.app={_uid:xv++,_component:i,_props:n,_container:null,_context:a,_instance:null,version:If,get config(){return a.config},set config(p){({}).NODE_ENV!=="production"&&Q("app.config cannot be replaced. Modify individual options instead.")},use(p,..._){return u.has(p)?{}.NODE_ENV!=="production"&&Q("Plugin has already been applied to target app."):p&&xe(p.install)?(u.add(p),p.install(m,..._)):xe(p)?(u.add(p),p(m,..._)):{}.NODE_ENV!=="production"&&Q('A plugin must either be a function or an object with an "install" function.'),m},mixin(p){return a.mixins.includes(p)?{}.NODE_ENV!=="production"&&Q("Mixin has already been applied to target app"+(p.name?`: ${p.name}`:"")):a.mixins.push(p),m},component(p,_){return{}.NODE_ENV!=="production"&&Bl(p,a.config),_?({}.NODE_ENV!=="production"&&a.components[p]&&Q(`Component "${p}" has already been registered in target app.`),a.components[p]=_,m):a.components[p]},directive(p,_){return{}.NODE_ENV!=="production"&&Cd(p),_?({}.NODE_ENV!=="production"&&a.directives[p]&&Q(`Directive "${p}" has already been registered in target app.`),a.directives[p]=_,m):a.directives[p]},mount(p,_,w){if(h)({}).NODE_ENV!=="production"&&Q("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`");else{({}).NODE_ENV!=="production"&&p.__vue_app__&&Q("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const D=m._ceVNode||k(i,n);return D.appContext=a,w===!0?w="svg":w===!1&&(w=void 0),{}.NODE_ENV!=="production"&&(a.reload=()=>{e(Ks(D),p,w)}),_&&t?t(D,p):e(D,p,w),h=!0,m._container=p,p.__vue_app__=m,{}.NODE_ENV!=="production"&&(m._instance=D.component,H_(m,If)),Li(D.component)}},onUnmount(p){({}).NODE_ENV!=="production"&&typeof p!="function"&&Q(`Expected function as first argument to app.onUnmount(), but got ${typeof p}`),c.push(p)},unmount(){h?(Ps(c,m._instance,16),e(null,m._container),{}.NODE_ENV!=="production"&&(m._instance=null,q_(m)),delete m._container.__vue_app__):{}.NODE_ENV!=="production"&&Q("Cannot unmount an app that is not mounted.")},provide(p,_){return{}.NODE_ENV!=="production"&&p in a.provides&&Q(`App already provides property with key "${String(p)}". It will be overwritten with the new value.`),a.provides[p]=_,m},runWithContext(p){const _=Mn;Mn=m;try{return p()}finally{Mn=_}}};return m}}let Mn=null;function Ai(e,t){if(!Nt)({}).NODE_ENV!=="production"&&Q("provide() can only be used inside setup().");else{let s=Nt.provides;const i=Nt.parent&&Nt.parent.provides;i===s&&(s=Nt.provides=Object.create(i)),s[e]=t}}function Vs(e,t,s=!1){const i=Nt||xt;if(i||Mn){const n=Mn?Mn._context.provides:i?i.parent==null?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&xe(t)?t.call(i&&i.proxy):t;({}).NODE_ENV!=="production"&&Q(`injection "${String(e)}" not found.`)}else({}).NODE_ENV!=="production"&&Q("inject() can only be used inside setup() or functional components.")}const Xd={},ef=()=>Object.create(Xd),tf=e=>Object.getPrototypeOf(e)===Xd;function Sv(e,t,s,i=!1){const n={},a=ef();e.propsDefaults=Object.create(null),sf(e,t,n,a);for(const u in e.propsOptions[0])u in n||(n[u]=void 0);({}).NODE_ENV!=="production"&&of(t||{},n,e),s?e.props=i?n:od(n):e.type.props?e.props=n:e.props=a,e.attrs=a}function Tv(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}function Nv(e,t,s,i){const{props:n,attrs:a,vnode:{patchFlag:u}}=e,c=Ie(n),[h]=e.propsOptions;let m=!1;if(!({}.NODE_ENV!=="production"&&Tv(e))&&(i||u>0)&&!(u&16)){if(u&8){const p=e.vnode.dynamicProps;for(let _=0;_<p.length;_++){let w=p[_];if(Mi(e.emitsOptions,w))continue;const D=t[w];if(h)if(Ye(a,w))D!==a[w]&&(a[w]=D,m=!0);else{const V=Kt(w);n[V]=Al(h,c,V,D,e,!1)}else D!==a[w]&&(a[w]=D,m=!0)}}}else{sf(e,t,n,a)&&(m=!0);let p;for(const _ in c)(!t||!Ye(t,_)&&((p=Or(_))===_||!Ye(t,p)))&&(h?s&&(s[_]!==void 0||s[p]!==void 0)&&(n[_]=Al(h,c,_,void 0,e,!0)):delete n[_]);if(a!==c)for(const _ in a)(!t||!Ye(t,_))&&(delete a[_],m=!0)}m&&qs(e.attrs,"set",""),{}.NODE_ENV!=="production"&&of(t||{},n,e)}function sf(e,t,s,i){const[n,a]=e.propsOptions;let u=!1,c;if(t)for(let h in t){if(ro(h))continue;const m=t[h];let p;n&&Ye(n,p=Kt(h))?!a||!a.includes(p)?s[p]=m:(c||(c={}))[p]=m:Mi(e.emitsOptions,h)||(!(h in i)||m!==i[h])&&(i[h]=m,u=!0)}if(a){const h=Ie(s),m=c||tt;for(let p=0;p<a.length;p++){const _=a[p];s[_]=Al(n,h,_,m[_],e,!Ye(m,_))}}return u}function Al(e,t,s,i,n,a){const u=e[s];if(u!=null){const c=Ye(u,"default");if(c&&i===void 0){const h=u.default;if(u.type!==Function&&!u.skipFactory&&xe(h)){const{propsDefaults:m}=n;if(s in m)i=m[s];else{const p=xo(n);i=m[s]=h.call(null,t),p()}}else i=h;n.ce&&n.ce._setProp(s,i)}u[0]&&(a&&!c?i=!1:u[1]&&(i===""||i===Or(s))&&(i=!0))}return i}const Av=new WeakMap;function rf(e,t,s=!1){const i=s?Av:t.propsCache,n=i.get(e);if(n)return n;const a=e.props,u={},c=[];let h=!1;if(!xe(e)){const p=_=>{h=!0;const[w,D]=rf(_,t,!0);pt(u,w),D&&c.push(...D)};!s&&t.mixins.length&&t.mixins.forEach(p),e.extends&&p(e.extends),e.mixins&&e.mixins.forEach(p)}if(!a&&!h)return Ze(e)&&i.set(e,Dn),Dn;if(me(a))for(let p=0;p<a.length;p++){({}).NODE_ENV!=="production"&&!ct(a[p])&&Q("props must be strings when using array syntax.",a[p]);const _=Kt(a[p]);nf(_)&&(u[_]=tt)}else if(a){({}).NODE_ENV!=="production"&&!Ze(a)&&Q("invalid props options",a);for(const p in a){const _=Kt(p);if(nf(_)){const w=a[p],D=u[_]=me(w)||xe(w)?{type:w}:pt({},w),V=D.type;let F=!1,te=!0;if(me(V))for(let I=0;I<V.length;++I){const se=V[I],K=xe(se)&&se.name;if(K==="Boolean"){F=!0;break}else K==="String"&&(te=!1)}else F=xe(V)&&V.name==="Boolean";D[0]=F,D[1]=te,(F||Ye(D,"default"))&&c.push(_)}}}const m=[u,c];return Ze(e)&&i.set(e,m),m}function nf(e){return e[0]!=="$"&&!ro(e)?!0:({}.NODE_ENV!=="production"&&Q(`Invalid prop name: "${e}" is a reserved property.`),!1)}function Iv(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function of(e,t,s){const i=Ie(t),n=s.propsOptions[0],a=Object.keys(e).map(u=>Kt(u));for(const u in n){let c=n[u];c!=null&&Mv(u,i[u],c,{}.NODE_ENV!=="production"?zs(i):i,!a.includes(u))}}function Mv(e,t,s,i,n){const{type:a,required:u,validator:c,skipCheck:h}=s;if(u&&n){Q('Missing required prop: "'+e+'"');return}if(!(t==null&&!u)){if(a!=null&&a!==!0&&!h){let m=!1;const p=me(a)?a:[a],_=[];for(let w=0;w<p.length&&!m;w++){const{valid:D,expectedType:V}=kv(t,p[w]);_.push(V||""),m=D}if(!m){Q(Vv(e,t,_));return}}c&&!c(t,i)&&Q('Invalid prop: custom validator check failed for prop "'+e+'".')}}const Pv=er("String,Number,Boolean,Function,Symbol,BigInt");function kv(e,t){let s;const i=Iv(t);if(i==="null")s=e===null;else if(Pv(i)){const n=typeof e;s=n===i.toLowerCase(),!s&&n==="object"&&(s=e instanceof t)}else i==="Object"?s=Ze(e):i==="Array"?s=me(e):s=e instanceof t;return{valid:s,expectedType:i}}function Vv(e,t,s){if(s.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let i=`Invalid prop: type check failed for prop "${e}". Expected ${s.map(Yr).join(" | ")}`;const n=s[0],a=Qa(t),u=af(t,n),c=af(t,a);return s.length===1&&lf(n)&&!Rv(n,a)&&(i+=` with value ${u}`),i+=`, got ${a} `,lf(a)&&(i+=`with value ${c}.`),i}function af(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function lf(e){return["string","number","boolean"].some(s=>e.toLowerCase()===s)}function Rv(...e){return e.some(t=>t.toLowerCase()==="boolean")}const uf=e=>e[0]==="_"||e==="$stable",Il=e=>me(e)?e.map(Rs):[Rs(e)],Lv=(e,t,s)=>{if(t._n)return t;const i=Ne((...n)=>({}.NODE_ENV!=="production"&&Nt&&(!s||s.root===Nt.root)&&Q(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),Il(t(...n))),s);return i._c=!1,i},cf=(e,t,s)=>{const i=e._ctx;for(const n in e){if(uf(n))continue;const a=e[n];if(xe(a))t[n]=Lv(n,a,i);else if(a!=null){({}).NODE_ENV!=="production"&&Q(`Non-function value encountered for slot "${n}". Prefer function slots for better performance.`);const u=Il(a);t[n]=()=>u}}},df=(e,t)=>{({}).NODE_ENV!=="production"&&!_o(e.vnode)&&Q("Non-function value encountered for default slot. Prefer function slots for better performance.");const s=Il(t);e.slots.default=()=>s},Ml=(e,t,s)=>{for(const i in t)(s||i!=="_")&&(e[i]=t[i])},Uv=(e,t,s)=>{const i=e.slots=ef();if(e.vnode.shapeFlag&32){const n=t._;n?(Ml(i,t,s),s&&ii(i,"_",n,!0)):cf(t,i)}else t&&df(e,t)},Fv=(e,t,s)=>{const{vnode:i,slots:n}=e;let a=!0,u=tt;if(i.shapeFlag&32){const c=t._;c?{}.NODE_ENV!=="production"&&ks?(Ml(n,t,s),qs(e,"set","$slots")):s&&c===1?a=!1:Ml(n,t,s):(a=!t.$stable,cf(t,n)),u=t}else t&&(df(e,t),u={default:1});if(a)for(const c in n)!uf(c)&&u[c]==null&&delete n[c]};let yo,Ir;function ar(e,t){e.appContext.config.performance&&Ii()&&Ir.mark(`vue-${t}-${e.uid}`),{}.NODE_ENV!=="production"&&K_(e,t,Ii()?Ir.now():Date.now())}function lr(e,t){if(e.appContext.config.performance&&Ii()){const s=`vue-${t}-${e.uid}`,i=s+":end";Ir.mark(i),Ir.measure(`<${Ui(e,e.type)}> ${t}`,s,i),Ir.clearMarks(s),Ir.clearMarks(i)}({}).NODE_ENV!=="production"&&Y_(e,t,Ii()?Ir.now():Date.now())}function Ii(){return yo!==void 0||(typeof window<"u"&&window.performance?(yo=!0,Ir=window.performance):yo=!1),yo}function Bv(){const e=[];if({}.NODE_ENV!=="production"&&e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`)}}const Jt=ty;function $v(e){return jv(e)}function jv(e,t){Bv();const s=no();s.__VUE__=!0,{}.NODE_ENV!=="production"&&yd(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:i,remove:n,patchProp:a,createElement:u,createText:c,createComment:h,setText:m,setElementText:p,parentNode:_,nextSibling:w,setScopeId:D=St,insertStaticContent:V}=e,F=(b,C,P,U=null,H=null,z=null,X=void 0,Y=null,J={}.NODE_ENV!=="production"&&ks?!1:!!C.dynamicChildren)=>{if(b===C)return;b&&!ln(b,C)&&(U=oe(b),ze(b,H,z,!0),b=null),C.patchFlag===-2&&(J=!1,C.dynamicChildren=null);const{type:W,ref:Ee,shapeFlag:re}=C;switch(W){case wo:te(b,C,P,U);break;case wt:I(b,C,P,U);break;case Eo:b==null?se(C,P,U,X):{}.NODE_ENV!=="production"&&K(b,C,P,X);break;case Me:Ge(b,C,P,U,H,z,X,Y,J);break;default:re&1?fe(b,C,P,U,H,z,X,Y,J):re&6?_t(b,C,P,U,H,z,X,Y,J):re&64||re&128?W.process(b,C,P,U,H,z,X,Y,J,ke):{}.NODE_ENV!=="production"&&Q("Invalid VNode type:",W,`(${typeof W})`)}Ee!=null&&H&&Si(Ee,b&&b.ref,z,C||b,!C)},te=(b,C,P,U)=>{if(b==null)i(C.el=c(C.children),P,U);else{const H=C.el=b.el;C.children!==b.children&&m(H,C.children)}},I=(b,C,P,U)=>{b==null?i(C.el=h(C.children||""),P,U):C.el=b.el},se=(b,C,P,U)=>{[b.el,b.anchor]=V(b.children,C,P,U,b.el,b.anchor)},K=(b,C,P,U)=>{if(C.children!==b.children){const H=w(b.anchor);Z(b),[C.el,C.anchor]=V(C.children,P,H,U)}else C.el=b.el,C.anchor=b.anchor},ye=({el:b,anchor:C},P,U)=>{let H;for(;b&&b!==C;)H=w(b),i(b,P,U),b=H;i(C,P,U)},Z=({el:b,anchor:C})=>{let P;for(;b&&b!==C;)P=w(b),n(b),b=P;n(C)},fe=(b,C,P,U,H,z,X,Y,J)=>{C.type==="svg"?X="svg":C.type==="math"&&(X="mathml"),b==null?ve(C,P,U,H,z,X,Y,J):A(b,C,H,z,X,Y,J)},ve=(b,C,P,U,H,z,X,Y)=>{let J,W;const{props:Ee,shapeFlag:re,transition:_e,dirs:Ce}=b;if(J=b.el=u(b.type,z,Ee&&Ee.is,Ee),re&8?p(J,b.children):re&16&&ae(b.children,J,null,U,H,Pl(b,z),X,Y),Ce&&sn(b,null,U,"created"),Ae(J,b,b.scopeId,X,U),Ee){for(const Xe in Ee)Xe!=="value"&&!ro(Xe)&&a(J,Xe,null,Ee[Xe],z,U);"value"in Ee&&a(J,"value",null,Ee.value,z),(W=Ee.onVnodeBeforeMount)&&Ys(W,U,b)}({}).NODE_ENV!=="production"&&(ii(J,"__vnode",b,!0),ii(J,"__vueParentComponent",U,!0)),Ce&&sn(b,null,U,"beforeMount");const Ue=Hv(H,_e);Ue&&_e.beforeEnter(J),i(J,C,P),((W=Ee&&Ee.onVnodeMounted)||Ue||Ce)&&Jt(()=>{W&&Ys(W,U,b),Ue&&_e.enter(J),Ce&&sn(b,null,U,"mounted")},H)},Ae=(b,C,P,U,H)=>{if(P&&D(b,P),U)for(let z=0;z<U.length;z++)D(b,U[z]);if(H){let z=H.subTree;if({}.NODE_ENV!=="production"&&z.patchFlag>0&&z.patchFlag&2048&&(z=Ll(z.children)||z),C===z||yf(z.type)&&(z.ssContent===C||z.ssFallback===C)){const X=H.vnode;Ae(b,X,X.scopeId,X.slotScopeIds,H.parent)}}},ae=(b,C,P,U,H,z,X,Y,J=0)=>{for(let W=J;W<b.length;W++){const Ee=b[W]=Y?Mr(b[W]):Rs(b[W]);F(null,Ee,C,P,U,H,z,X,Y)}},A=(b,C,P,U,H,z,X)=>{const Y=C.el=b.el;({}).NODE_ENV!=="production"&&(Y.__vnode=C);let{patchFlag:J,dynamicChildren:W,dirs:Ee}=C;J|=b.patchFlag&16;const re=b.props||tt,_e=C.props||tt;let Ce;if(P&&on(P,!1),(Ce=_e.onVnodeBeforeUpdate)&&Ys(Ce,P,C,b),Ee&&sn(C,b,P,"beforeUpdate"),P&&on(P,!0),{}.NODE_ENV!=="production"&&ks&&(J=0,X=!1,W=null),(re.innerHTML&&_e.innerHTML==null||re.textContent&&_e.textContent==null)&&p(Y,""),W?(be(b.dynamicChildren,W,Y,P,U,Pl(C,H),z),{}.NODE_ENV!=="production"&&bo(b,C)):X||Ut(b,C,Y,null,P,U,Pl(C,H),z,!1),J>0){if(J&16)ue(Y,re,_e,P,H);else if(J&2&&re.class!==_e.class&&a(Y,"class",null,_e.class,H),J&4&&a(Y,"style",re.style,_e.style,H),J&8){const Ue=C.dynamicProps;for(let Xe=0;Xe<Ue.length;Xe++){const Qe=Ue[Xe],Ft=re[Qe],Ot=_e[Qe];(Ot!==Ft||Qe==="value")&&a(Y,Qe,Ft,Ot,H,P)}}J&1&&b.children!==C.children&&p(Y,C.children)}else!X&&W==null&&ue(Y,re,_e,P,H);((Ce=_e.onVnodeUpdated)||Ee)&&Jt(()=>{Ce&&Ys(Ce,P,C,b),Ee&&sn(C,b,P,"updated")},U)},be=(b,C,P,U,H,z,X)=>{for(let Y=0;Y<C.length;Y++){const J=b[Y],W=C[Y],Ee=J.el&&(J.type===Me||!ln(J,W)||J.shapeFlag&70)?_(J.el):P;F(J,W,Ee,null,U,H,z,X,!0)}},ue=(b,C,P,U,H)=>{if(C!==P){if(C!==tt)for(const z in C)!ro(z)&&!(z in P)&&a(b,z,C[z],null,H,U);for(const z in P){if(ro(z))continue;const X=P[z],Y=C[z];X!==Y&&z!=="value"&&a(b,z,Y,X,H,U)}"value"in P&&a(b,"value",C.value,P.value,H)}},Ge=(b,C,P,U,H,z,X,Y,J)=>{const W=C.el=b?b.el:c(""),Ee=C.anchor=b?b.anchor:c("");let{patchFlag:re,dynamicChildren:_e,slotScopeIds:Ce}=C;({}).NODE_ENV!=="production"&&(ks||re&2048)&&(re=0,J=!1,_e=null),Ce&&(Y=Y?Y.concat(Ce):Ce),b==null?(i(W,P,U),i(Ee,P,U),ae(C.children||[],P,Ee,H,z,X,Y,J)):re>0&&re&64&&_e&&b.dynamicChildren?(be(b.dynamicChildren,_e,P,H,z,X,Y),{}.NODE_ENV!=="production"?bo(b,C):(C.key!=null||H&&C===H.subTree)&&bo(b,C,!0)):Ut(b,C,P,Ee,H,z,X,Y,J)},_t=(b,C,P,U,H,z,X,Y,J)=>{C.slotScopeIds=Y,b==null?C.shapeFlag&512?H.ctx.activate(C,P,U,X,J):mt(C,P,U,H,z,X,J):ft(b,C,J)},mt=(b,C,P,U,H,z,X)=>{const Y=b.component=uy(b,U,H);if({}.NODE_ENV!=="production"&&Y.type.__hmrId&&F_(Y),{}.NODE_ENV!=="production"&&(_i(b),ar(Y,"mount")),_o(b)&&(Y.ctx.renderer=ke),{}.NODE_ENV!=="production"&&ar(Y,"init"),dy(Y,!1,X),{}.NODE_ENV!=="production"&&lr(Y,"init"),Y.asyncDep){if({}.NODE_ENV!=="production"&&ks&&(b.el=null),H&&H.registerDep(Y,Oe,X),!b.el){const J=Y.subTree=k(wt);I(null,J,C,P)}}else Oe(Y,b,C,P,H,z,X);({}).NODE_ENV!=="production"&&(vi(),lr(Y,"mount"))},ft=(b,C,P)=>{const U=C.component=b.component;if(Xv(b,C,P))if(U.asyncDep&&!U.asyncResolved){({}).NODE_ENV!=="production"&&_i(C),we(U,C,P),{}.NODE_ENV!=="production"&&vi();return}else U.next=C,U.update();else C.el=b.el,U.vnode=C},Oe=(b,C,P,U,H,z,X)=>{const Y=()=>{if(b.isMounted){let{next:re,bu:_e,u:Ce,parent:Ue,vnode:Xe}=b;{const Bt=ff(b);if(Bt){re&&(re.el=Xe.el,we(b,re,X)),Bt.asyncDep.then(()=>{b.isUnmounted||Y()});return}}let Qe=re,Ft;({}).NODE_ENV!=="production"&&_i(re||b.vnode),on(b,!1),re?(re.el=Xe.el,we(b,re,X)):re=Xe,_e&&On(_e),(Ft=re.props&&re.props.onVnodeBeforeUpdate)&&Ys(Ft,Ue,re,Xe),on(b,!0),{}.NODE_ENV!=="production"&&ar(b,"render");const Ot=Rl(b);({}).NODE_ENV!=="production"&&lr(b,"render");const ts=b.subTree;b.subTree=Ot,{}.NODE_ENV!=="production"&&ar(b,"patch"),F(ts,Ot,_(ts.el),oe(ts),b,H,z),{}.NODE_ENV!=="production"&&lr(b,"patch"),re.el=Ot.el,Qe===null&&ey(b,Ot.el),Ce&&Jt(Ce,H),(Ft=re.props&&re.props.onVnodeUpdated)&&Jt(()=>Ys(Ft,Ue,re,Xe),H),{}.NODE_ENV!=="production"&&bd(b),{}.NODE_ENV!=="production"&&vi()}else{let re;const{el:_e,props:Ce}=C,{bm:Ue,m:Xe,parent:Qe,root:Ft,type:Ot}=b,ts=In(C);if(on(b,!1),Ue&&On(Ue),!ts&&(re=Ce&&Ce.onVnodeBeforeMount)&&Ys(re,Qe,C),on(b,!0),_e&&Ve){const Bt=()=>{({}).NODE_ENV!=="production"&&ar(b,"render"),b.subTree=Rl(b),{}.NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"hydrate"),Ve(_e,b.subTree,b,H,null),{}.NODE_ENV!=="production"&&lr(b,"hydrate")};ts&&Ot.__asyncHydrate?Ot.__asyncHydrate(_e,b,Bt):Bt()}else{Ft.ce&&Ft.ce._injectChildStyle(Ot),{}.NODE_ENV!=="production"&&ar(b,"render");const Bt=b.subTree=Rl(b);({}).NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"patch"),F(null,Bt,P,U,b,H,z),{}.NODE_ENV!=="production"&&lr(b,"patch"),C.el=Bt.el}if(Xe&&Jt(Xe,H),!ts&&(re=Ce&&Ce.onVnodeMounted)){const Bt=C;Jt(()=>Ys(re,Qe,Bt),H)}(C.shapeFlag&256||Qe&&In(Qe.vnode)&&Qe.vnode.shapeFlag&256)&&b.a&&Jt(b.a,H),b.isMounted=!0,{}.NODE_ENV!=="production"&&z_(b),C=P=U=null}};b.scope.on();const J=b.effect=new Bc(Y);b.scope.off();const W=b.update=J.run.bind(J),Ee=b.job=J.runIfDirty.bind(J);Ee.i=b,Ee.id=b.uid,J.scheduler=()=>bi(Ee),on(b,!0),{}.NODE_ENV!=="production"&&(J.onTrack=b.rtc?re=>On(b.rtc,re):void 0,J.onTrigger=b.rtg?re=>On(b.rtg,re):void 0),W()},we=(b,C,P)=>{C.component=b;const U=b.vnode.props;b.vnode=C,b.next=null,Nv(b,C.props,U,P),Fv(b,C.children,P),tr(),pd(b),sr()},Ut=(b,C,P,U,H,z,X,Y,J=!1)=>{const W=b&&b.children,Ee=b?b.shapeFlag:0,re=C.children,{patchFlag:_e,shapeFlag:Ce}=C;if(_e>0){if(_e&128){yt(W,re,P,U,H,z,X,Y,J);return}else if(_e&256){es(W,re,P,U,H,z,X,Y,J);return}}Ce&8?(Ee&16&&R(W,H,z),re!==W&&p(P,re)):Ee&16?Ce&16?yt(W,re,P,U,H,z,X,Y,J):R(W,H,z,!0):(Ee&8&&p(P,""),Ce&16&&ae(re,P,U,H,z,X,Y,J))},es=(b,C,P,U,H,z,X,Y,J)=>{b=b||Dn,C=C||Dn;const W=b.length,Ee=C.length,re=Math.min(W,Ee);let _e;for(_e=0;_e<re;_e++){const Ce=C[_e]=J?Mr(C[_e]):Rs(C[_e]);F(b[_e],Ce,P,null,H,z,X,Y,J)}W>Ee?R(b,H,z,!0,!1,re):ae(C,P,U,H,z,X,Y,J,re)},yt=(b,C,P,U,H,z,X,Y,J)=>{let W=0;const Ee=C.length;let re=b.length-1,_e=Ee-1;for(;W<=re&&W<=_e;){const Ce=b[W],Ue=C[W]=J?Mr(C[W]):Rs(C[W]);if(ln(Ce,Ue))F(Ce,Ue,P,null,H,z,X,Y,J);else break;W++}for(;W<=re&&W<=_e;){const Ce=b[re],Ue=C[_e]=J?Mr(C[_e]):Rs(C[_e]);if(ln(Ce,Ue))F(Ce,Ue,P,null,H,z,X,Y,J);else break;re--,_e--}if(W>re){if(W<=_e){const Ce=_e+1,Ue=Ce<Ee?C[Ce].el:U;for(;W<=_e;)F(null,C[W]=J?Mr(C[W]):Rs(C[W]),P,Ue,H,z,X,Y,J),W++}}else if(W>_e)for(;W<=re;)ze(b[W],H,z,!0),W++;else{const Ce=W,Ue=W,Xe=new Map;for(W=Ue;W<=_e;W++){const At=C[W]=J?Mr(C[W]):Rs(C[W]);At.key!=null&&({}.NODE_ENV!=="production"&&Xe.has(At.key)&&Q("Duplicate keys found during update:",JSON.stringify(At.key),"Make sure keys are unique."),Xe.set(At.key,W))}let Qe,Ft=0;const Ot=_e-Ue+1;let ts=!1,Bt=0;const gr=new Array(Ot);for(W=0;W<Ot;W++)gr[W]=0;for(W=Ce;W<=re;W++){const At=b[W];if(Ft>=Ot){ze(At,H,z,!0);continue}let Es;if(At.key!=null)Es=Xe.get(At.key);else for(Qe=Ue;Qe<=_e;Qe++)if(gr[Qe-Ue]===0&&ln(At,C[Qe])){Es=Qe;break}Es===void 0?ze(At,H,z,!0):(gr[Es-Ue]=W+1,Es>=Bt?Bt=Es:ts=!0,F(At,C[Es],P,null,H,z,X,Y,J),Ft++)}const Bn=ts?qv(gr):Dn;for(Qe=Bn.length-1,W=Ot-1;W>=0;W--){const At=Ue+W,Es=C[At],oa=At+1<Ee?C[At+1].el:U;gr[W]===0?F(null,Es,P,oa,H,z,X,Y,J):ts&&(Qe<0||W!==Bn[Qe]?ce(Es,P,oa,2):Qe--)}}},ce=(b,C,P,U,H=null)=>{const{el:z,type:X,transition:Y,children:J,shapeFlag:W}=b;if(W&6){ce(b.component.subTree,C,P,U);return}if(W&128){b.suspense.move(C,P,U);return}if(W&64){X.move(b,C,P,ke);return}if(X===Me){i(z,C,P);for(let re=0;re<J.length;re++)ce(J[re],C,P,U);i(b.anchor,C,P);return}if(X===Eo){ye(b,C,P);return}if(U!==2&&W&1&&Y)if(U===0)Y.beforeEnter(z),i(z,C,P),Jt(()=>Y.enter(z),H);else{const{leave:re,delayLeave:_e,afterLeave:Ce}=Y,Ue=()=>i(z,C,P),Xe=()=>{re(z,()=>{Ue(),Ce&&Ce()})};_e?_e(z,Ue,Xe):Xe()}else i(z,C,P)},ze=(b,C,P,U=!1,H=!1)=>{const{type:z,props:X,ref:Y,children:J,dynamicChildren:W,shapeFlag:Ee,patchFlag:re,dirs:_e,cacheIndex:Ce}=b;if(re===-2&&(H=!1),Y!=null&&Si(Y,null,P,b,!0),Ce!=null&&(C.renderCache[Ce]=void 0),Ee&256){C.ctx.deactivate(b);return}const Ue=Ee&1&&_e,Xe=!In(b);let Qe;if(Xe&&(Qe=X&&X.onVnodeBeforeUnmount)&&Ys(Qe,C,b),Ee&6)ps(b.component,P,U);else{if(Ee&128){b.suspense.unmount(P,U);return}Ue&&sn(b,null,C,"beforeUnmount"),Ee&64?b.type.remove(b,C,P,ke,U):W&&!W.hasOnce&&(z!==Me||re>0&&re&64)?R(W,C,P,!1,!0):(z===Me&&re&384||!H&&Ee&16)&&R(J,C,P),U&&hs(b)}(Xe&&(Qe=X&&X.onVnodeUnmounted)||Ue)&&Jt(()=>{Qe&&Ys(Qe,C,b),Ue&&sn(b,null,C,"unmounted")},P)},hs=b=>{const{type:C,el:P,anchor:U,transition:H}=b;if(C===Me){({}).NODE_ENV!=="production"&&b.patchFlag>0&&b.patchFlag&2048&&H&&!H.persisted?b.children.forEach(X=>{X.type===wt?n(X.el):hs(X)}):zt(P,U);return}if(C===Eo){Z(b);return}const z=()=>{n(P),H&&!H.persisted&&H.afterLeave&&H.afterLeave()};if(b.shapeFlag&1&&H&&!H.persisted){const{leave:X,delayLeave:Y}=H,J=()=>X(P,z);Y?Y(b.el,z,J):J()}else z()},zt=(b,C)=>{let P;for(;b!==C;)P=w(b),n(b),b=P;n(C)},ps=(b,C,P)=>{({}).NODE_ENV!=="production"&&b.type.__hmrId&&B_(b);const{bum:U,scope:H,job:z,subTree:X,um:Y,m:J,a:W}=b;hf(J),hf(W),U&&On(U),H.stop(),z&&(z.flags|=8,ze(X,b,C,P)),Y&&Jt(Y,C),Jt(()=>{b.isUnmounted=!0},C),C&&C.pendingBranch&&!C.isUnmounted&&b.asyncDep&&!b.asyncResolved&&b.suspenseId===C.pendingId&&(C.deps--,C.deps===0&&C.resolve()),{}.NODE_ENV!=="production"&&G_(b)},R=(b,C,P,U=!1,H=!1,z=0)=>{for(let X=z;X<b.length;X++)ze(b[X],C,P,U,H)},oe=b=>{if(b.shapeFlag&6)return oe(b.component.subTree);if(b.shapeFlag&128)return b.suspense.next();const C=w(b.anchor||b.el),P=C&&C[Dd];return P?w(P):C};let ne=!1;const pe=(b,C,P)=>{b==null?C._vnode&&ze(C._vnode,null,null,!0):F(C._vnode||null,b,C,null,null,null,P),C._vnode=b,ne||(ne=!0,pd(),md(),ne=!1)},ke={p:F,um:ze,m:ce,r:hs,mt,mc:ae,pc:Ut,pbc:be,n:oe,o:e};let ot,Ve;return t&&([ot,Ve]=t(ke)),{render:pe,hydrate:ot,createApp:Ov(pe,ot)}}function Pl({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function on({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Hv(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function bo(e,t,s=!1){const i=e.children,n=t.children;if(me(i)&&me(n))for(let a=0;a<i.length;a++){const u=i[a];let c=n[a];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=n[a]=Mr(n[a]),c.el=u.el),!s&&c.patchFlag!==-2&&bo(u,c)),c.type===wo&&(c.el=u.el),{}.NODE_ENV!=="production"&&c.type===wt&&!c.el&&(c.el=u.el)}}function qv(e){const t=e.slice(),s=[0];let i,n,a,u,c;const h=e.length;for(i=0;i<h;i++){const m=e[i];if(m!==0){if(n=s[s.length-1],e[n]<m){t[i]=n,s.push(i);continue}for(a=0,u=s.length-1;a<u;)c=a+u>>1,e[s[c]]<m?a=c+1:u=c;m<e[s[a]]&&(a>0&&(t[i]=s[a-1]),s[a]=i)}}for(a=s.length,u=s[a-1];a-- >0;)s[a]=u,u=t[u];return s}function ff(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ff(t)}function hf(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const zv=Symbol.for("v-scx"),Wv=()=>{{const e=Vs(zv);return e||{}.NODE_ENV!=="production"&&Q("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function Gv(e,t){return kl(e,null,t)}function Pn(e,t,s){return{}.NODE_ENV!=="production"&&!xe(t)&&Q("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),kl(e,t,s)}function kl(e,t,s=tt){const{immediate:i,deep:n,flush:a,once:u}=s;({}).NODE_ENV!=="production"&&!t&&(i!==void 0&&Q('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),n!==void 0&&Q('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),u!==void 0&&Q('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=pt({},s);({}).NODE_ENV!=="production"&&(c.onWarn=Q);const h=t&&i||!t&&a!=="post";let m;if(Oo){if(a==="sync"){const D=Wv();m=D.__watcherHandles||(D.__watcherHandles=[])}else if(!h){const D=()=>{};return D.stop=St,D.resume=St,D.pause=St,D}}const p=Nt;c.call=(D,V,F)=>Ps(D,p,V,F);let _=!1;a==="post"?c.scheduler=D=>{Jt(D,p&&p.suspense)}:a!=="sync"&&(_=!0,c.scheduler=(D,V)=>{V?D():bi(D)}),c.augmentJob=D=>{t&&(D.flags|=4),_&&(D.flags|=2,p&&(D.id=p.uid,D.i=p))};const w=A_(e,t,c);return Oo&&(m?m.push(w):h&&w()),w}function Kv(e,t,s){const i=this.proxy,n=ct(e)?e.includes(".")?pf(i,e):()=>i[e]:e.bind(i,i);let a;xe(t)?a=t:(a=t.handler,s=t);const u=xo(this),c=kl(n,a.bind(i),s);return u(),c}function pf(e,t){const s=t.split(".");return()=>{let i=e;for(let n=0;n<s.length&&i;n++)i=i[s[n]];return i}}const Yv=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Kt(t)}Modifiers`]||e[`${Or(t)}Modifiers`];function Qv(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||tt;if({}.NODE_ENV!=="production"){const{emitsOptions:p,propsOptions:[_]}=e;if(p)if(!(t in p))(!_||!(Qr(Kt(t))in _))&&Q(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${Qr(Kt(t))}" prop.`);else{const w=p[t];xe(w)&&(w(...s)||Q(`Invalid event arguments: event validation failed for event "${t}".`))}}let n=s;const a=t.startsWith("update:"),u=a&&Yv(i,t.slice(7));if(u&&(u.trim&&(n=s.map(p=>ct(p)?p.trim():p)),u.number&&(n=s.map(ai))),{}.NODE_ENV!=="production"&&Q_(e,t,n),{}.NODE_ENV!=="production"){const p=t.toLowerCase();p!==t&&i[Qr(p)]&&Q(`Event "${p}" is emitted in component ${Ui(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${Or(t)}" instead of "${t}".`)}let c,h=i[c=Qr(t)]||i[c=Qr(Kt(t))];!h&&a&&(h=i[c=Qr(Or(t))]),h&&Ps(h,e,6,n);const m=i[c+"Once"];if(m){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Ps(m,e,6,n)}}function mf(e,t,s=!1){const i=t.emitsCache,n=i.get(e);if(n!==void 0)return n;const a=e.emits;let u={},c=!1;if(!xe(e)){const h=m=>{const p=mf(m,t,!0);p&&(c=!0,pt(u,p))};!s&&t.mixins.length&&t.mixins.forEach(h),e.extends&&h(e.extends),e.mixins&&e.mixins.forEach(h)}return!a&&!c?(Ze(e)&&i.set(e,null),null):(me(a)?a.forEach(h=>u[h]=null):pt(u,a),Ze(e)&&i.set(e,u),u)}function Mi(e,t){return!e||!to(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ye(e,t[0].toLowerCase()+t.slice(1))||Ye(e,Or(t))||Ye(e,t))}let Vl=!1;function Pi(){Vl=!0}function Rl(e){const{type:t,vnode:s,proxy:i,withProxy:n,propsOptions:[a],slots:u,attrs:c,emit:h,render:m,renderCache:p,props:_,data:w,setupState:D,ctx:V,inheritAttrs:F}=e,te=Ci(e);let I,se;({}).NODE_ENV!=="production"&&(Vl=!1);try{if(s.shapeFlag&4){const Z=n||i,fe={}.NODE_ENV!=="production"&&D.__isScriptSetup?new Proxy(Z,{get(ve,Ae,ae){return Q(`Property '${String(Ae)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(ve,Ae,ae)}}):Z;I=Rs(m.call(fe,Z,p,{}.NODE_ENV!=="production"?zs(_):_,D,w,V)),se=c}else{const Z=t;({}).NODE_ENV!=="production"&&c===_&&Pi(),I=Rs(Z.length>1?Z({}.NODE_ENV!=="production"?zs(_):_,{}.NODE_ENV!=="production"?{get attrs(){return Pi(),zs(c)},slots:u,emit:h}:{attrs:c,slots:u,emit:h}):Z({}.NODE_ENV!=="production"?zs(_):_,null)),se=t.props?c:Zv(c)}}catch(Z){Co.length=0,fo(Z,e,1),I=k(wt)}let K=I,ye;if({}.NODE_ENV!=="production"&&I.patchFlag>0&&I.patchFlag&2048&&([K,ye]=gf(I)),se&&F!==!1){const Z=Object.keys(se),{shapeFlag:fe}=K;if(Z.length){if(fe&7)a&&Z.some(ni)&&(se=Jv(se,a)),K=Ks(K,se,!1,!0);else if({}.NODE_ENV!=="production"&&!Vl&&K.type!==wt){const ve=Object.keys(c),Ae=[],ae=[];for(let A=0,be=ve.length;A<be;A++){const ue=ve[A];to(ue)?ni(ue)||Ae.push(ue[2].toLowerCase()+ue.slice(3)):ae.push(ue)}ae.length&&Q(`Extraneous non-props attributes (${ae.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),Ae.length&&Q(`Extraneous non-emits event listeners (${Ae.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}}return s.dirs&&({}.NODE_ENV!=="production"&&!_f(K)&&Q("Runtime directive used on component with non-element root node. The directives will not function as intended."),K=Ks(K,null,!1,!0),K.dirs=K.dirs?K.dirs.concat(s.dirs):s.dirs),s.transition&&({}.NODE_ENV!=="production"&&!_f(K)&&Q("Component inside <Transition> renders non-element root node that cannot be animated."),go(K,s.transition)),{}.NODE_ENV!=="production"&&ye?ye(K):I=K,Ci(te),I}const gf=e=>{const t=e.children,s=e.dynamicChildren,i=Ll(t,!1);if(i){if({}.NODE_ENV!=="production"&&i.patchFlag>0&&i.patchFlag&2048)return gf(i)}else return[e,void 0];const n=t.indexOf(i),a=s?s.indexOf(i):-1,u=c=>{t[n]=c,s&&(a>-1?s[a]=c:c.patchFlag>0&&(e.dynamicChildren=[...s,c]))};return[Rs(i),u]};function Ll(e,t=!0){let s;for(let i=0;i<e.length;i++){const n=e[i];if(an(n)){if(n.type!==wt||n.children==="v-if"){if(s)return;if(s=n,{}.NODE_ENV!=="production"&&t&&s.patchFlag>0&&s.patchFlag&2048)return Ll(s.children)}}else return}return s}const Zv=e=>{let t;for(const s in e)(s==="class"||s==="style"||to(s))&&((t||(t={}))[s]=e[s]);return t},Jv=(e,t)=>{const s={};for(const i in e)(!ni(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s},_f=e=>e.shapeFlag&7||e.type===wt;function Xv(e,t,s){const{props:i,children:n,component:a}=e,{props:u,children:c,patchFlag:h}=t,m=a.emitsOptions;if({}.NODE_ENV!=="production"&&(n||c)&&ks||t.dirs||t.transition)return!0;if(s&&h>=0){if(h&1024)return!0;if(h&16)return i?vf(i,u,m):!!u;if(h&8){const p=t.dynamicProps;for(let _=0;_<p.length;_++){const w=p[_];if(u[w]!==i[w]&&!Mi(m,w))return!0}}}else return(n||c)&&(!c||!c.$stable)?!0:i===u?!1:i?u?vf(i,u,m):!0:!!u;return!1}function vf(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let n=0;n<i.length;n++){const a=i[n];if(t[a]!==e[a]&&!Mi(s,a))return!0}return!1}function ey({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const yf=e=>e.__isSuspense;function ty(e,t){t&&t.pendingBranch?me(e)?t.effects.push(...e):t.effects.push(e):hd(e)}const Me=Symbol.for("v-fgt"),wo=Symbol.for("v-txt"),wt=Symbol.for("v-cmt"),Eo=Symbol.for("v-stc"),Co=[];let ds=null;function O(e=!1){Co.push(ds=e?null:[])}function sy(){Co.pop(),ds=Co[Co.length-1]||null}let Do=1;function bf(e,t=!1){Do+=e,e<0&&ds&&t&&(ds.hasOnce=!0)}function wf(e){return e.dynamicChildren=Do>0?ds||Dn:null,sy(),Do>0&&ds&&ds.push(e),e}function N(e,t,s,i,n,a){return wf(f(e,t,s,i,n,a,!0))}function Rt(e,t,s,i,n){return wf(k(e,t,s,i,n,!0))}function an(e){return e?e.__v_isVNode===!0:!1}function ln(e,t){if({}.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const s=wi.get(t.type);if(s&&s.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const ry=(...e)=>Cf(...e),Ef=({key:e})=>e??null,ki=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?ct(e)||Dt(e)||xe(e)?{i:xt,r:e,k:t,f:!!s}:e:null);function f(e,t=null,s=null,i=0,n=null,a=e===Me?0:1,u=!1,c=!1){const h={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ef(t),ref:t&&ki(t),scopeId:Ed,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:i,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:xt};return c?(Ul(h,s),a&128&&e.normalize(h)):s&&(h.shapeFlag|=ct(s)?8:16),{}.NODE_ENV!=="production"&&h.key!==h.key&&Q("VNode created with invalid key (NaN). VNode type:",h.type),Do>0&&!u&&ds&&(h.patchFlag>0||a&6)&&h.patchFlag!==32&&ds.push(h),h}const k={}.NODE_ENV!=="production"?ry:Cf;function Cf(e,t=null,s=null,i=0,n=null,a=!1){if((!e||e===pv)&&({}.NODE_ENV!=="production"&&!e&&Q(`Invalid vnode type when creating vnode: ${e}.`),e=wt),an(e)){const c=Ks(e,t,!0);return s&&Ul(c,s),Do>0&&!a&&ds&&(c.shapeFlag&6?ds[ds.indexOf(e)]=c:ds.push(c)),c.patchFlag=-2,c}if(Af(e)&&(e=e.__vccOpts),t){t=ny(t);let{class:c,style:h}=t;c&&!ct(c)&&(t.class=he(c)),Ze(h)&&(pi(h)&&!me(h)&&(h=pt({},h)),t.style=us(h))}const u=ct(e)?1:yf(e)?128:xd(e)?64:Ze(e)?4:xe(e)?2:0;return{}.NODE_ENV!=="production"&&u&4&&pi(e)&&(e=Ie(e),Q("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),f(e,t,s,i,n,u,a,!0)}function ny(e){return e?pi(e)||tf(e)?pt({},e):e:null}function Ks(e,t,s=!1,i=!1){const{props:n,ref:a,patchFlag:u,children:c,transition:h}=e,m=t?iy(n||{},t):n,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:m,key:m&&Ef(m),ref:t&&t.ref?s&&a?me(a)?a.concat(ki(t)):[a,ki(t)]:ki(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:{}.NODE_ENV!=="production"&&u===-1&&me(c)?c.map(Df):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Me?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:h,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ks(e.ssContent),ssFallback:e.ssFallback&&Ks(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return h&&i&&go(p,h.clone(p)),p}function Df(e){const t=Ks(e);return me(e.children)&&(t.children=e.children.map(Df)),t}function nt(e=" ",t=0){return k(wo,null,e,t)}function oy(e,t){const s=k(Eo,null,e);return s.staticCount=t,s}function ie(e="",t=!1){return t?(O(),Rt(wt,null,e)):k(wt,null,e)}function Rs(e){return e==null||typeof e=="boolean"?k(wt):me(e)?k(Me,null,e.slice()):an(e)?Mr(e):k(wo,null,String(e))}function Mr(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ks(e)}function Ul(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(me(t))s=16;else if(typeof t=="object")if(i&65){const n=t.default;n&&(n._c&&(n._d=!1),Ul(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!tf(t)?t._ctx=xt:n===3&&xt&&(xt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else xe(t)?(t={default:t,_ctx:xt},s=32):(t=String(t),i&64?(s=16,t=[nt(t)]):s=8);e.children=t,e.shapeFlag|=s}function iy(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const n in i)if(n==="class")t.class!==i.class&&(t.class=he([t.class,i.class]));else if(n==="style")t.style=us([t.style,i.style]);else if(to(n)){const a=t[n],u=i[n];u&&a!==u&&!(me(a)&&a.includes(u))&&(t[n]=a?[].concat(a,u):u)}else n!==""&&(t[n]=i[n])}return t}function Ys(e,t,s,i=null){Ps(e,t,7,[s,i])}const ay=Jd();let ly=0;function uy(e,t,s){const i=e.type,n=(t?t.appContext:e.appContext)||ay,a={uid:ly++,vnode:e,type:i,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Fc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:rf(i,n),emitsOptions:mf(i,n),emit:null,emitted:null,propsDefaults:tt,inheritAttrs:i.inheritAttrs,ctx:tt,data:tt,props:tt,attrs:tt,slots:tt,refs:tt,setupState:tt,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return{}.NODE_ENV!=="production"?a.ctx=gv(a):a.ctx={_:a},a.root=t?t.root:a,a.emit=Qv.bind(null,a),e.ce&&e.ce(a),a}let Nt=null;const Vi=()=>Nt||xt;let Ri,Fl;{const e=no(),t=(s,i)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(i),a=>{n.length>1?n.forEach(u=>u(a)):n[0](a)}};Ri=t("__VUE_INSTANCE_SETTERS__",s=>Nt=s),Fl=t("__VUE_SSR_SETTERS__",s=>Oo=s)}const xo=e=>{const t=Nt;return Ri(e),e.scope.on(),()=>{e.scope.off(),Ri(t)}},xf=()=>{Nt&&Nt.scope.off(),Ri(null)},cy=er("slot,component");function Bl(e,{isNativeTag:t}){(cy(e)||t(e))&&Q("Do not use built-in or reserved HTML elements as component id: "+e)}function Of(e){return e.vnode.shapeFlag&4}let Oo=!1;function dy(e,t=!1,s=!1){t&&Fl(t);const{props:i,children:n}=e.vnode,a=Of(e);Sv(e,i,a,t),Uv(e,n,s);const u=a?fy(e,t):void 0;return t&&Fl(!1),u}function fy(e,t){var s;const i=e.type;if({}.NODE_ENV!=="production"){if(i.name&&Bl(i.name,e.appContext.config),i.components){const a=Object.keys(i.components);for(let u=0;u<a.length;u++)Bl(a[u],e.appContext.config)}if(i.directives){const a=Object.keys(i.directives);for(let u=0;u<a.length;u++)Cd(a[u])}i.compilerOptions&&hy()&&Q('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Wd),{}.NODE_ENV!=="production"&&_v(e);const{setup:n}=i;if(n){tr();const a=e.setupContext=n.length>1?my(e):null,u=xo(e),c=Tn(n,e,0,[{}.NODE_ENV!=="production"?zs(e.props):e.props,a]),h=Ya(c);if(sr(),u(),(h||e.sp)&&!In(e)&&Ud(e),h){if(c.then(xf,xf),t)return c.then(m=>{Sf(e,m,t)}).catch(m=>{fo(m,e,0)});if(e.asyncDep=c,{}.NODE_ENV!=="production"&&!e.suspense){const m=(s=i.name)!=null?s:"Anonymous";Q(`Component <${m}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else Sf(e,c,t)}else Tf(e,t)}function Sf(e,t,s){xe(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Ze(t)?({}.NODE_ENV!=="production"&&an(t)&&Q("setup() should not return VNodes directly - return a render function instead."),{}.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=ud(t),{}.NODE_ENV!=="production"&&vv(e)):{}.NODE_ENV!=="production"&&t!==void 0&&Q(`setup() should return an object. Received: ${t===null?"null":typeof t}`),Tf(e,s)}let $l;const hy=()=>!$l;function Tf(e,t,s){const i=e.type;if(!e.render){if(!t&&$l&&!i.render){const n=i.template||Tl(e).template;if(n){({}).NODE_ENV!=="production"&&ar(e,"compile");const{isCustomElement:a,compilerOptions:u}=e.appContext.config,{delimiters:c,compilerOptions:h}=i,m=pt(pt({isCustomElement:a,delimiters:c},u),h);i.render=$l(n,m),{}.NODE_ENV!=="production"&&lr(e,"compile")}}e.render=i.render||St}{const n=xo(e);tr();try{bv(e)}finally{sr(),n()}}({}).NODE_ENV!=="production"&&!i.render&&e.render===St&&!t&&(i.template?Q('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):Q("Component is missing template or render function: ",i))}const Nf={}.NODE_ENV!=="production"?{get(e,t){return Pi(),Tt(e,"get",""),e[t]},set(){return Q("setupContext.attrs is readonly."),!1},deleteProperty(){return Q("setupContext.attrs is readonly."),!1}}:{get(e,t){return Tt(e,"get",""),e[t]}};function py(e){return new Proxy(e.slots,{get(t,s){return Tt(e,"get","$slots"),t[s]}})}function my(e){const t=s=>{if({}.NODE_ENV!=="production"&&(e.exposed&&Q("expose() should be called only once per setup()."),s!=null)){let i=typeof s;i==="object"&&(me(s)?i="array":Dt(s)&&(i="ref")),i!=="object"&&Q(`expose() should be passed a plain object, received ${i}.`)}e.exposed=s||{}};if({}.NODE_ENV!=="production"){let s,i;return Object.freeze({get attrs(){return s||(s=new Proxy(e.attrs,Nf))},get slots(){return i||(i=py(e))},get emit(){return(n,...a)=>e.emit(n,...a)},expose:t})}else return{attrs:new Proxy(e.attrs,Nf),slots:e.slots,emit:e.emit,expose:t}}function Li(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ud(dl(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in nn)return nn[s](e)},has(t,s){return s in t||s in nn}})):e.proxy}const gy=/(?:^|[-_])(\w)/g,_y=e=>e.replace(gy,t=>t.toUpperCase()).replace(/[-_]/g,"");function jl(e,t=!0){return xe(e)?e.displayName||e.name:e.name||t&&e.__name}function Ui(e,t,s=!1){let i=jl(t);if(!i&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(i=n[1])}if(!i&&e&&e.parent){const n=a=>{for(const u in a)if(a[u]===t)return u};i=n(e.components||e.parent.type.components)||n(e.appContext.components)}return i?_y(i):s?"App":"Anonymous"}function Af(e){return xe(e)&&"__vccOpts"in e}const Ls=(e,t)=>{const s=T_(e,t,Oo);if({}.NODE_ENV!=="production"){const i=Vi();i&&i.appContext.config.warnRecursiveComputed&&(s._warnRecursive=!0)}return s};function Hl(e,t,s){const i=arguments.length;return i===2?Ze(t)&&!me(t)?an(t)?k(e,null,[t]):k(e,t):k(e,null,t):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&an(s)&&(s=[s]),k(e,t,s))}function vy(){if({}.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},s={style:"color:#f5222d"},i={style:"color:#eb2f96"},n={__vue_custom_formatter:!0,header(_){return Ze(_)?_.__isVue?["div",e,"VueInstance"]:Dt(_)?["div",{},["span",e,p(_)],"<",c("_value"in _?_._value:_),">"]:Jr(_)?["div",{},["span",e,Yt(_)?"ShallowReactive":"Reactive"],"<",c(_),`>${nr(_)?" (readonly)":""}`]:nr(_)?["div",{},["span",e,Yt(_)?"ShallowReadonly":"Readonly"],"<",c(_),">"]:null:null},hasBody(_){return _&&_.__isVue},body(_){if(_&&_.__isVue)return["div",{},...a(_.$)]}};function a(_){const w=[];_.type.props&&_.props&&w.push(u("props",Ie(_.props))),_.setupState!==tt&&w.push(u("setup",_.setupState)),_.data!==tt&&w.push(u("data",Ie(_.data)));const D=h(_,"computed");D&&w.push(u("computed",D));const V=h(_,"inject");return V&&w.push(u("injected",V)),w.push(["div",{},["span",{style:i.style+";opacity:0.66"},"$ (internal): "],["object",{object:_}]]),w}function u(_,w){return w=pt({},w),Object.keys(w).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},_],["div",{style:"padding-left:1.25em"},...Object.keys(w).map(D=>["div",{},["span",i,D+": "],c(w[D],!1)])]]:["span",{}]}function c(_,w=!0){return typeof _=="number"?["span",t,_]:typeof _=="string"?["span",s,JSON.stringify(_)]:typeof _=="boolean"?["span",i,_]:Ze(_)?["object",{object:w?Ie(_):_}]:["span",s,String(_)]}function h(_,w){const D=_.type;if(xe(D))return;const V={};for(const F in _.ctx)m(D,F,w)&&(V[F]=_.ctx[F]);return V}function m(_,w,D){const V=_[D];if(me(V)&&V.includes(w)||Ze(V)&&w in V||_.extends&&m(_.extends,w,D)||_.mixins&&_.mixins.some(F=>m(F,w,D)))return!0}function p(_){return Yt(_)?"ShallowRef":_.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(n):window.devtoolsFormatters=[n]}const If="3.5.13",Qs={}.NODE_ENV!=="production"?Q:St;/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ql;const Mf=typeof window<"u"&&window.trustedTypes;if(Mf)try{ql=Mf.createPolicy("vue",{createHTML:e=>e})}catch(e){({}).NODE_ENV!=="production"&&Qs(`Error creating trusted types policy: ${e}`)}const Pf=ql?e=>ql.createHTML(e):e=>e,yy="http://www.w3.org/2000/svg",by="http://www.w3.org/1998/Math/MathML",ur=typeof document<"u"?document:null,kf=ur&&ur.createElement("template"),wy={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const n=t==="svg"?ur.createElementNS(yy,e):t==="mathml"?ur.createElementNS(by,e):s?ur.createElement(e,{is:s}):ur.createElement(e);return e==="select"&&i&&i.multiple!=null&&n.setAttribute("multiple",i.multiple),n},createText:e=>ur.createTextNode(e),createComment:e=>ur.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ur.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,n,a){const u=s?s.previousSibling:t.lastChild;if(n&&(n===a||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===a||!(n=n.nextSibling)););else{kf.innerHTML=Pf(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const c=kf.content;if(i==="svg"||i==="mathml"){const h=c.firstChild;for(;h.firstChild;)c.appendChild(h.firstChild);c.removeChild(h)}t.insertBefore(c,s)}return[u?u.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Pr="transition",So="animation",To=Symbol("_vtc"),Vf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Ey=pt({},Id,Vf),Rf=(e=>(e.displayName="Transition",e.props=Ey,e))((e,{slots:t})=>Hl(tv,Cy(e),t)),un=(e,t=[])=>{me(e)?e.forEach(s=>s(...t)):e&&e(...t)},Lf=e=>e?me(e)?e.some(t=>t.length>1):e.length>1:!1;function Cy(e){const t={};for(const ue in e)ue in Vf||(t[ue]=e[ue]);if(e.css===!1)return t;const{name:s="v",type:i,duration:n,enterFromClass:a=`${s}-enter-from`,enterActiveClass:u=`${s}-enter-active`,enterToClass:c=`${s}-enter-to`,appearFromClass:h=a,appearActiveClass:m=u,appearToClass:p=c,leaveFromClass:_=`${s}-leave-from`,leaveActiveClass:w=`${s}-leave-active`,leaveToClass:D=`${s}-leave-to`}=e,V=Dy(n),F=V&&V[0],te=V&&V[1],{onBeforeEnter:I,onEnter:se,onEnterCancelled:K,onLeave:ye,onLeaveCancelled:Z,onBeforeAppear:fe=I,onAppear:ve=se,onAppearCancelled:Ae=K}=t,ae=(ue,Ge,_t,mt)=>{ue._enterCancelled=mt,cn(ue,Ge?p:c),cn(ue,Ge?m:u),_t&&_t()},A=(ue,Ge)=>{ue._isLeaving=!1,cn(ue,_),cn(ue,D),cn(ue,w),Ge&&Ge()},be=ue=>(Ge,_t)=>{const mt=ue?ve:se,ft=()=>ae(Ge,ue,_t);un(mt,[Ge,ft]),Uf(()=>{cn(Ge,ue?h:a),cr(Ge,ue?p:c),Lf(mt)||Ff(Ge,i,F,ft)})};return pt(t,{onBeforeEnter(ue){un(I,[ue]),cr(ue,a),cr(ue,u)},onBeforeAppear(ue){un(fe,[ue]),cr(ue,h),cr(ue,m)},onEnter:be(!1),onAppear:be(!0),onLeave(ue,Ge){ue._isLeaving=!0;const _t=()=>A(ue,Ge);cr(ue,_),ue._enterCancelled?(cr(ue,w),jf()):(jf(),cr(ue,w)),Uf(()=>{ue._isLeaving&&(cn(ue,_),cr(ue,D),Lf(ye)||Ff(ue,i,te,_t))}),un(ye,[ue,_t])},onEnterCancelled(ue){ae(ue,!1,void 0,!0),un(K,[ue])},onAppearCancelled(ue){ae(ue,!0,void 0,!0),un(Ae,[ue])},onLeaveCancelled(ue){A(ue),un(Z,[ue])}})}function Dy(e){if(e==null)return null;if(Ze(e))return[zl(e.enter),zl(e.leave)];{const t=zl(e);return[t,t]}}function zl(e){const t=zg(e);return{}.NODE_ENV!=="production"&&V_(t,"<transition> explicit duration"),t}function cr(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.add(s)),(e[To]||(e[To]=new Set)).add(t)}function cn(e,t){t.split(/\s+/).forEach(i=>i&&e.classList.remove(i));const s=e[To];s&&(s.delete(t),s.size||(e[To]=void 0))}function Uf(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let xy=0;function Ff(e,t,s,i){const n=e._endId=++xy,a=()=>{n===e._endId&&i()};if(s!=null)return setTimeout(a,s);const{type:u,timeout:c,propCount:h}=Oy(e,t);if(!u)return i();const m=u+"end";let p=0;const _=()=>{e.removeEventListener(m,w),a()},w=D=>{D.target===e&&++p>=h&&_()};setTimeout(()=>{p<h&&_()},c+1),e.addEventListener(m,w)}function Oy(e,t){const s=window.getComputedStyle(e),i=V=>(s[V]||"").split(", "),n=i(`${Pr}Delay`),a=i(`${Pr}Duration`),u=Bf(n,a),c=i(`${So}Delay`),h=i(`${So}Duration`),m=Bf(c,h);let p=null,_=0,w=0;t===Pr?u>0&&(p=Pr,_=u,w=a.length):t===So?m>0&&(p=So,_=m,w=h.length):(_=Math.max(u,m),p=_>0?u>m?Pr:So:null,w=p?p===Pr?a.length:h.length:0);const D=p===Pr&&/\b(transform|all)(,|$)/.test(i(`${Pr}Property`).toString());return{type:p,timeout:_,propCount:w,hasTransform:D}}function Bf(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((s,i)=>$f(s)+$f(e[i])))}function $f(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function jf(){return document.body.offsetHeight}function Sy(e,t,s){const i=e[To];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Fi=Symbol("_vod"),Hf=Symbol("_vsh"),Wl={beforeMount(e,{value:t},{transition:s}){e[Fi]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):No(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),No(e,!0),i.enter(e)):i.leave(e,()=>{No(e,!1)}):No(e,t))},beforeUnmount(e,{value:t}){No(e,t)}};({}).NODE_ENV!=="production"&&(Wl.name="show");function No(e,t){e.style.display=t?e[Fi]:"none",e[Hf]=!t}const Ty=Symbol({}.NODE_ENV!=="production"?"CSS_VAR_TEXT":""),Ny=/(^|;)\s*display\s*:/;function Ay(e,t,s){const i=e.style,n=ct(s);let a=!1;if(s&&!n){if(t)if(ct(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();s[c]==null&&Bi(i,c,"")}else for(const u in t)s[u]==null&&Bi(i,u,"");for(const u in s)u==="display"&&(a=!0),Bi(i,u,s[u])}else if(n){if(t!==s){const u=i[Ty];u&&(s+=";"+u),i.cssText=s,a=Ny.test(s)}}else t&&e.removeAttribute("style");Fi in e&&(e[Fi]=a?i.display:"",e[Hf]&&(i.display="none"))}const Iy=/[^\\];\s*$/,qf=/\s*!important$/;function Bi(e,t,s){if(me(s))s.forEach(i=>Bi(e,t,i));else if(s==null&&(s=""),{}.NODE_ENV!=="production"&&Iy.test(s)&&Qs(`Unexpected semicolon at the end of '${t}' style value: '${s}'`),t.startsWith("--"))e.setProperty(t,s);else{const i=My(e,t);qf.test(s)?e.setProperty(Or(i),s.replace(qf,""),"important"):e[i]=s}}const zf=["Webkit","Moz","ms"],Gl={};function My(e,t){const s=Gl[t];if(s)return s;let i=Kt(t);if(i!=="filter"&&i in e)return Gl[t]=i;i=Yr(i);for(let n=0;n<zf.length;n++){const a=zf[n]+i;if(a in e)return Gl[t]=a}return t}const Wf="http://www.w3.org/1999/xlink";function Gf(e,t,s,i,n,a=s_(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Wf,t.slice(6,t.length)):e.setAttributeNS(Wf,t,s):s==null||a&&!Rc(s)?e.removeAttribute(t):e.setAttribute(t,a?"":Is(s)?String(s):s)}function Kf(e,t,s,i,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Pf(s):s);return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const c=a==="OPTION"?e.getAttribute("value")||"":e.value,h=s==null?e.type==="checkbox"?"on":"":String(s);(c!==h||!("_value"in e))&&(e.value=h),s==null&&e.removeAttribute(t),e._value=s;return}let u=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=Rc(s):s==null&&c==="string"?(s="",u=!0):c==="number"&&(s=0,u=!0)}try{e[t]=s}catch(c){({}).NODE_ENV!=="production"&&!u&&Qs(`Failed setting prop "${t}" on <${a.toLowerCase()}>: value ${s} is invalid.`,c)}u&&e.removeAttribute(n||t)}function kr(e,t,s,i){e.addEventListener(t,s,i)}function Py(e,t,s,i){e.removeEventListener(t,s,i)}const Yf=Symbol("_vei");function ky(e,t,s,i,n=null){const a=e[Yf]||(e[Yf]={}),u=a[t];if(i&&u)u.value={}.NODE_ENV!=="production"?Zf(i,t):i;else{const[c,h]=Vy(t);if(i){const m=a[t]=Uy({}.NODE_ENV!=="production"?Zf(i,t):i,n);kr(e,c,m,h)}else u&&(Py(e,c,u,h),a[t]=void 0)}}const Qf=/(?:Once|Passive|Capture)$/;function Vy(e){let t;if(Qf.test(e)){t={};let i;for(;i=e.match(Qf);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Or(e.slice(2)),t]}let Kl=0;const Ry=Promise.resolve(),Ly=()=>Kl||(Ry.then(()=>Kl=0),Kl=Date.now());function Uy(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Ps(Fy(i,s.value),t,5,[i])};return s.value=e,s.attached=Ly(),s}function Zf(e,t){return xe(e)||me(e)?e:(Qs(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof e}.`),St)}function Fy(e,t){if(me(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>n=>!n._stopped&&i&&i(n))}else return t}const Jf=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,By=(e,t,s,i,n,a)=>{const u=n==="svg";t==="class"?Sy(e,i,u):t==="style"?Ay(e,s,i):to(t)?ni(t)||ky(e,t,s,i,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):$y(e,t,i,u))?(Kf(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Gf(e,t,i,u,a,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ct(i))?Kf(e,Kt(t),i,a,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),Gf(e,t,i,u))};function $y(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&Jf(t)&&xe(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Jf(t)&&ct(s)?!1:t in e}const kn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return me(t)?s=>On(t,s):t};function jy(e){e.target.composing=!0}function Xf(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const dr=Symbol("_assign"),Xt={created(e,{modifiers:{lazy:t,trim:s,number:i}},n){e[dr]=kn(n);const a=i||n.props&&n.props.type==="number";kr(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;s&&(c=c.trim()),a&&(c=ai(c)),e[dr](c)}),s&&kr(e,"change",()=>{e.value=e.value.trim()}),t||(kr(e,"compositionstart",jy),kr(e,"compositionend",Xf),kr(e,"change",Xf))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:n,number:a}},u){if(e[dr]=kn(u),e.composing)return;const c=(a||e.type==="number")&&!/^0\d/.test(e.value)?ai(e.value):e.value,h=t??"";c!==h&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||n&&e.value.trim()===h)||(e.value=h))}},$i={deep:!0,created(e,t,s){e[dr]=kn(s),kr(e,"change",()=>{const i=e._modelValue,n=Ao(e),a=e.checked,u=e[dr];if(me(i)){const c=Ja(i,n),h=c!==-1;if(a&&!h)u(i.concat(n));else if(!a&&h){const m=[...i];m.splice(c,1),u(m)}}else if(xn(i)){const c=new Set(i);a?c.add(n):c.delete(n),u(c)}else u(sh(e,a))})},mounted:eh,beforeUpdate(e,t,s){e[dr]=kn(s),eh(e,t,s)}};function eh(e,{value:t,oldValue:s},i){e._modelValue=t;let n;if(me(t))n=Ja(t,i.props.value)>-1;else if(xn(t))n=t.has(i.props.value);else{if(t===s)return;n=oo(t,sh(e,!0))}e.checked!==n&&(e.checked=n)}const Yl={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const n=xn(t);kr(e,"change",()=>{const a=Array.prototype.filter.call(e.options,u=>u.selected).map(u=>s?ai(Ao(u)):Ao(u));e[dr](e.multiple?n?new Set(a):a:a[0]),e._assigning=!0,ml(()=>{e._assigning=!1})}),e[dr]=kn(i)},mounted(e,{value:t}){th(e,t)},beforeUpdate(e,t,s){e[dr]=kn(s)},updated(e,{value:t}){e._assigning||th(e,t)}};function th(e,t){const s=e.multiple,i=me(t);if(s&&!i&&!xn(t)){({}).NODE_ENV!=="production"&&Qs(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`);return}for(let n=0,a=e.options.length;n<a;n++){const u=e.options[n],c=Ao(u);if(s)if(i){const h=typeof c;h==="string"||h==="number"?u.selected=t.some(m=>String(m)===String(c)):u.selected=Ja(t,c)>-1}else u.selected=t.has(c);else if(oo(Ao(u),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}function Ao(e){return"_value"in e?e._value:e.value}function sh(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Hy=["ctrl","shift","alt","meta"],qy={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Hy.some(s=>e[`${s}Key`]&&!t.includes(s))},Lt=(e,t)=>{const s=e._withMods||(e._withMods={}),i=t.join(".");return s[i]||(s[i]=(n,...a)=>{for(let u=0;u<t.length;u++){const c=qy[t[u]];if(c&&c(n,t))return}return e(n,...a)})},zy=pt({patchProp:By},wy);let rh;function Wy(){return rh||(rh=$v(zy))}const Gy=(...e)=>{const t=Wy().createApp(...e);({}).NODE_ENV!=="production"&&(Yy(t),Qy(t));const{mount:s}=t;return t.mount=i=>{const n=Zy(i);if(!n)return;const a=t._component;!xe(a)&&!a.render&&!a.template&&(a.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const u=s(n,!1,Ky(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),u},t};function Ky(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Yy(e){Object.defineProperty(e.config,"isNativeTag",{value:t=>Xg(t)||e_(t)||t_(t),writable:!1})}function Qy(e){{const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get(){return t},set(){Qs("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const s=e.config.compilerOptions,i='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get(){return Qs(i),s},set(){Qs(i)}})}}function Zy(e){if(ct(e)){const t=document.querySelector(e);return{}.NODE_ENV!=="production"&&!t&&Qs(`Failed to mount app: mount target selector "${e}" returned null.`),t}return{}.NODE_ENV!=="production"&&window.ShadowRoot&&e instanceof window.ShadowRoot&&e.mode==="closed"&&Qs('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs'),e}/**
* vue v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Jy(){vy()}({}).NODE_ENV!=="production"&&Jy();var Xy=!1;function eb(){return nh().__VUE_DEVTOOLS_GLOBAL_HOOK__}function nh(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const tb=typeof Proxy=="function",sb="devtools-plugin:setup",rb="plugin:settings:set";let Vn,Ql;function nb(){var e;return Vn!==void 0||(typeof window<"u"&&window.performance?(Vn=!0,Ql=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Vn=!0,Ql=globalThis.perf_hooks.performance):Vn=!1),Vn}function ob(){return nb()?Ql.now():Date.now()}class ib{constructor(t,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=s;const i={};if(t.settings)for(const u in t.settings){const c=t.settings[u];i[u]=c.defaultValue}const n=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},i);try{const u=localStorage.getItem(n),c=JSON.parse(u);Object.assign(a,c)}catch{}this.fallbacks={getSettings(){return a},setSettings(u){try{localStorage.setItem(n,JSON.stringify(u))}catch{}a=u},now(){return ob()}},s&&s.on(rb,(u,c)=>{u===this.plugin.id&&this.fallbacks.setSettings(c)}),this.proxiedOn=new Proxy({},{get:(u,c)=>this.target?this.target.on[c]:(...h)=>{this.onQueue.push({method:c,args:h})}}),this.proxiedTarget=new Proxy({},{get:(u,c)=>this.target?this.target[c]:c==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(c)?(...h)=>(this.targetQueue.push({method:c,args:h,resolve:()=>{}}),this.fallbacks[c](...h)):(...h)=>new Promise(m=>{this.targetQueue.push({method:c,args:h,resolve:m})})})}async setRealTarget(t){this.target=t;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args))}}function Zl(e,t){const s=e,i=nh(),n=eb(),a=tb&&s.enableEarlyProxy;if(n&&(i.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))n.emit(sb,e,t);else{const u=a?new ib(s,n):null;(i.__VUE_DEVTOOLS_PLUGINS__=i.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:t,proxy:u}),u&&t(u.proxiedTarget)}}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const ab={}.NODE_ENV!=="production"?Symbol("pinia"):Symbol();var dn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(dn||(dn={}));const Jl=typeof window<"u",oh=(()=>typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null})();function lb(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function Xl(e,t,s){const i=new XMLHttpRequest;i.open("GET",e),i.responseType="blob",i.onload=function(){lh(i.response,t,s)},i.onerror=function(){console.error("could not download file")},i.send()}function ih(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function ji(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}const Hi=typeof navigator=="object"?navigator:{userAgent:""},ah=(()=>/Macintosh/.test(Hi.userAgent)&&/AppleWebKit/.test(Hi.userAgent)&&!/Safari/.test(Hi.userAgent))(),lh=Jl?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!ah?ub:"msSaveOrOpenBlob"in Hi?cb:db:()=>{};function ub(e,t="download",s){const i=document.createElement("a");i.download=t,i.rel="noopener",typeof e=="string"?(i.href=e,i.origin!==location.origin?ih(i.href)?Xl(e,t,s):(i.target="_blank",ji(i)):ji(i)):(i.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(i.href)},4e4),setTimeout(function(){ji(i)},0))}function cb(e,t="download",s){if(typeof e=="string")if(ih(e))Xl(e,t,s);else{const i=document.createElement("a");i.href=e,i.target="_blank",setTimeout(function(){ji(i)})}else navigator.msSaveOrOpenBlob(lb(e,s),t)}function db(e,t,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),typeof e=="string")return Xl(e,t,s);const n=e.type==="application/octet-stream",a=/constructor/i.test(String(oh.HTMLElement))||"safari"in oh,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||n&&a||ah)&&typeof FileReader<"u"){const c=new FileReader;c.onloadend=function(){let h=c.result;if(typeof h!="string")throw i=null,new Error("Wrong reader.result type");h=u?h:h.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=h:location.assign(h),i=null},c.readAsDataURL(e)}else{const c=URL.createObjectURL(e);i?i.location.assign(c):location.href=c,i=null,setTimeout(function(){URL.revokeObjectURL(c)},4e4)}}function Pt(e,t){const s="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(s,t):t==="error"?console.error(s):t==="warn"?console.warn(s):console.log(s)}function eu(e){return"_a"in e&&"install"in e}function uh(){if(!("clipboard"in navigator))return Pt("Your browser doesn't support the Clipboard API","error"),!0}function ch(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?(Pt('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function fb(e){if(!uh())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),Pt("Global state copied to clipboard.")}catch(t){if(ch(t))return;Pt("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function hb(e){if(!uh())try{dh(e,JSON.parse(await navigator.clipboard.readText())),Pt("Global state pasted from clipboard.")}catch(t){if(ch(t))return;Pt("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function pb(e){try{lh(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){Pt("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let fr;function mb(){fr||(fr=document.createElement("input"),fr.type="file",fr.accept=".json");function e(){return new Promise((t,s)=>{fr.onchange=async()=>{const i=fr.files;if(!i)return t(null);const n=i.item(0);return t(n?{text:await n.text(),file:n}:null)},fr.oncancel=()=>t(null),fr.onerror=s,fr.click()})}return e}async function gb(e){try{const s=await mb()();if(!s)return;const{text:i,file:n}=s;dh(e,JSON.parse(i)),Pt(`Global state imported from "${n.name}".`)}catch(t){Pt("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function dh(e,t){for(const s in t){const i=e.state.value[s];i?Object.assign(i,t[s]):e.state.value[s]=t[s]}}function Us(e){return{_custom:{display:e}}}const fh="🍍 Pinia (root)",qi="_root";function _b(e){return eu(e)?{id:qi,label:fh}:{id:e.$id,label:e.$id}}function vb(e){if(eu(e)){const s=Array.from(e._s.keys()),i=e._s;return{state:s.map(a=>({editable:!0,key:a,value:e.state.value[a]})),getters:s.filter(a=>i.get(a)._getters).map(a=>{const u=i.get(a);return{editable:!1,key:a,value:u._getters.reduce((c,h)=>(c[h]=u[h],c),{})}})}}const t={state:Object.keys(e.$state).map(s=>({editable:!0,key:s,value:e.$state[s]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(s=>({editable:!1,key:s,value:e[s]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(s=>({editable:!0,key:s,value:e[s]}))),t}function yb(e){return e?Array.isArray(e)?e.reduce((t,s)=>(t.keys.push(s.key),t.operations.push(s.type),t.oldValue[s.key]=s.oldValue,t.newValue[s.key]=s.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:Us(e.type),key:Us(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function bb(e){switch(e){case dn.direct:return"mutation";case dn.patchFunction:return"$patch";case dn.patchObject:return"$patch";default:return"unknown"}}let Rn=!0;const zi=[],fn="pinia:mutations",qt="pinia",{assign:wb}=Object,Wi=e=>"🍍 "+e;function Eb(e,t){Zl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:zi,app:e},s=>{typeof s.now!="function"&&Pt("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.addTimelineLayer({id:fn,label:"Pinia 🍍",color:15064968}),s.addInspector({id:qt,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{fb(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await hb(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{pb(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await gb(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:i=>{const n=t._s.get(i);n?typeof n.$reset!="function"?Pt(`Cannot reset "${i}" store because it doesn't have a "$reset" method implemented.`,"warn"):(n.$reset(),Pt(`Store "${i}" reset.`)):Pt(`Cannot reset "${i}" store because it wasn't found.`,"warn")}}]}),s.on.inspectComponent((i,n)=>{const a=i.componentInstance&&i.componentInstance.proxy;if(a&&a._pStores){const u=i.componentInstance.proxy._pStores;Object.values(u).forEach(c=>{i.instanceData.state.push({type:Wi(c.$id),key:"state",editable:!0,value:c._isOptionsAPI?{_custom:{value:Ie(c.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>c.$reset()}]}}:Object.keys(c.$state).reduce((h,m)=>(h[m]=c.$state[m],h),{})}),c._getters&&c._getters.length&&i.instanceData.state.push({type:Wi(c.$id),key:"getters",editable:!1,value:c._getters.reduce((h,m)=>{try{h[m]=c[m]}catch(p){h[m]=p}return h},{})})})}}),s.on.getInspectorTree(i=>{if(i.app===e&&i.inspectorId===qt){let n=[t];n=n.concat(Array.from(t._s.values())),i.rootNodes=(i.filter?n.filter(a=>"$id"in a?a.$id.toLowerCase().includes(i.filter.toLowerCase()):fh.toLowerCase().includes(i.filter.toLowerCase())):n).map(_b)}}),globalThis.$pinia=t,s.on.getInspectorState(i=>{if(i.app===e&&i.inspectorId===qt){const n=i.nodeId===qi?t:t._s.get(i.nodeId);if(!n)return;n&&(i.nodeId!==qi&&(globalThis.$store=Ie(n)),i.state=vb(n))}}),s.on.editInspectorState((i,n)=>{if(i.app===e&&i.inspectorId===qt){const a=i.nodeId===qi?t:t._s.get(i.nodeId);if(!a)return Pt(`store "${i.nodeId}" not found`,"error");const{path:u}=i;eu(a)?u.unshift("state"):(u.length!==1||!a._customProperties.has(u[0])||u[0]in a.$state)&&u.unshift("$state"),Rn=!1,i.set(a,u,i.state.value),Rn=!0}}),s.on.editComponentState(i=>{if(i.type.startsWith("🍍")){const n=i.type.replace(/^🍍\s*/,""),a=t._s.get(n);if(!a)return Pt(`store "${n}" not found`,"error");const{path:u}=i;if(u[0]!=="state")return Pt(`Invalid path for store "${n}":
${u}
Only state can be modified.`);u[0]="$state",Rn=!1,i.set(a,u,i.state.value),Rn=!0}})})}function Cb(e,t){zi.includes(Wi(t.$id))||zi.push(Wi(t.$id)),Zl({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:zi,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},s=>{const i=typeof s.now=="function"?s.now.bind(s):Date.now;t.$onAction(({after:u,onError:c,name:h,args:m})=>{const p=hh++;s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🛫 "+h,subtitle:"start",data:{store:Us(t.$id),action:Us(h),args:m},groupId:p}}),u(_=>{Vr=void 0,s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🛬 "+h,subtitle:"end",data:{store:Us(t.$id),action:Us(h),args:m,result:_},groupId:p}})}),c(_=>{Vr=void 0,s.addTimelineEvent({layerId:fn,event:{time:i(),logType:"error",title:"💥 "+h,subtitle:"end",data:{store:Us(t.$id),action:Us(h),args:m,error:_},groupId:p}})})},!0),t._customProperties.forEach(u=>{Pn(()=>Tr(t[u]),(c,h)=>{s.notifyComponentUpdate(),s.sendInspectorState(qt),Rn&&s.addTimelineEvent({layerId:fn,event:{time:i(),title:"Change",subtitle:u,data:{newValue:c,oldValue:h},groupId:Vr}})},{deep:!0})}),t.$subscribe(({events:u,type:c},h)=>{if(s.notifyComponentUpdate(),s.sendInspectorState(qt),!Rn)return;const m={time:i(),title:bb(c),data:wb({store:Us(t.$id)},yb(u)),groupId:Vr};c===dn.patchFunction?m.subtitle="⤵️":c===dn.patchObject?m.subtitle="🧩":u&&!Array.isArray(u)&&(m.subtitle=u.type),u&&(m.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:u}}),s.addTimelineEvent({layerId:fn,event:m})},{detached:!0,flush:"sync"});const n=t._hotUpdate;t._hotUpdate=dl(u=>{n(u),s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:Us(t.$id),info:Us("HMR update")}}}),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt)});const{$dispose:a}=t;t.$dispose=()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&Pt(`Disposed "${t.$id}" store 🗑`)},s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&Pt(`"${t.$id}" store installed 🆕`)})}let hh=0,Vr;function ph(e,t,s){const i=t.reduce((n,a)=>(n[a]=Ie(e)[a],n),{});for(const n in i)e[n]=function(){const a=hh,u=s?new Proxy(e,{get(...h){return Vr=a,Reflect.get(...h)},set(...h){return Vr=a,Reflect.set(...h)}}):e;Vr=a;const c=i[n].apply(u,arguments);return Vr=void 0,c}}function Db({app:e,store:t,options:s}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!s.state,!t._p._testing){ph(t,Object.keys(s.actions),t._isOptionsAPI);const i=t._hotUpdate;Ie(t)._hotUpdate=function(n){i.apply(this,arguments),ph(t,Object.keys(n._hmrPayload.actions),!!t._isOptionsAPI)}}Cb(e,t)}}function xb(){const e=n_(!0),t=e.run(()=>ad({}));let s=[],i=[];const n=dl({install(a){n._a=a,a.provide(ab,n),a.config.globalProperties.$pinia=n,{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&Jl&&Eb(a,n),i.forEach(u=>s.push(u)),i=[]},use(a){return!this._a&&!Xy?i.push(a):s.push(a),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&Jl&&typeof Proxy<"u"&&n.use(Db),n}const aR="",He=(e,t)=>{const s=e.__vccOpts||e;for(const[i,n]of t)s[i]=n;return s},Ob={name:"App",mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}}},Sb={id:"app"};function Tb(e,t,s,i,n,a){const u=ee("router-view");return O(),N("div",Sb,[k(u)])}const Nb=He(Ob,[["render",Tb]]);/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const hr=typeof document<"u";function mh(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Ab(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&mh(e.default)}const Je=Object.assign;function tu(e,t){const s={};for(const i in t){const n=t[i];s[i]=fs(n)?n.map(e):e(n)}return s}const Io=()=>{},fs=Array.isArray;function $e(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const gh=/#/g,Ib=/&/g,Mb=/\//g,Pb=/=/g,kb=/\?/g,_h=/\+/g,Vb=/%5B/g,Rb=/%5D/g,vh=/%5E/g,Lb=/%60/g,yh=/%7B/g,Ub=/%7C/g,bh=/%7D/g,Fb=/%20/g;function su(e){return encodeURI(""+e).replace(Ub,"|").replace(Vb,"[").replace(Rb,"]")}function Bb(e){return su(e).replace(yh,"{").replace(bh,"}").replace(vh,"^")}function ru(e){return su(e).replace(_h,"%2B").replace(Fb,"+").replace(gh,"%23").replace(Ib,"%26").replace(Lb,"`").replace(yh,"{").replace(bh,"}").replace(vh,"^")}function $b(e){return ru(e).replace(Pb,"%3D")}function jb(e){return su(e).replace(gh,"%23").replace(kb,"%3F")}function Hb(e){return e==null?"":jb(e).replace(Mb,"%2F")}function Ln(e){try{return decodeURIComponent(""+e)}catch{({}).NODE_ENV!=="production"&&$e(`Error decoding "${e}". Using original value`)}return""+e}const qb=/\/$/,zb=e=>e.replace(qb,"");function nu(e,t,s="/"){let i,n={},a="",u="";const c=t.indexOf("#");let h=t.indexOf("?");return c<h&&c>=0&&(h=-1),h>-1&&(i=t.slice(0,h),a=t.slice(h+1,c>-1?c:t.length),n=e(a)),c>-1&&(i=i||t.slice(0,c),u=t.slice(c,t.length)),i=Kb(i??t,s),{fullPath:i+(a&&"?")+a+u,path:i,query:n,hash:Ln(u)}}function Wb(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function wh(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Eh(e,t,s){const i=t.matched.length-1,n=s.matched.length-1;return i>-1&&i===n&&Rr(t.matched[i],s.matched[n])&&Ch(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Rr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ch(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Gb(e[s],t[s]))return!1;return!0}function Gb(e,t){return fs(e)?Dh(e,t):fs(t)?Dh(t,e):e===t}function Dh(e,t){return fs(t)?e.length===t.length&&e.every((s,i)=>s===t[i]):e.length===1&&e[0]===t}function Kb(e,t){if(e.startsWith("/"))return e;if({}.NODE_ENV!=="production"&&!t.startsWith("/"))return $e(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const s=t.split("/"),i=e.split("/"),n=i[i.length-1];(n===".."||n===".")&&i.push("");let a=s.length-1,u,c;for(u=0;u<i.length;u++)if(c=i[u],c!==".")if(c==="..")a>1&&a--;else break;return s.slice(0,a).join("/")+"/"+i.slice(u).join("/")}const Lr={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Mo;(function(e){e.pop="pop",e.push="push"})(Mo||(Mo={}));var Po;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Po||(Po={}));function Yb(e){if(!e)if(hr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),zb(e)}const Qb=/^[^#]+#/;function Zb(e,t){return e.replace(Qb,"#")+t}function Jb(e,t){const s=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-s.left-(t.left||0),top:i.top-s.top-(t.top||0)}}const Gi=()=>({left:window.scrollX,top:window.scrollY});function Xb(e){let t;if("el"in e){const s=e.el,i=typeof s=="string"&&s.startsWith("#");if({}.NODE_ENV!=="production"&&typeof e.el=="string"&&(!i||!document.getElementById(e.el.slice(1))))try{const a=document.querySelector(e.el);if(i&&a){$e(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{$e(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}const n=typeof s=="string"?i?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n){({}).NODE_ENV!=="production"&&$e(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=Jb(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function xh(e,t){return(history.state?history.state.position-t:-1)+e}const ou=new Map;function ew(e,t){ou.set(e,t)}function tw(e){const t=ou.get(e);return ou.delete(e),t}let sw=()=>location.protocol+"//"+location.host;function Oh(e,t){const{pathname:s,search:i,hash:n}=t,a=e.indexOf("#");if(a>-1){let c=n.includes(e.slice(a))?e.slice(a).length:1,h=n.slice(c);return h[0]!=="/"&&(h="/"+h),wh(h,"")}return wh(s,e)+i+n}function rw(e,t,s,i){let n=[],a=[],u=null;const c=({state:w})=>{const D=Oh(e,location),V=s.value,F=t.value;let te=0;if(w){if(s.value=D,t.value=w,u&&u===V){u=null;return}te=F?w.position-F.position:0}else i(D);n.forEach(I=>{I(s.value,V,{delta:te,type:Mo.pop,direction:te?te>0?Po.forward:Po.back:Po.unknown})})};function h(){u=s.value}function m(w){n.push(w);const D=()=>{const V=n.indexOf(w);V>-1&&n.splice(V,1)};return a.push(D),D}function p(){const{history:w}=window;w.state&&w.replaceState(Je({},w.state,{scroll:Gi()}),"")}function _(){for(const w of a)w();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:h,listen:m,destroy:_}}function Sh(e,t,s,i=!1,n=!1){return{back:e,current:t,forward:s,replaced:i,position:window.history.length,scroll:n?Gi():null}}function nw(e){const{history:t,location:s}=window,i={value:Oh(e,s)},n={value:t.state};n.value||a(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(h,m,p){const _=e.indexOf("#"),w=_>-1?(s.host&&document.querySelector("base")?e:e.slice(_))+h:sw()+e+h;try{t[p?"replaceState":"pushState"](m,"",w),n.value=m}catch(D){({}).NODE_ENV!=="production"?$e("Error with push/replace State",D):console.error(D),s[p?"replace":"assign"](w)}}function u(h,m){const p=Je({},t.state,Sh(n.value.back,h,n.value.forward,!0),m,{position:n.value.position});a(h,p,!0),i.value=h}function c(h,m){const p=Je({},n.value,t.state,{forward:h,scroll:Gi()});({}).NODE_ENV!=="production"&&!t.state&&$e(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(p.current,p,!0);const _=Je({},Sh(i.value,h,null),{position:p.position+1},m);a(h,_,!1),i.value=h}return{location:i,state:n,push:c,replace:u}}function ow(e){e=Yb(e);const t=nw(e),s=rw(e,t.state,t.location,t.replace);function i(a,u=!0){u||s.pauseListeners(),history.go(a)}const n=Je({location:"",base:e,go:i,createHref:Zb.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function Ki(e){return typeof e=="string"||e&&typeof e=="object"}function Th(e){return typeof e=="string"||typeof e=="symbol"}const iu=Symbol({}.NODE_ENV!=="production"?"navigation failure":"");var Nh;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Nh||(Nh={}));const iw={1({location:e,currentLocation:t}){return`No match for
 ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):""}`},2({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${lw(t)}" via a navigation guard.`},4({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},8({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},16({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function Un(e,t){return{}.NODE_ENV!=="production"?Je(new Error(iw[e](t)),{type:e,[iu]:!0},t):Je(new Error,{type:e,[iu]:!0},t)}function pr(e,t){return e instanceof Error&&iu in e&&(t==null||!!(e.type&t))}const aw=["params","query","hash"];function lw(e){if(typeof e=="string")return e;if(e.path!=null)return e.path;const t={};for(const s of aw)s in e&&(t[s]=e[s]);return JSON.stringify(t,null,2)}const Ah="[^/]+?",uw={sensitive:!1,strict:!1,start:!0,end:!0},cw=/[.+*?^${}()[\]/\\]/g;function dw(e,t){const s=Je({},uw,t),i=[];let n=s.start?"^":"";const a=[];for(const m of e){const p=m.length?[]:[90];s.strict&&!m.length&&(n+="/");for(let _=0;_<m.length;_++){const w=m[_];let D=40+(s.sensitive?.25:0);if(w.type===0)_||(n+="/"),n+=w.value.replace(cw,"\\$&"),D+=40;else if(w.type===1){const{value:V,repeatable:F,optional:te,regexp:I}=w;a.push({name:V,repeatable:F,optional:te});const se=I||Ah;if(se!==Ah){D+=10;try{new RegExp(`(${se})`)}catch(ye){throw new Error(`Invalid custom RegExp for param "${V}" (${se}): `+ye.message)}}let K=F?`((?:${se})(?:/(?:${se}))*)`:`(${se})`;_||(K=te&&m.length<2?`(?:/${K})`:"/"+K),te&&(K+="?"),n+=K,D+=20,te&&(D+=-8),F&&(D+=-20),se===".*"&&(D+=-50)}p.push(D)}i.push(p)}if(s.strict&&s.end){const m=i.length-1;i[m][i[m].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const u=new RegExp(n,s.sensitive?"":"i");function c(m){const p=m.match(u),_={};if(!p)return null;for(let w=1;w<p.length;w++){const D=p[w]||"",V=a[w-1];_[V.name]=D&&V.repeatable?D.split("/"):D}return _}function h(m){let p="",_=!1;for(const w of e){(!_||!p.endsWith("/"))&&(p+="/"),_=!1;for(const D of w)if(D.type===0)p+=D.value;else if(D.type===1){const{value:V,repeatable:F,optional:te}=D,I=V in m?m[V]:"";if(fs(I)&&!F)throw new Error(`Provided param "${V}" is an array but it is not repeatable (* or + modifiers)`);const se=fs(I)?I.join("/"):I;if(!se)if(te)w.length<2&&(p.endsWith("/")?p=p.slice(0,-1):_=!0);else throw new Error(`Missing required param "${V}"`);p+=se}}return p||"/"}return{re:u,score:i,keys:a,parse:c,stringify:h}}function fw(e,t){let s=0;for(;s<e.length&&s<t.length;){const i=t[s]-e[s];if(i)return i;s++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Ih(e,t){let s=0;const i=e.score,n=t.score;for(;s<i.length&&s<n.length;){const a=fw(i[s],n[s]);if(a)return a;s++}if(Math.abs(n.length-i.length)===1){if(Mh(i))return 1;if(Mh(n))return-1}return n.length-i.length}function Mh(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const hw={type:0,value:""},pw=/[a-zA-Z0-9_]/;function mw(e){if(!e)return[[]];if(e==="/")return[[hw]];if(!e.startsWith("/"))throw new Error({}.NODE_ENV!=="production"?`Route paths should start with a "/": "${e}" should be "/${e}".`:`Invalid path "${e}"`);function t(D){throw new Error(`ERR (${s})/"${m}": ${D}`)}let s=0,i=s;const n=[];let a;function u(){a&&n.push(a),a=[]}let c=0,h,m="",p="";function _(){m&&(s===0?a.push({type:0,value:m}):s===1||s===2||s===3?(a.length>1&&(h==="*"||h==="+")&&t(`A repeatable param (${m}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:m,regexp:p,repeatable:h==="*"||h==="+",optional:h==="*"||h==="?"})):t("Invalid state to consume buffer"),m="")}function w(){m+=h}for(;c<e.length;){if(h=e[c++],h==="\\"&&s!==2){i=s,s=4;continue}switch(s){case 0:h==="/"?(m&&_(),u()):h===":"?(_(),s=1):w();break;case 4:w(),s=i;break;case 1:h==="("?s=2:pw.test(h)?w():(_(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--);break;case 2:h===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+h:s=3:p+=h;break;case 3:_(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--,p="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${m}"`),_(),u(),n}function gw(e,t,s){const i=dw(mw(e.path),s);if({}.NODE_ENV!=="production"){const a=new Set;for(const u of i.keys)a.has(u.name)&&$e(`Found duplicated params with name "${u.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),a.add(u.name)}const n=Je(i,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function _w(e,t){const s=[],i=new Map;t=Rh({strict:!1,end:!0,sensitive:!1},t);function n(_){return i.get(_)}function a(_,w,D){const V=!D,F=kh(_);({}).NODE_ENV!=="production"&&ww(F,w),F.aliasOf=D&&D.record;const te=Rh(t,_),I=[F];if("alias"in _){const ye=typeof _.alias=="string"?[_.alias]:_.alias;for(const Z of ye)I.push(kh(Je({},F,{components:D?D.record.components:F.components,path:Z,aliasOf:D?D.record:F})))}let se,K;for(const ye of I){const{path:Z}=ye;if(w&&Z[0]!=="/"){const fe=w.record.path,ve=fe[fe.length-1]==="/"?"":"/";ye.path=w.record.path+(Z&&ve+Z)}if({}.NODE_ENV!=="production"&&ye.path==="*")throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(se=gw(ye,w,te),{}.NODE_ENV!=="production"&&w&&Z[0]==="/"&&Cw(se,w),D?(D.alias.push(se),{}.NODE_ENV!=="production"&&bw(D,se)):(K=K||se,K!==se&&K.alias.push(se),V&&_.name&&!Vh(se)&&({}.NODE_ENV!=="production"&&Ew(_,w),u(_.name))),Lh(se)&&h(se),F.children){const fe=F.children;for(let ve=0;ve<fe.length;ve++)a(fe[ve],se,D&&D.children[ve])}D=D||se}return K?()=>{u(K)}:Io}function u(_){if(Th(_)){const w=i.get(_);w&&(i.delete(_),s.splice(s.indexOf(w),1),w.children.forEach(u),w.alias.forEach(u))}else{const w=s.indexOf(_);w>-1&&(s.splice(w,1),_.record.name&&i.delete(_.record.name),_.children.forEach(u),_.alias.forEach(u))}}function c(){return s}function h(_){const w=Dw(_,s);s.splice(w,0,_),_.record.name&&!Vh(_)&&i.set(_.record.name,_)}function m(_,w){let D,V={},F,te;if("name"in _&&_.name){if(D=i.get(_.name),!D)throw Un(1,{location:_});if({}.NODE_ENV!=="production"){const K=Object.keys(_.params||{}).filter(ye=>!D.keys.find(Z=>Z.name===ye));K.length&&$e(`Discarded invalid param(s) "${K.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}te=D.record.name,V=Je(Ph(w.params,D.keys.filter(K=>!K.optional).concat(D.parent?D.parent.keys.filter(K=>K.optional):[]).map(K=>K.name)),_.params&&Ph(_.params,D.keys.map(K=>K.name))),F=D.stringify(V)}else if(_.path!=null)F=_.path,{}.NODE_ENV!=="production"&&!F.startsWith("/")&&$e(`The Matcher cannot resolve relative paths but received "${F}". Unless you directly called \`matcher.resolve("${F}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),D=s.find(K=>K.re.test(F)),D&&(V=D.parse(F),te=D.record.name);else{if(D=w.name?i.get(w.name):s.find(K=>K.re.test(w.path)),!D)throw Un(1,{location:_,currentLocation:w});te=D.record.name,V=Je({},w.params,_.params),F=D.stringify(V)}const I=[];let se=D;for(;se;)I.unshift(se.record),se=se.parent;return{name:te,path:F,params:V,matched:I,meta:yw(I)}}e.forEach(_=>a(_));function p(){s.length=0,i.clear()}return{addRoute:a,resolve:m,removeRoute:u,clearRoutes:p,getRoutes:c,getRecordMatcher:n}}function Ph(e,t){const s={};for(const i of t)i in e&&(s[i]=e[i]);return s}function kh(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:vw(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function vw(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const i in e.components)t[i]=typeof s=="object"?s[i]:s;return t}function Vh(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function yw(e){return e.reduce((t,s)=>Je(t,s.meta),{})}function Rh(e,t){const s={};for(const i in e)s[i]=i in t?t[i]:e[i];return s}function au(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function bw(e,t){for(const s of e.keys)if(!s.optional&&!t.keys.find(au.bind(null,s)))return $e(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`);for(const s of t.keys)if(!s.optional&&!e.keys.find(au.bind(null,s)))return $e(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`)}function ww(e,t){t&&t.record.name&&!e.name&&!e.path&&$e(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function Ew(e,t){for(let s=t;s;s=s.parent)if(s.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===s?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function Cw(e,t){for(const s of t.keys)if(!e.keys.find(au.bind(null,s)))return $e(`Absolute path "${e.record.path}" must have the exact same param named "${s.name}" as its parent "${t.record.path}".`)}function Dw(e,t){let s=0,i=t.length;for(;s!==i;){const a=s+i>>1;Ih(e,t[a])<0?i=a:s=a+1}const n=xw(e);return n&&(i=t.lastIndexOf(n,i-1),{}.NODE_ENV!=="production"&&i<0&&$e(`Finding ancestor route "${n.record.path}" failed for "${e.record.path}"`)),i}function xw(e){let t=e;for(;t=t.parent;)if(Lh(t)&&Ih(e,t)===0)return t}function Lh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Ow(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<i.length;++n){const a=i[n].replace(_h," "),u=a.indexOf("="),c=Ln(u<0?a:a.slice(0,u)),h=u<0?null:Ln(a.slice(u+1));if(c in t){let m=t[c];fs(m)||(m=t[c]=[m]),m.push(h)}else t[c]=h}return t}function Uh(e){let t="";for(let s in e){const i=e[s];if(s=$b(s),i==null){i!==void 0&&(t+=(t.length?"&":"")+s);continue}(fs(i)?i.map(a=>a&&ru(a)):[i&&ru(i)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+s,a!=null&&(t+="="+a))})}return t}function Sw(e){const t={};for(const s in e){const i=e[s];i!==void 0&&(t[s]=fs(i)?i.map(n=>n==null?null:""+n):i==null?i:""+i)}return t}const Tw=Symbol({}.NODE_ENV!=="production"?"router view location matched":""),Fh=Symbol({}.NODE_ENV!=="production"?"router view depth":""),Yi=Symbol({}.NODE_ENV!=="production"?"router":""),lu=Symbol({}.NODE_ENV!=="production"?"route location":""),uu=Symbol({}.NODE_ENV!=="production"?"router view location":"");function ko(){let e=[];function t(i){return e.push(i),()=>{const n=e.indexOf(i);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Ur(e,t,s,i,n,a=u=>u()){const u=i&&(i.enterCallbacks[n]=i.enterCallbacks[n]||[]);return()=>new Promise((c,h)=>{const m=w=>{w===!1?h(Un(4,{from:s,to:t})):w instanceof Error?h(w):Ki(w)?h(Un(2,{from:t,to:w})):(u&&i.enterCallbacks[n]===u&&typeof w=="function"&&u.push(w),c())},p=a(()=>e.call(i&&i.instances[n],t,s,{}.NODE_ENV!=="production"?Nw(m,t,s):m));let _=Promise.resolve(p);if(e.length<3&&(_=_.then(m)),{}.NODE_ENV!=="production"&&e.length>2){const w=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof p=="object"&&"then"in p)_=_.then(D=>m._called?D:($e(w),Promise.reject(new Error("Invalid navigation guard"))));else if(p!==void 0&&!m._called){$e(w),h(new Error("Invalid navigation guard"));return}}_.catch(w=>h(w))})}function Nw(e,t,s){let i=0;return function(){i++===1&&$e(`The "next" callback was called more than once in one navigation guard when going from "${s.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,i===1&&e.apply(null,arguments)}}function cu(e,t,s,i,n=a=>a()){const a=[];for(const u of e){({}).NODE_ENV!=="production"&&!u.components&&!u.children.length&&$e(`Record with path "${u.path}" is either missing a "component(s)" or "children" property.`);for(const c in u.components){let h=u.components[c];if({}.NODE_ENV!=="production"){if(!h||typeof h!="object"&&typeof h!="function")throw $e(`Component "${c}" in record with path "${u.path}" is not a valid component. Received "${String(h)}".`),new Error("Invalid route component");if("then"in h){$e(`Component "${c}" in record with path "${u.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const m=h;h=()=>m}else h.__asyncLoader&&!h.__warnedDefineAsync&&(h.__warnedDefineAsync=!0,$e(`Component "${c}" in record with path "${u.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(mh(h)){const p=(h.__vccOpts||h)[t];p&&a.push(Ur(p,s,i,u,c,n))}else{let m=h();({}).NODE_ENV!=="production"&&!("catch"in m)&&($e(`Component "${c}" in record with path "${u.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),m=Promise.resolve(m)),a.push(()=>m.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${c}" at "${u.path}"`);const _=Ab(p)?p.default:p;u.mods[c]=p,u.components[c]=_;const D=(_.__vccOpts||_)[t];return D&&Ur(D,s,i,u,c,n)()}))}}}return a}function Bh(e){const t=Vs(Yi),s=Vs(lu);let i=!1,n=null;const a=Ls(()=>{const p=Tr(e.to);return{}.NODE_ENV!=="production"&&(!i||p!==n)&&(Ki(p)||(i?$e(`Invalid value for prop "to" in useLink()
- to:`,p,`
- previous to:`,n,`
- props:`,e):$e(`Invalid value for prop "to" in useLink()
- to:`,p,`
- props:`,e)),n=p,i=!0),t.resolve(p)}),u=Ls(()=>{const{matched:p}=a.value,{length:_}=p,w=p[_-1],D=s.matched;if(!w||!D.length)return-1;const V=D.findIndex(Rr.bind(null,w));if(V>-1)return V;const F=$h(p[_-2]);return _>1&&$h(w)===F&&D[D.length-1].path!==F?D.findIndex(Rr.bind(null,p[_-2])):V}),c=Ls(()=>u.value>-1&&Pw(s.params,a.value.params)),h=Ls(()=>u.value>-1&&u.value===s.matched.length-1&&Ch(s.params,a.value.params));function m(p={}){if(Mw(p)){const _=t[Tr(e.replace)?"replace":"push"](Tr(e.to)).catch(Io);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>_),_}return Promise.resolve()}if({}.NODE_ENV!=="production"&&hr){const p=Vi();if(p){const _={route:a.value,isActive:c.value,isExactActive:h.value,error:null};p.__vrl_devtools=p.__vrl_devtools||[],p.__vrl_devtools.push(_),Gv(()=>{_.route=a.value,_.isActive=c.value,_.isExactActive=h.value,_.error=Ki(Tr(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:a,href:Ls(()=>a.value.href),isActive:c,isExactActive:h,navigate:m}}function Aw(e){return e.length===1?e[0]:e}const Iw=Ld({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Bh,setup(e,{slots:t}){const s=fi(Bh(e)),{options:i}=Vs(Yi),n=Ls(()=>({[jh(e.activeClass,i.linkActiveClass,"router-link-active")]:s.isActive,[jh(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const a=t.default&&Aw(t.default(s));return e.custom?a:Hl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},a)}}});function Mw(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Pw(e,t){for(const s in t){const i=t[s],n=e[s];if(typeof i=="string"){if(i!==n)return!1}else if(!fs(n)||n.length!==i.length||i.some((a,u)=>a!==n[u]))return!1}return!0}function $h(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const jh=(e,t,s)=>e??t??s,kw=Ld({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){({}).NODE_ENV!=="production"&&Rw();const i=Vs(uu),n=Ls(()=>e.route||i.value),a=Vs(Fh,0),u=Ls(()=>{let m=Tr(a);const{matched:p}=n.value;let _;for(;(_=p[m])&&!_.components;)m++;return m}),c=Ls(()=>n.value.matched[u.value]);Ai(Fh,Ls(()=>u.value+1)),Ai(Tw,c),Ai(uu,n);const h=ad();return Pn(()=>[h.value,c.value,e.name],([m,p,_],[w,D,V])=>{p&&(p.instances[_]=m,D&&D!==p&&m&&m===w&&(p.leaveGuards.size||(p.leaveGuards=D.leaveGuards),p.updateGuards.size||(p.updateGuards=D.updateGuards))),m&&p&&(!D||!Rr(p,D)||!w)&&(p.enterCallbacks[_]||[]).forEach(F=>F(m))},{flush:"post"}),()=>{const m=n.value,p=e.name,_=c.value,w=_&&_.components[p];if(!w)return Hh(s.default,{Component:w,route:m});const D=_.props[p],V=D?D===!0?m.params:typeof D=="function"?D(m):D:null,te=Hl(w,Je({},V,t,{onVnodeUnmounted:I=>{I.component.isUnmounted&&(_.instances[p]=null)},ref:h}));if({}.NODE_ENV!=="production"&&hr&&te.ref){const I={depth:u.value,name:_.name,path:_.path,meta:_.meta};(fs(te.ref)?te.ref.map(K=>K.i):[te.ref.i]).forEach(K=>{K.__vrv_devtools=I})}return Hh(s.default,{Component:te,route:m})||te}}});function Hh(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const Vw=kw;function Rw(){const e=Vi(),t=e.parent&&e.parent.type.name,s=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t==="KeepAlive"||t.includes("Transition"))&&typeof s=="object"&&s.name==="RouterView"){const i=t==="KeepAlive"?"keep-alive":"transition";$e(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${i}>
    <component :is="Component" />
  </${i}>
</router-view>`)}}function Vo(e,t){const s=Je({},e,{matched:e.matched.map(i=>Gw(i,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:s}}}function Qi(e){return{_custom:{display:e}}}let Lw=0;function Uw(e,t,s){if(t.__hasDevtools)return;t.__hasDevtools=!0;const i=Lw++;Zl({id:"org.vuejs.router"+(i?"."+i:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},n=>{typeof n.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.on.inspectComponent((p,_)=>{p.instanceData&&p.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:Vo(t.currentRoute.value,"Current Route")})}),n.on.visitComponentTree(({treeNode:p,componentInstance:_})=>{if(_.__vrv_devtools){const w=_.__vrv_devtools;p.tags.push({label:(w.name?`${w.name.toString()}: `:"")+w.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:qh})}fs(_.__vrl_devtools)&&(_.__devtoolsApi=n,_.__vrl_devtools.forEach(w=>{let D=w.route.path,V=Gh,F="",te=0;w.error?(D=w.error,V=Hw,te=qw):w.isExactActive?(V=Wh,F="This is exactly active"):w.isActive&&(V=zh,F="This link is active"),p.tags.push({label:D,textColor:te,tooltip:F,backgroundColor:V})}))}),Pn(t.currentRoute,()=>{h(),n.notifyComponentUpdate(),n.sendInspectorTree(c),n.sendInspectorState(c)});const a="router:navigations:"+i;n.addTimelineLayer({id:a,label:`Router${i?" "+i:""} Navigations`,color:4237508}),t.onError((p,_)=>{n.addTimelineEvent({layerId:a,event:{title:"Error during Navigation",subtitle:_.fullPath,logType:"error",time:n.now(),data:{error:p},groupId:_.meta.__navigationId}})});let u=0;t.beforeEach((p,_)=>{const w={guard:Qi("beforeEach"),from:Vo(_,"Current Location during this navigation"),to:Vo(p,"Target location")};Object.defineProperty(p.meta,"__navigationId",{value:u++}),n.addTimelineEvent({layerId:a,event:{time:n.now(),title:"Start of navigation",subtitle:p.fullPath,data:w,groupId:p.meta.__navigationId}})}),t.afterEach((p,_,w)=>{const D={guard:Qi("afterEach")};w?(D.failure={_custom:{type:Error,readOnly:!0,display:w?w.message:"",tooltip:"Navigation Failure",value:w}},D.status=Qi("❌")):D.status=Qi("✅"),D.from=Vo(_,"Current Location during this navigation"),D.to=Vo(p,"Target location"),n.addTimelineEvent({layerId:a,event:{title:"End of navigation",subtitle:p.fullPath,time:n.now(),data:D,logType:w?"warning":"default",groupId:p.meta.__navigationId}})});const c="router-inspector:"+i;n.addInspector({id:c,label:"Routes"+(i?" "+i:""),icon:"book",treeFilterPlaceholder:"Search routes"});function h(){if(!m)return;const p=m;let _=s.getRoutes().filter(w=>!w.parent||!w.parent.record.components);_.forEach(Qh),p.filter&&(_=_.filter(w=>du(w,p.filter.toLowerCase()))),_.forEach(w=>Yh(w,t.currentRoute.value)),p.rootNodes=_.map(Kh)}let m;n.on.getInspectorTree(p=>{m=p,p.app===e&&p.inspectorId===c&&h()}),n.on.getInspectorState(p=>{if(p.app===e&&p.inspectorId===c){const w=s.getRoutes().find(D=>D.record.__vd_id===p.nodeId);w&&(p.state={options:Bw(w)})}}),n.sendInspectorTree(c),n.sendInspectorState(c)})}function Fw(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function Bw(e){const{record:t}=e,s=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&s.push({editable:!1,key:"name",value:t.name}),s.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&s.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(i=>`${i.name}${Fw(i)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&s.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&s.push({editable:!1,key:"aliases",value:e.alias.map(i=>i.record.path)}),Object.keys(e.record.meta).length&&s.push({editable:!1,key:"meta",value:e.record.meta}),s.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(i=>i.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),s}const qh=15485081,zh=2450411,Wh=8702998,$w=2282478,Gh=16486972,jw=6710886,Hw=16704226,qw=12131356;function Kh(e){const t=[],{record:s}=e;s.name!=null&&t.push({label:String(s.name),textColor:0,backgroundColor:$w}),s.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Gh}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:qh}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:Wh}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:zh}),s.redirect&&t.push({label:typeof s.redirect=="string"?`redirect: ${s.redirect}`:"redirects",textColor:16777215,backgroundColor:jw});let i=s.__vd_id;return i==null&&(i=String(zw++),s.__vd_id=i),{id:i,label:s.path,tags:t,children:e.children.map(Kh)}}let zw=0;const Ww=/^\/(.*)\/([a-z]*)$/;function Yh(e,t){const s=t.matched.length&&Rr(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=s,s||(e.__vd_active=t.matched.some(i=>Rr(i,e.record))),e.children.forEach(i=>Yh(i,t))}function Qh(e){e.__vd_match=!1,e.children.forEach(Qh)}function du(e,t){const s=String(e.re).match(Ww);if(e.__vd_match=!1,!s||s.length<3)return!1;if(new RegExp(s[1].replace(/\$$/,""),s[2]).test(t))return e.children.forEach(u=>du(u,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const n=e.record.path.toLowerCase(),a=Ln(n);return!t.startsWith("/")&&(a.includes(t)||n.includes(t))||a.startsWith(t)||n.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(u=>du(u,t))}function Gw(e,t){const s={};for(const i in e)t.includes(i)||(s[i]=e[i]);return s}function Kw(e){const t=_w(e.routes,e),s=e.parseQuery||Ow,i=e.stringifyQuery||Uh,n=e.history;if({}.NODE_ENV!=="production"&&!n)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const a=ko(),u=ko(),c=ko(),h=D_(Lr);let m=Lr;hr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=tu.bind(null,R=>""+R),_=tu.bind(null,Hb),w=tu.bind(null,Ln);function D(R,oe){let ne,pe;return Th(R)?(ne=t.getRecordMatcher(R),{}.NODE_ENV!=="production"&&!ne&&$e(`Parent route "${String(R)}" not found when adding child route`,oe),pe=oe):pe=R,t.addRoute(pe,ne)}function V(R){const oe=t.getRecordMatcher(R);oe?t.removeRoute(oe):{}.NODE_ENV!=="production"&&$e(`Cannot remove non-existent route "${String(R)}"`)}function F(){return t.getRoutes().map(R=>R.record)}function te(R){return!!t.getRecordMatcher(R)}function I(R,oe){if(oe=Je({},oe||h.value),typeof R=="string"){const b=nu(s,R,oe.path),C=t.resolve({path:b.path},oe),P=n.createHref(b.fullPath);return{}.NODE_ENV!=="production"&&(P.startsWith("//")?$e(`Location "${R}" resolved to "${P}". A resolved location cannot start with multiple slashes.`):C.matched.length||$e(`No match found for location with path "${R}"`)),Je(b,C,{params:w(C.params),hash:Ln(b.hash),redirectedFrom:void 0,href:P})}if({}.NODE_ENV!=="production"&&!Ki(R))return $e(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,R),I({});let ne;if(R.path!=null)({}).NODE_ENV!=="production"&&"params"in R&&!("name"in R)&&Object.keys(R.params).length&&$e(`Path "${R.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),ne=Je({},R,{path:nu(s,R.path,oe.path).path});else{const b=Je({},R.params);for(const C in b)b[C]==null&&delete b[C];ne=Je({},R,{params:_(b)}),oe.params=_(oe.params)}const pe=t.resolve(ne,oe),ke=R.hash||"";({}).NODE_ENV!=="production"&&ke&&!ke.startsWith("#")&&$e(`A \`hash\` should always start with the character "#". Replace "${ke}" with "#${ke}".`),pe.params=p(w(pe.params));const ot=Wb(i,Je({},R,{hash:Bb(ke),path:pe.path})),Ve=n.createHref(ot);return{}.NODE_ENV!=="production"&&(Ve.startsWith("//")?$e(`Location "${R}" resolved to "${Ve}". A resolved location cannot start with multiple slashes.`):pe.matched.length||$e(`No match found for location with path "${R.path!=null?R.path:R}"`)),Je({fullPath:ot,hash:ke,query:i===Uh?Sw(R.query):R.query||{}},pe,{redirectedFrom:void 0,href:Ve})}function se(R){return typeof R=="string"?nu(s,R,h.value.path):Je({},R)}function K(R,oe){if(m!==R)return Un(8,{from:oe,to:R})}function ye(R){return ve(R)}function Z(R){return ye(Je(se(R),{replace:!0}))}function fe(R){const oe=R.matched[R.matched.length-1];if(oe&&oe.redirect){const{redirect:ne}=oe;let pe=typeof ne=="function"?ne(R):ne;if(typeof pe=="string"&&(pe=pe.includes("?")||pe.includes("#")?pe=se(pe):{path:pe},pe.params={}),{}.NODE_ENV!=="production"&&pe.path==null&&!("name"in pe))throw $e(`Invalid redirect found:
${JSON.stringify(pe,null,2)}
 when navigating to "${R.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return Je({query:R.query,hash:R.hash,params:pe.path!=null?{}:R.params},pe)}}function ve(R,oe){const ne=m=I(R),pe=h.value,ke=R.state,ot=R.force,Ve=R.replace===!0,b=fe(ne);if(b)return ve(Je(se(b),{state:typeof b=="object"?Je({},ke,b.state):ke,force:ot,replace:Ve}),oe||ne);const C=ne;C.redirectedFrom=oe;let P;return!ot&&Eh(i,pe,ne)&&(P=Un(16,{to:C,from:pe}),yt(pe,pe,!0,!1)),(P?Promise.resolve(P):A(C,pe)).catch(U=>pr(U)?pr(U,2)?U:es(U):we(U,C,pe)).then(U=>{if(U){if(pr(U,2))return{}.NODE_ENV!=="production"&&Eh(i,I(U.to),C)&&oe&&(oe._count=oe._count?oe._count+1:1)>30?($e(`Detected a possibly infinite redirection in a navigation guard when going from "${pe.fullPath}" to "${C.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):ve(Je({replace:Ve},se(U.to),{state:typeof U.to=="object"?Je({},ke,U.to.state):ke,force:ot}),oe||C)}else U=ue(C,pe,!0,Ve,ke);return be(C,pe,U),U})}function Ae(R,oe){const ne=K(R,oe);return ne?Promise.reject(ne):Promise.resolve()}function ae(R){const oe=hs.values().next().value;return oe&&typeof oe.runWithContext=="function"?oe.runWithContext(R):R()}function A(R,oe){let ne;const[pe,ke,ot]=Yw(R,oe);ne=cu(pe.reverse(),"beforeRouteLeave",R,oe);for(const b of pe)b.leaveGuards.forEach(C=>{ne.push(Ur(C,R,oe))});const Ve=Ae.bind(null,R,oe);return ne.push(Ve),ps(ne).then(()=>{ne=[];for(const b of a.list())ne.push(Ur(b,R,oe));return ne.push(Ve),ps(ne)}).then(()=>{ne=cu(ke,"beforeRouteUpdate",R,oe);for(const b of ke)b.updateGuards.forEach(C=>{ne.push(Ur(C,R,oe))});return ne.push(Ve),ps(ne)}).then(()=>{ne=[];for(const b of ot)if(b.beforeEnter)if(fs(b.beforeEnter))for(const C of b.beforeEnter)ne.push(Ur(C,R,oe));else ne.push(Ur(b.beforeEnter,R,oe));return ne.push(Ve),ps(ne)}).then(()=>(R.matched.forEach(b=>b.enterCallbacks={}),ne=cu(ot,"beforeRouteEnter",R,oe,ae),ne.push(Ve),ps(ne))).then(()=>{ne=[];for(const b of u.list())ne.push(Ur(b,R,oe));return ne.push(Ve),ps(ne)}).catch(b=>pr(b,8)?b:Promise.reject(b))}function be(R,oe,ne){c.list().forEach(pe=>ae(()=>pe(R,oe,ne)))}function ue(R,oe,ne,pe,ke){const ot=K(R,oe);if(ot)return ot;const Ve=oe===Lr,b=hr?history.state:{};ne&&(pe||Ve?n.replace(R.fullPath,Je({scroll:Ve&&b&&b.scroll},ke)):n.push(R.fullPath,ke)),h.value=R,yt(R,oe,ne,Ve),es()}let Ge;function _t(){Ge||(Ge=n.listen((R,oe,ne)=>{if(!zt.listening)return;const pe=I(R),ke=fe(pe);if(ke){ve(Je(ke,{replace:!0,force:!0}),pe).catch(Io);return}m=pe;const ot=h.value;hr&&ew(xh(ot.fullPath,ne.delta),Gi()),A(pe,ot).catch(Ve=>pr(Ve,12)?Ve:pr(Ve,2)?(ve(Je(se(Ve.to),{force:!0}),pe).then(b=>{pr(b,20)&&!ne.delta&&ne.type===Mo.pop&&n.go(-1,!1)}).catch(Io),Promise.reject()):(ne.delta&&n.go(-ne.delta,!1),we(Ve,pe,ot))).then(Ve=>{Ve=Ve||ue(pe,ot,!1),Ve&&(ne.delta&&!pr(Ve,8)?n.go(-ne.delta,!1):ne.type===Mo.pop&&pr(Ve,20)&&n.go(-1,!1)),be(pe,ot,Ve)}).catch(Io)}))}let mt=ko(),ft=ko(),Oe;function we(R,oe,ne){es(R);const pe=ft.list();return pe.length?pe.forEach(ke=>ke(R,oe,ne)):({}.NODE_ENV!=="production"&&$e("uncaught error during route navigation:"),console.error(R)),Promise.reject(R)}function Ut(){return Oe&&h.value!==Lr?Promise.resolve():new Promise((R,oe)=>{mt.add([R,oe])})}function es(R){return Oe||(Oe=!R,_t(),mt.list().forEach(([oe,ne])=>R?ne(R):oe()),mt.reset()),R}function yt(R,oe,ne,pe){const{scrollBehavior:ke}=e;if(!hr||!ke)return Promise.resolve();const ot=!ne&&tw(xh(R.fullPath,0))||(pe||!ne)&&history.state&&history.state.scroll||null;return ml().then(()=>ke(R,oe,ot)).then(Ve=>Ve&&Xb(Ve)).catch(Ve=>we(Ve,R,oe))}const ce=R=>n.go(R);let ze;const hs=new Set,zt={currentRoute:h,listening:!0,addRoute:D,removeRoute:V,clearRoutes:t.clearRoutes,hasRoute:te,getRoutes:F,resolve:I,options:e,push:ye,replace:Z,go:ce,back:()=>ce(-1),forward:()=>ce(1),beforeEach:a.add,beforeResolve:u.add,afterEach:c.add,onError:ft.add,isReady:Ut,install(R){const oe=this;R.component("RouterLink",Iw),R.component("RouterView",Vw),R.config.globalProperties.$router=oe,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>Tr(h)}),hr&&!ze&&h.value===Lr&&(ze=!0,ye(n.location).catch(ke=>{({}).NODE_ENV!=="production"&&$e("Unexpected error when starting the router:",ke)}));const ne={};for(const ke in Lr)Object.defineProperty(ne,ke,{get:()=>h.value[ke],enumerable:!0});R.provide(Yi,oe),R.provide(lu,od(ne)),R.provide(uu,h);const pe=R.unmount;hs.add(R),R.unmount=function(){hs.delete(R),hs.size<1&&(m=Lr,Ge&&Ge(),Ge=null,h.value=Lr,ze=!1,Oe=!1),pe()},{}.NODE_ENV!=="production"&&hr&&Uw(R,oe,t)}};function ps(R){return R.reduce((oe,ne)=>oe.then(()=>ae(ne)),Promise.resolve())}return zt}function Yw(e,t){const s=[],i=[],n=[],a=Math.max(t.matched.length,e.matched.length);for(let u=0;u<a;u++){const c=t.matched[u];c&&(e.matched.find(m=>Rr(m,c))?i.push(c):s.push(c));const h=e.matched[u];h&&(t.matched.find(m=>Rr(m,h))||n.push(h))}return[s,i,n]}function Zi(){return Vs(Yi)}function Zh(e){return Vs(lu)}const Qw="data:image/svg+xml;base64,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";var Ro=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Ji={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */Ji.exports,function(e,t){(function(){var s,i="4.17.21",n=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",c="Invalid `variable` option passed into `_.template`",h="__lodash_hash_undefined__",m=500,p="__lodash_placeholder__",_=1,w=2,D=4,V=1,F=2,te=1,I=2,se=4,K=8,ye=16,Z=32,fe=64,ve=128,Ae=256,ae=512,A=30,be="...",ue=800,Ge=16,_t=1,mt=2,ft=3,Oe=1/0,we=9007199254740991,Ut=17976931348623157e292,es=0/0,yt=**********,ce=yt-1,ze=yt>>>1,hs=[["ary",ve],["bind",te],["bindKey",I],["curry",K],["curryRight",ye],["flip",ae],["partial",Z],["partialRight",fe],["rearg",Ae]],zt="[object Arguments]",ps="[object Array]",R="[object AsyncFunction]",oe="[object Boolean]",ne="[object Date]",pe="[object DOMException]",ke="[object Error]",ot="[object Function]",Ve="[object GeneratorFunction]",b="[object Map]",C="[object Number]",P="[object Null]",U="[object Object]",H="[object Promise]",z="[object Proxy]",X="[object RegExp]",Y="[object Set]",J="[object String]",W="[object Symbol]",Ee="[object Undefined]",re="[object WeakMap]",_e="[object WeakSet]",Ce="[object ArrayBuffer]",Ue="[object DataView]",Xe="[object Float32Array]",Qe="[object Float64Array]",Ft="[object Int8Array]",Ot="[object Int16Array]",ts="[object Int32Array]",Bt="[object Uint8Array]",gr="[object Uint8ClampedArray]",Bn="[object Uint16Array]",At="[object Uint32Array]",Es=/\b__p \+= '';/g,oa=/\b(__p \+=) '' \+/g,TN=/(__e\(.*?\)|\b__t\)) \+\n'';/g,dp=/&(?:amp|lt|gt|quot|#39);/g,fp=/[&<>"']/g,NN=RegExp(dp.source),AN=RegExp(fp.source),IN=/<%-([\s\S]+?)%>/g,MN=/<%([\s\S]+?)%>/g,hp=/<%=([\s\S]+?)%>/g,PN=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,kN=/^\w*$/,VN=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,bu=/[\\^$.*+?()[\]{}|]/g,RN=RegExp(bu.source),wu=/^\s+/,LN=/\s/,UN=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,FN=/\{\n\/\* \[wrapped with (.+)\] \*/,BN=/,? & /,$N=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,jN=/[()=,{}\[\]\/\s]/,HN=/\\(\\)?/g,qN=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,pp=/\w*$/,zN=/^[-+]0x[0-9a-f]+$/i,WN=/^0b[01]+$/i,GN=/^\[object .+?Constructor\]$/,KN=/^0o[0-7]+$/i,YN=/^(?:0|[1-9]\d*)$/,QN=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ia=/($^)/,ZN=/['\n\r\u2028\u2029\\]/g,aa="\\ud800-\\udfff",JN="\\u0300-\\u036f",XN="\\ufe20-\\ufe2f",eA="\\u20d0-\\u20ff",mp=JN+XN+eA,gp="\\u2700-\\u27bf",_p="a-z\\xdf-\\xf6\\xf8-\\xff",tA="\\xac\\xb1\\xd7\\xf7",sA="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",rA="\\u2000-\\u206f",nA=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vp="A-Z\\xc0-\\xd6\\xd8-\\xde",yp="\\ufe0e\\ufe0f",bp=tA+sA+rA+nA,Eu="['’]",oA="["+aa+"]",wp="["+bp+"]",la="["+mp+"]",Ep="\\d+",iA="["+gp+"]",Cp="["+_p+"]",Dp="[^"+aa+bp+Ep+gp+_p+vp+"]",Cu="\\ud83c[\\udffb-\\udfff]",aA="(?:"+la+"|"+Cu+")",xp="[^"+aa+"]",Du="(?:\\ud83c[\\udde6-\\uddff]){2}",xu="[\\ud800-\\udbff][\\udc00-\\udfff]",$n="["+vp+"]",Op="\\u200d",Sp="(?:"+Cp+"|"+Dp+")",lA="(?:"+$n+"|"+Dp+")",Tp="(?:"+Eu+"(?:d|ll|m|re|s|t|ve))?",Np="(?:"+Eu+"(?:D|LL|M|RE|S|T|VE))?",Ap=aA+"?",Ip="["+yp+"]?",uA="(?:"+Op+"(?:"+[xp,Du,xu].join("|")+")"+Ip+Ap+")*",cA="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",dA="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Mp=Ip+Ap+uA,fA="(?:"+[iA,Du,xu].join("|")+")"+Mp,hA="(?:"+[xp+la+"?",la,Du,xu,oA].join("|")+")",pA=RegExp(Eu,"g"),mA=RegExp(la,"g"),Ou=RegExp(Cu+"(?="+Cu+")|"+hA+Mp,"g"),gA=RegExp([$n+"?"+Cp+"+"+Tp+"(?="+[wp,$n,"$"].join("|")+")",lA+"+"+Np+"(?="+[wp,$n+Sp,"$"].join("|")+")",$n+"?"+Sp+"+"+Tp,$n+"+"+Np,dA,cA,Ep,fA].join("|"),"g"),_A=RegExp("["+Op+aa+mp+yp+"]"),vA=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,yA=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],bA=-1,ht={};ht[Xe]=ht[Qe]=ht[Ft]=ht[Ot]=ht[ts]=ht[Bt]=ht[gr]=ht[Bn]=ht[At]=!0,ht[zt]=ht[ps]=ht[Ce]=ht[oe]=ht[Ue]=ht[ne]=ht[ke]=ht[ot]=ht[b]=ht[C]=ht[U]=ht[X]=ht[Y]=ht[J]=ht[re]=!1;var ut={};ut[zt]=ut[ps]=ut[Ce]=ut[Ue]=ut[oe]=ut[ne]=ut[Xe]=ut[Qe]=ut[Ft]=ut[Ot]=ut[ts]=ut[b]=ut[C]=ut[U]=ut[X]=ut[Y]=ut[J]=ut[W]=ut[Bt]=ut[gr]=ut[Bn]=ut[At]=!0,ut[ke]=ut[ot]=ut[re]=!1;var wA={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},EA={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},CA={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},DA={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},xA=parseFloat,OA=parseInt,Pp=typeof Ro=="object"&&Ro&&Ro.Object===Object&&Ro,SA=typeof self=="object"&&self&&self.Object===Object&&self,$t=Pp||SA||Function("return this")(),Su=t&&!t.nodeType&&t,mn=Su&&!0&&e&&!e.nodeType&&e,kp=mn&&mn.exports===Su,Tu=kp&&Pp.process,Cs=function(){try{var S=mn&&mn.require&&mn.require("util").types;return S||Tu&&Tu.binding&&Tu.binding("util")}catch{}}(),Vp=Cs&&Cs.isArrayBuffer,Rp=Cs&&Cs.isDate,Lp=Cs&&Cs.isMap,Up=Cs&&Cs.isRegExp,Fp=Cs&&Cs.isSet,Bp=Cs&&Cs.isTypedArray;function ms(S,L,M){switch(M.length){case 0:return S.call(L);case 1:return S.call(L,M[0]);case 2:return S.call(L,M[0],M[1]);case 3:return S.call(L,M[0],M[1],M[2])}return S.apply(L,M)}function TA(S,L,M,de){for(var Pe=-1,et=S==null?0:S.length;++Pe<et;){var It=S[Pe];L(de,It,M(It),S)}return de}function Ds(S,L){for(var M=-1,de=S==null?0:S.length;++M<de&&L(S[M],M,S)!==!1;);return S}function NA(S,L){for(var M=S==null?0:S.length;M--&&L(S[M],M,S)!==!1;);return S}function $p(S,L){for(var M=-1,de=S==null?0:S.length;++M<de;)if(!L(S[M],M,S))return!1;return!0}function Fr(S,L){for(var M=-1,de=S==null?0:S.length,Pe=0,et=[];++M<de;){var It=S[M];L(It,M,S)&&(et[Pe++]=It)}return et}function ua(S,L){var M=S==null?0:S.length;return!!M&&jn(S,L,0)>-1}function Nu(S,L,M){for(var de=-1,Pe=S==null?0:S.length;++de<Pe;)if(M(L,S[de]))return!0;return!1}function gt(S,L){for(var M=-1,de=S==null?0:S.length,Pe=Array(de);++M<de;)Pe[M]=L(S[M],M,S);return Pe}function Br(S,L){for(var M=-1,de=L.length,Pe=S.length;++M<de;)S[Pe+M]=L[M];return S}function Au(S,L,M,de){var Pe=-1,et=S==null?0:S.length;for(de&&et&&(M=S[++Pe]);++Pe<et;)M=L(M,S[Pe],Pe,S);return M}function AA(S,L,M,de){var Pe=S==null?0:S.length;for(de&&Pe&&(M=S[--Pe]);Pe--;)M=L(M,S[Pe],Pe,S);return M}function Iu(S,L){for(var M=-1,de=S==null?0:S.length;++M<de;)if(L(S[M],M,S))return!0;return!1}var IA=Mu("length");function MA(S){return S.split("")}function PA(S){return S.match($N)||[]}function jp(S,L,M){var de;return M(S,function(Pe,et,It){if(L(Pe,et,It))return de=et,!1}),de}function ca(S,L,M,de){for(var Pe=S.length,et=M+(de?1:-1);de?et--:++et<Pe;)if(L(S[et],et,S))return et;return-1}function jn(S,L,M){return L===L?zA(S,L,M):ca(S,Hp,M)}function kA(S,L,M,de){for(var Pe=M-1,et=S.length;++Pe<et;)if(de(S[Pe],L))return Pe;return-1}function Hp(S){return S!==S}function qp(S,L){var M=S==null?0:S.length;return M?ku(S,L)/M:es}function Mu(S){return function(L){return L==null?s:L[S]}}function Pu(S){return function(L){return S==null?s:S[L]}}function zp(S,L,M,de,Pe){return Pe(S,function(et,It,at){M=de?(de=!1,et):L(M,et,It,at)}),M}function VA(S,L){var M=S.length;for(S.sort(L);M--;)S[M]=S[M].value;return S}function ku(S,L){for(var M,de=-1,Pe=S.length;++de<Pe;){var et=L(S[de]);et!==s&&(M=M===s?et:M+et)}return M}function Vu(S,L){for(var M=-1,de=Array(S);++M<S;)de[M]=L(M);return de}function RA(S,L){return gt(L,function(M){return[M,S[M]]})}function Wp(S){return S&&S.slice(0,Qp(S)+1).replace(wu,"")}function gs(S){return function(L){return S(L)}}function Ru(S,L){return gt(L,function(M){return S[M]})}function Ho(S,L){return S.has(L)}function Gp(S,L){for(var M=-1,de=S.length;++M<de&&jn(L,S[M],0)>-1;);return M}function Kp(S,L){for(var M=S.length;M--&&jn(L,S[M],0)>-1;);return M}function LA(S,L){for(var M=S.length,de=0;M--;)S[M]===L&&++de;return de}var UA=Pu(wA),FA=Pu(EA);function BA(S){return"\\"+DA[S]}function $A(S,L){return S==null?s:S[L]}function Hn(S){return _A.test(S)}function jA(S){return vA.test(S)}function HA(S){for(var L,M=[];!(L=S.next()).done;)M.push(L.value);return M}function Lu(S){var L=-1,M=Array(S.size);return S.forEach(function(de,Pe){M[++L]=[Pe,de]}),M}function Yp(S,L){return function(M){return S(L(M))}}function $r(S,L){for(var M=-1,de=S.length,Pe=0,et=[];++M<de;){var It=S[M];(It===L||It===p)&&(S[M]=p,et[Pe++]=M)}return et}function da(S){var L=-1,M=Array(S.size);return S.forEach(function(de){M[++L]=de}),M}function qA(S){var L=-1,M=Array(S.size);return S.forEach(function(de){M[++L]=[de,de]}),M}function zA(S,L,M){for(var de=M-1,Pe=S.length;++de<Pe;)if(S[de]===L)return de;return-1}function WA(S,L,M){for(var de=M+1;de--;)if(S[de]===L)return de;return de}function qn(S){return Hn(S)?KA(S):IA(S)}function Fs(S){return Hn(S)?YA(S):MA(S)}function Qp(S){for(var L=S.length;L--&&LN.test(S.charAt(L)););return L}var GA=Pu(CA);function KA(S){for(var L=Ou.lastIndex=0;Ou.test(S);)++L;return L}function YA(S){return S.match(Ou)||[]}function QA(S){return S.match(gA)||[]}var ZA=function S(L){L=L==null?$t:zn.defaults($t.Object(),L,zn.pick($t,yA));var M=L.Array,de=L.Date,Pe=L.Error,et=L.Function,It=L.Math,at=L.Object,Uu=L.RegExp,JA=L.String,xs=L.TypeError,fa=M.prototype,XA=et.prototype,Wn=at.prototype,ha=L["__core-js_shared__"],pa=XA.toString,it=Wn.hasOwnProperty,eI=0,Zp=function(){var r=/[^.]+$/.exec(ha&&ha.keys&&ha.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),ma=Wn.toString,tI=pa.call(at),sI=$t._,rI=Uu("^"+pa.call(it).replace(bu,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ga=kp?L.Buffer:s,jr=L.Symbol,_a=L.Uint8Array,Jp=ga?ga.allocUnsafe:s,va=Yp(at.getPrototypeOf,at),Xp=at.create,em=Wn.propertyIsEnumerable,ya=fa.splice,tm=jr?jr.isConcatSpreadable:s,qo=jr?jr.iterator:s,gn=jr?jr.toStringTag:s,ba=function(){try{var r=wn(at,"defineProperty");return r({},"",{}),r}catch{}}(),nI=L.clearTimeout!==$t.clearTimeout&&L.clearTimeout,oI=de&&de.now!==$t.Date.now&&de.now,iI=L.setTimeout!==$t.setTimeout&&L.setTimeout,wa=It.ceil,Ea=It.floor,Fu=at.getOwnPropertySymbols,aI=ga?ga.isBuffer:s,sm=L.isFinite,lI=fa.join,uI=Yp(at.keys,at),Mt=It.max,Wt=It.min,cI=de.now,dI=L.parseInt,rm=It.random,fI=fa.reverse,Bu=wn(L,"DataView"),zo=wn(L,"Map"),$u=wn(L,"Promise"),Gn=wn(L,"Set"),Wo=wn(L,"WeakMap"),Go=wn(at,"create"),Ca=Wo&&new Wo,Kn={},hI=En(Bu),pI=En(zo),mI=En($u),gI=En(Gn),_I=En(Wo),Da=jr?jr.prototype:s,Ko=Da?Da.valueOf:s,nm=Da?Da.toString:s;function v(r){if(bt(r)&&!Re(r)&&!(r instanceof We)){if(r instanceof Os)return r;if(it.call(r,"__wrapped__"))return og(r)}return new Os(r)}var Yn=function(){function r(){}return function(o){if(!vt(o))return{};if(Xp)return Xp(o);r.prototype=o;var l=new r;return r.prototype=s,l}}();function xa(){}function Os(r,o){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!o,this.__index__=0,this.__values__=s}v.templateSettings={escape:IN,evaluate:MN,interpolate:hp,variable:"",imports:{_:v}},v.prototype=xa.prototype,v.prototype.constructor=v,Os.prototype=Yn(xa.prototype),Os.prototype.constructor=Os;function We(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=yt,this.__views__=[]}function vI(){var r=new We(this.__wrapped__);return r.__actions__=os(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=os(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=os(this.__views__),r}function yI(){if(this.__filtered__){var r=new We(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function bI(){var r=this.__wrapped__.value(),o=this.__dir__,l=Re(r),d=o<0,g=l?r.length:0,y=MM(0,g,this.__views__),E=y.start,x=y.end,T=x-E,B=d?x:E-1,j=this.__iteratees__,q=j.length,le=0,ge=Wt(T,this.__takeCount__);if(!l||!d&&g==T&&ge==T)return Tm(r,this.__actions__);var Se=[];e:for(;T--&&le<ge;){B+=o;for(var Be=-1,Te=r[B];++Be<q;){var qe=j[Be],Ke=qe.iteratee,ys=qe.type,ns=Ke(Te);if(ys==mt)Te=ns;else if(!ns){if(ys==_t)continue e;break e}}Se[le++]=Te}return Se}We.prototype=Yn(xa.prototype),We.prototype.constructor=We;function _n(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function wI(){this.__data__=Go?Go(null):{},this.size=0}function EI(r){var o=this.has(r)&&delete this.__data__[r];return this.size-=o?1:0,o}function CI(r){var o=this.__data__;if(Go){var l=o[r];return l===h?s:l}return it.call(o,r)?o[r]:s}function DI(r){var o=this.__data__;return Go?o[r]!==s:it.call(o,r)}function xI(r,o){var l=this.__data__;return this.size+=this.has(r)?0:1,l[r]=Go&&o===s?h:o,this}_n.prototype.clear=wI,_n.prototype.delete=EI,_n.prototype.get=CI,_n.prototype.has=DI,_n.prototype.set=xI;function _r(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function OI(){this.__data__=[],this.size=0}function SI(r){var o=this.__data__,l=Oa(o,r);if(l<0)return!1;var d=o.length-1;return l==d?o.pop():ya.call(o,l,1),--this.size,!0}function TI(r){var o=this.__data__,l=Oa(o,r);return l<0?s:o[l][1]}function NI(r){return Oa(this.__data__,r)>-1}function AI(r,o){var l=this.__data__,d=Oa(l,r);return d<0?(++this.size,l.push([r,o])):l[d][1]=o,this}_r.prototype.clear=OI,_r.prototype.delete=SI,_r.prototype.get=TI,_r.prototype.has=NI,_r.prototype.set=AI;function vr(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function II(){this.size=0,this.__data__={hash:new _n,map:new(zo||_r),string:new _n}}function MI(r){var o=Ua(this,r).delete(r);return this.size-=o?1:0,o}function PI(r){return Ua(this,r).get(r)}function kI(r){return Ua(this,r).has(r)}function VI(r,o){var l=Ua(this,r),d=l.size;return l.set(r,o),this.size+=l.size==d?0:1,this}vr.prototype.clear=II,vr.prototype.delete=MI,vr.prototype.get=PI,vr.prototype.has=kI,vr.prototype.set=VI;function vn(r){var o=-1,l=r==null?0:r.length;for(this.__data__=new vr;++o<l;)this.add(r[o])}function RI(r){return this.__data__.set(r,h),this}function LI(r){return this.__data__.has(r)}vn.prototype.add=vn.prototype.push=RI,vn.prototype.has=LI;function Bs(r){var o=this.__data__=new _r(r);this.size=o.size}function UI(){this.__data__=new _r,this.size=0}function FI(r){var o=this.__data__,l=o.delete(r);return this.size=o.size,l}function BI(r){return this.__data__.get(r)}function $I(r){return this.__data__.has(r)}function jI(r,o){var l=this.__data__;if(l instanceof _r){var d=l.__data__;if(!zo||d.length<n-1)return d.push([r,o]),this.size=++l.size,this;l=this.__data__=new vr(d)}return l.set(r,o),this.size=l.size,this}Bs.prototype.clear=UI,Bs.prototype.delete=FI,Bs.prototype.get=BI,Bs.prototype.has=$I,Bs.prototype.set=jI;function om(r,o){var l=Re(r),d=!l&&Cn(r),g=!l&&!d&&Gr(r),y=!l&&!d&&!g&&Xn(r),E=l||d||g||y,x=E?Vu(r.length,JA):[],T=x.length;for(var B in r)(o||it.call(r,B))&&!(E&&(B=="length"||g&&(B=="offset"||B=="parent")||y&&(B=="buffer"||B=="byteLength"||B=="byteOffset")||Er(B,T)))&&x.push(B);return x}function im(r){var o=r.length;return o?r[Ju(0,o-1)]:s}function HI(r,o){return Fa(os(r),yn(o,0,r.length))}function qI(r){return Fa(os(r))}function ju(r,o,l){(l!==s&&!$s(r[o],l)||l===s&&!(o in r))&&yr(r,o,l)}function Yo(r,o,l){var d=r[o];(!(it.call(r,o)&&$s(d,l))||l===s&&!(o in r))&&yr(r,o,l)}function Oa(r,o){for(var l=r.length;l--;)if($s(r[l][0],o))return l;return-1}function zI(r,o,l,d){return Hr(r,function(g,y,E){o(d,g,l(g),E)}),d}function am(r,o){return r&&Js(o,kt(o),r)}function WI(r,o){return r&&Js(o,as(o),r)}function yr(r,o,l){o=="__proto__"&&ba?ba(r,o,{configurable:!0,enumerable:!0,value:l,writable:!0}):r[o]=l}function Hu(r,o){for(var l=-1,d=o.length,g=M(d),y=r==null;++l<d;)g[l]=y?s:Cc(r,o[l]);return g}function yn(r,o,l){return r===r&&(l!==s&&(r=r<=l?r:l),o!==s&&(r=r>=o?r:o)),r}function Ss(r,o,l,d,g,y){var E,x=o&_,T=o&w,B=o&D;if(l&&(E=g?l(r,d,g,y):l(r)),E!==s)return E;if(!vt(r))return r;var j=Re(r);if(j){if(E=kM(r),!x)return os(r,E)}else{var q=Gt(r),le=q==ot||q==Ve;if(Gr(r))return Im(r,x);if(q==U||q==zt||le&&!g){if(E=T||le?{}:Qm(r),!x)return T?CM(r,WI(E,r)):EM(r,am(E,r))}else{if(!ut[q])return g?r:{};E=VM(r,q,x)}}y||(y=new Bs);var ge=y.get(r);if(ge)return ge;y.set(r,E),xg(r)?r.forEach(function(Te){E.add(Ss(Te,o,l,Te,r,y))}):Cg(r)&&r.forEach(function(Te,qe){E.set(qe,Ss(Te,o,l,qe,r,y))});var Se=B?T?uc:lc:T?as:kt,Be=j?s:Se(r);return Ds(Be||r,function(Te,qe){Be&&(qe=Te,Te=r[qe]),Yo(E,qe,Ss(Te,o,l,qe,r,y))}),E}function GI(r){var o=kt(r);return function(l){return lm(l,r,o)}}function lm(r,o,l){var d=l.length;if(r==null)return!d;for(r=at(r);d--;){var g=l[d],y=o[g],E=r[g];if(E===s&&!(g in r)||!y(E))return!1}return!0}function um(r,o,l){if(typeof r!="function")throw new xs(u);return si(function(){r.apply(s,l)},o)}function Qo(r,o,l,d){var g=-1,y=ua,E=!0,x=r.length,T=[],B=o.length;if(!x)return T;l&&(o=gt(o,gs(l))),d?(y=Nu,E=!1):o.length>=n&&(y=Ho,E=!1,o=new vn(o));e:for(;++g<x;){var j=r[g],q=l==null?j:l(j);if(j=d||j!==0?j:0,E&&q===q){for(var le=B;le--;)if(o[le]===q)continue e;T.push(j)}else y(o,q,d)||T.push(j)}return T}var Hr=Rm(Zs),cm=Rm(zu,!0);function KI(r,o){var l=!0;return Hr(r,function(d,g,y){return l=!!o(d,g,y),l}),l}function Sa(r,o,l){for(var d=-1,g=r.length;++d<g;){var y=r[d],E=o(y);if(E!=null&&(x===s?E===E&&!vs(E):l(E,x)))var x=E,T=y}return T}function YI(r,o,l,d){var g=r.length;for(l=Fe(l),l<0&&(l=-l>g?0:g+l),d=d===s||d>g?g:Fe(d),d<0&&(d+=g),d=l>d?0:Sg(d);l<d;)r[l++]=o;return r}function dm(r,o){var l=[];return Hr(r,function(d,g,y){o(d,g,y)&&l.push(d)}),l}function jt(r,o,l,d,g){var y=-1,E=r.length;for(l||(l=LM),g||(g=[]);++y<E;){var x=r[y];o>0&&l(x)?o>1?jt(x,o-1,l,d,g):Br(g,x):d||(g[g.length]=x)}return g}var qu=Lm(),fm=Lm(!0);function Zs(r,o){return r&&qu(r,o,kt)}function zu(r,o){return r&&fm(r,o,kt)}function Ta(r,o){return Fr(o,function(l){return Cr(r[l])})}function bn(r,o){o=zr(o,r);for(var l=0,d=o.length;r!=null&&l<d;)r=r[Xs(o[l++])];return l&&l==d?r:s}function hm(r,o,l){var d=o(r);return Re(r)?d:Br(d,l(r))}function ss(r){return r==null?r===s?Ee:P:gn&&gn in at(r)?IM(r):qM(r)}function Wu(r,o){return r>o}function QI(r,o){return r!=null&&it.call(r,o)}function ZI(r,o){return r!=null&&o in at(r)}function JI(r,o,l){return r>=Wt(o,l)&&r<Mt(o,l)}function Gu(r,o,l){for(var d=l?Nu:ua,g=r[0].length,y=r.length,E=y,x=M(y),T=1/0,B=[];E--;){var j=r[E];E&&o&&(j=gt(j,gs(o))),T=Wt(j.length,T),x[E]=!l&&(o||g>=120&&j.length>=120)?new vn(E&&j):s}j=r[0];var q=-1,le=x[0];e:for(;++q<g&&B.length<T;){var ge=j[q],Se=o?o(ge):ge;if(ge=l||ge!==0?ge:0,!(le?Ho(le,Se):d(B,Se,l))){for(E=y;--E;){var Be=x[E];if(!(Be?Ho(Be,Se):d(r[E],Se,l)))continue e}le&&le.push(Se),B.push(ge)}}return B}function XI(r,o,l,d){return Zs(r,function(g,y,E){o(d,l(g),y,E)}),d}function Zo(r,o,l){o=zr(o,r),r=eg(r,o);var d=r==null?r:r[Xs(Ns(o))];return d==null?s:ms(d,r,l)}function pm(r){return bt(r)&&ss(r)==zt}function eM(r){return bt(r)&&ss(r)==Ce}function tM(r){return bt(r)&&ss(r)==ne}function Jo(r,o,l,d,g){return r===o?!0:r==null||o==null||!bt(r)&&!bt(o)?r!==r&&o!==o:sM(r,o,l,d,Jo,g)}function sM(r,o,l,d,g,y){var E=Re(r),x=Re(o),T=E?ps:Gt(r),B=x?ps:Gt(o);T=T==zt?U:T,B=B==zt?U:B;var j=T==U,q=B==U,le=T==B;if(le&&Gr(r)){if(!Gr(o))return!1;E=!0,j=!1}if(le&&!j)return y||(y=new Bs),E||Xn(r)?Gm(r,o,l,d,g,y):NM(r,o,T,l,d,g,y);if(!(l&V)){var ge=j&&it.call(r,"__wrapped__"),Se=q&&it.call(o,"__wrapped__");if(ge||Se){var Be=ge?r.value():r,Te=Se?o.value():o;return y||(y=new Bs),g(Be,Te,l,d,y)}}return le?(y||(y=new Bs),AM(r,o,l,d,g,y)):!1}function rM(r){return bt(r)&&Gt(r)==b}function Ku(r,o,l,d){var g=l.length,y=g,E=!d;if(r==null)return!y;for(r=at(r);g--;){var x=l[g];if(E&&x[2]?x[1]!==r[x[0]]:!(x[0]in r))return!1}for(;++g<y;){x=l[g];var T=x[0],B=r[T],j=x[1];if(E&&x[2]){if(B===s&&!(T in r))return!1}else{var q=new Bs;if(d)var le=d(B,j,T,r,o,q);if(!(le===s?Jo(j,B,V|F,d,q):le))return!1}}return!0}function mm(r){if(!vt(r)||FM(r))return!1;var o=Cr(r)?rI:GN;return o.test(En(r))}function nM(r){return bt(r)&&ss(r)==X}function oM(r){return bt(r)&&Gt(r)==Y}function iM(r){return bt(r)&&za(r.length)&&!!ht[ss(r)]}function gm(r){return typeof r=="function"?r:r==null?ls:typeof r=="object"?Re(r)?ym(r[0],r[1]):vm(r):Ug(r)}function Yu(r){if(!ti(r))return uI(r);var o=[];for(var l in at(r))it.call(r,l)&&l!="constructor"&&o.push(l);return o}function aM(r){if(!vt(r))return HM(r);var o=ti(r),l=[];for(var d in r)d=="constructor"&&(o||!it.call(r,d))||l.push(d);return l}function Qu(r,o){return r<o}function _m(r,o){var l=-1,d=is(r)?M(r.length):[];return Hr(r,function(g,y,E){d[++l]=o(g,y,E)}),d}function vm(r){var o=dc(r);return o.length==1&&o[0][2]?Jm(o[0][0],o[0][1]):function(l){return l===r||Ku(l,r,o)}}function ym(r,o){return hc(r)&&Zm(o)?Jm(Xs(r),o):function(l){var d=Cc(l,r);return d===s&&d===o?Dc(l,r):Jo(o,d,V|F)}}function Na(r,o,l,d,g){r!==o&&qu(o,function(y,E){if(g||(g=new Bs),vt(y))lM(r,o,E,l,Na,d,g);else{var x=d?d(mc(r,E),y,E+"",r,o,g):s;x===s&&(x=y),ju(r,E,x)}},as)}function lM(r,o,l,d,g,y,E){var x=mc(r,l),T=mc(o,l),B=E.get(T);if(B){ju(r,l,B);return}var j=y?y(x,T,l+"",r,o,E):s,q=j===s;if(q){var le=Re(T),ge=!le&&Gr(T),Se=!le&&!ge&&Xn(T);j=T,le||ge||Se?Re(x)?j=x:Et(x)?j=os(x):ge?(q=!1,j=Im(T,!0)):Se?(q=!1,j=Mm(T,!0)):j=[]:ri(T)||Cn(T)?(j=x,Cn(x)?j=Tg(x):(!vt(x)||Cr(x))&&(j=Qm(T))):q=!1}q&&(E.set(T,j),g(j,T,d,y,E),E.delete(T)),ju(r,l,j)}function bm(r,o){var l=r.length;if(l)return o+=o<0?l:0,Er(o,l)?r[o]:s}function wm(r,o,l){o.length?o=gt(o,function(y){return Re(y)?function(E){return bn(E,y.length===1?y[0]:y)}:y}):o=[ls];var d=-1;o=gt(o,gs(De()));var g=_m(r,function(y,E,x){var T=gt(o,function(B){return B(y)});return{criteria:T,index:++d,value:y}});return VA(g,function(y,E){return wM(y,E,l)})}function uM(r,o){return Em(r,o,function(l,d){return Dc(r,d)})}function Em(r,o,l){for(var d=-1,g=o.length,y={};++d<g;){var E=o[d],x=bn(r,E);l(x,E)&&Xo(y,zr(E,r),x)}return y}function cM(r){return function(o){return bn(o,r)}}function Zu(r,o,l,d){var g=d?kA:jn,y=-1,E=o.length,x=r;for(r===o&&(o=os(o)),l&&(x=gt(r,gs(l)));++y<E;)for(var T=0,B=o[y],j=l?l(B):B;(T=g(x,j,T,d))>-1;)x!==r&&ya.call(x,T,1),ya.call(r,T,1);return r}function Cm(r,o){for(var l=r?o.length:0,d=l-1;l--;){var g=o[l];if(l==d||g!==y){var y=g;Er(g)?ya.call(r,g,1):tc(r,g)}}return r}function Ju(r,o){return r+Ea(rm()*(o-r+1))}function dM(r,o,l,d){for(var g=-1,y=Mt(wa((o-r)/(l||1)),0),E=M(y);y--;)E[d?y:++g]=r,r+=l;return E}function Xu(r,o){var l="";if(!r||o<1||o>we)return l;do o%2&&(l+=r),o=Ea(o/2),o&&(r+=r);while(o);return l}function je(r,o){return gc(Xm(r,o,ls),r+"")}function fM(r){return im(eo(r))}function hM(r,o){var l=eo(r);return Fa(l,yn(o,0,l.length))}function Xo(r,o,l,d){if(!vt(r))return r;o=zr(o,r);for(var g=-1,y=o.length,E=y-1,x=r;x!=null&&++g<y;){var T=Xs(o[g]),B=l;if(T==="__proto__"||T==="constructor"||T==="prototype")return r;if(g!=E){var j=x[T];B=d?d(j,T,x):s,B===s&&(B=vt(j)?j:Er(o[g+1])?[]:{})}Yo(x,T,B),x=x[T]}return r}var Dm=Ca?function(r,o){return Ca.set(r,o),r}:ls,pM=ba?function(r,o){return ba(r,"toString",{configurable:!0,enumerable:!1,value:Oc(o),writable:!0})}:ls;function mM(r){return Fa(eo(r))}function Ts(r,o,l){var d=-1,g=r.length;o<0&&(o=-o>g?0:g+o),l=l>g?g:l,l<0&&(l+=g),g=o>l?0:l-o>>>0,o>>>=0;for(var y=M(g);++d<g;)y[d]=r[d+o];return y}function gM(r,o){var l;return Hr(r,function(d,g,y){return l=o(d,g,y),!l}),!!l}function Aa(r,o,l){var d=0,g=r==null?d:r.length;if(typeof o=="number"&&o===o&&g<=ze){for(;d<g;){var y=d+g>>>1,E=r[y];E!==null&&!vs(E)&&(l?E<=o:E<o)?d=y+1:g=y}return g}return ec(r,o,ls,l)}function ec(r,o,l,d){var g=0,y=r==null?0:r.length;if(y===0)return 0;o=l(o);for(var E=o!==o,x=o===null,T=vs(o),B=o===s;g<y;){var j=Ea((g+y)/2),q=l(r[j]),le=q!==s,ge=q===null,Se=q===q,Be=vs(q);if(E)var Te=d||Se;else B?Te=Se&&(d||le):x?Te=Se&&le&&(d||!ge):T?Te=Se&&le&&!ge&&(d||!Be):ge||Be?Te=!1:Te=d?q<=o:q<o;Te?g=j+1:y=j}return Wt(y,ce)}function xm(r,o){for(var l=-1,d=r.length,g=0,y=[];++l<d;){var E=r[l],x=o?o(E):E;if(!l||!$s(x,T)){var T=x;y[g++]=E===0?0:E}}return y}function Om(r){return typeof r=="number"?r:vs(r)?es:+r}function _s(r){if(typeof r=="string")return r;if(Re(r))return gt(r,_s)+"";if(vs(r))return nm?nm.call(r):"";var o=r+"";return o=="0"&&1/r==-Oe?"-0":o}function qr(r,o,l){var d=-1,g=ua,y=r.length,E=!0,x=[],T=x;if(l)E=!1,g=Nu;else if(y>=n){var B=o?null:SM(r);if(B)return da(B);E=!1,g=Ho,T=new vn}else T=o?[]:x;e:for(;++d<y;){var j=r[d],q=o?o(j):j;if(j=l||j!==0?j:0,E&&q===q){for(var le=T.length;le--;)if(T[le]===q)continue e;o&&T.push(q),x.push(j)}else g(T,q,l)||(T!==x&&T.push(q),x.push(j))}return x}function tc(r,o){return o=zr(o,r),r=eg(r,o),r==null||delete r[Xs(Ns(o))]}function Sm(r,o,l,d){return Xo(r,o,l(bn(r,o)),d)}function Ia(r,o,l,d){for(var g=r.length,y=d?g:-1;(d?y--:++y<g)&&o(r[y],y,r););return l?Ts(r,d?0:y,d?y+1:g):Ts(r,d?y+1:0,d?g:y)}function Tm(r,o){var l=r;return l instanceof We&&(l=l.value()),Au(o,function(d,g){return g.func.apply(g.thisArg,Br([d],g.args))},l)}function sc(r,o,l){var d=r.length;if(d<2)return d?qr(r[0]):[];for(var g=-1,y=M(d);++g<d;)for(var E=r[g],x=-1;++x<d;)x!=g&&(y[g]=Qo(y[g]||E,r[x],o,l));return qr(jt(y,1),o,l)}function Nm(r,o,l){for(var d=-1,g=r.length,y=o.length,E={};++d<g;){var x=d<y?o[d]:s;l(E,r[d],x)}return E}function rc(r){return Et(r)?r:[]}function nc(r){return typeof r=="function"?r:ls}function zr(r,o){return Re(r)?r:hc(r,o)?[r]:ng(rt(r))}var _M=je;function Wr(r,o,l){var d=r.length;return l=l===s?d:l,!o&&l>=d?r:Ts(r,o,l)}var Am=nI||function(r){return $t.clearTimeout(r)};function Im(r,o){if(o)return r.slice();var l=r.length,d=Jp?Jp(l):new r.constructor(l);return r.copy(d),d}function oc(r){var o=new r.constructor(r.byteLength);return new _a(o).set(new _a(r)),o}function vM(r,o){var l=o?oc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.byteLength)}function yM(r){var o=new r.constructor(r.source,pp.exec(r));return o.lastIndex=r.lastIndex,o}function bM(r){return Ko?at(Ko.call(r)):{}}function Mm(r,o){var l=o?oc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.length)}function Pm(r,o){if(r!==o){var l=r!==s,d=r===null,g=r===r,y=vs(r),E=o!==s,x=o===null,T=o===o,B=vs(o);if(!x&&!B&&!y&&r>o||y&&E&&T&&!x&&!B||d&&E&&T||!l&&T||!g)return 1;if(!d&&!y&&!B&&r<o||B&&l&&g&&!d&&!y||x&&l&&g||!E&&g||!T)return-1}return 0}function wM(r,o,l){for(var d=-1,g=r.criteria,y=o.criteria,E=g.length,x=l.length;++d<E;){var T=Pm(g[d],y[d]);if(T){if(d>=x)return T;var B=l[d];return T*(B=="desc"?-1:1)}}return r.index-o.index}function km(r,o,l,d){for(var g=-1,y=r.length,E=l.length,x=-1,T=o.length,B=Mt(y-E,0),j=M(T+B),q=!d;++x<T;)j[x]=o[x];for(;++g<E;)(q||g<y)&&(j[l[g]]=r[g]);for(;B--;)j[x++]=r[g++];return j}function Vm(r,o,l,d){for(var g=-1,y=r.length,E=-1,x=l.length,T=-1,B=o.length,j=Mt(y-x,0),q=M(j+B),le=!d;++g<j;)q[g]=r[g];for(var ge=g;++T<B;)q[ge+T]=o[T];for(;++E<x;)(le||g<y)&&(q[ge+l[E]]=r[g++]);return q}function os(r,o){var l=-1,d=r.length;for(o||(o=M(d));++l<d;)o[l]=r[l];return o}function Js(r,o,l,d){var g=!l;l||(l={});for(var y=-1,E=o.length;++y<E;){var x=o[y],T=d?d(l[x],r[x],x,l,r):s;T===s&&(T=r[x]),g?yr(l,x,T):Yo(l,x,T)}return l}function EM(r,o){return Js(r,fc(r),o)}function CM(r,o){return Js(r,Km(r),o)}function Ma(r,o){return function(l,d){var g=Re(l)?TA:zI,y=o?o():{};return g(l,r,De(d,2),y)}}function Qn(r){return je(function(o,l){var d=-1,g=l.length,y=g>1?l[g-1]:s,E=g>2?l[2]:s;for(y=r.length>3&&typeof y=="function"?(g--,y):s,E&&rs(l[0],l[1],E)&&(y=g<3?s:y,g=1),o=at(o);++d<g;){var x=l[d];x&&r(o,x,d,y)}return o})}function Rm(r,o){return function(l,d){if(l==null)return l;if(!is(l))return r(l,d);for(var g=l.length,y=o?g:-1,E=at(l);(o?y--:++y<g)&&d(E[y],y,E)!==!1;);return l}}function Lm(r){return function(o,l,d){for(var g=-1,y=at(o),E=d(o),x=E.length;x--;){var T=E[r?x:++g];if(l(y[T],T,y)===!1)break}return o}}function DM(r,o,l){var d=o&te,g=ei(r);function y(){var E=this&&this!==$t&&this instanceof y?g:r;return E.apply(d?l:this,arguments)}return y}function Um(r){return function(o){o=rt(o);var l=Hn(o)?Fs(o):s,d=l?l[0]:o.charAt(0),g=l?Wr(l,1).join(""):o.slice(1);return d[r]()+g}}function Zn(r){return function(o){return Au(Rg(Vg(o).replace(pA,"")),r,"")}}function ei(r){return function(){var o=arguments;switch(o.length){case 0:return new r;case 1:return new r(o[0]);case 2:return new r(o[0],o[1]);case 3:return new r(o[0],o[1],o[2]);case 4:return new r(o[0],o[1],o[2],o[3]);case 5:return new r(o[0],o[1],o[2],o[3],o[4]);case 6:return new r(o[0],o[1],o[2],o[3],o[4],o[5]);case 7:return new r(o[0],o[1],o[2],o[3],o[4],o[5],o[6])}var l=Yn(r.prototype),d=r.apply(l,o);return vt(d)?d:l}}function xM(r,o,l){var d=ei(r);function g(){for(var y=arguments.length,E=M(y),x=y,T=Jn(g);x--;)E[x]=arguments[x];var B=y<3&&E[0]!==T&&E[y-1]!==T?[]:$r(E,T);if(y-=B.length,y<l)return Hm(r,o,Pa,g.placeholder,s,E,B,s,s,l-y);var j=this&&this!==$t&&this instanceof g?d:r;return ms(j,this,E)}return g}function Fm(r){return function(o,l,d){var g=at(o);if(!is(o)){var y=De(l,3);o=kt(o),l=function(x){return y(g[x],x,g)}}var E=r(o,l,d);return E>-1?g[y?o[E]:E]:s}}function Bm(r){return wr(function(o){var l=o.length,d=l,g=Os.prototype.thru;for(r&&o.reverse();d--;){var y=o[d];if(typeof y!="function")throw new xs(u);if(g&&!E&&La(y)=="wrapper")var E=new Os([],!0)}for(d=E?d:l;++d<l;){y=o[d];var x=La(y),T=x=="wrapper"?cc(y):s;T&&pc(T[0])&&T[1]==(ve|K|Z|Ae)&&!T[4].length&&T[9]==1?E=E[La(T[0])].apply(E,T[3]):E=y.length==1&&pc(y)?E[x]():E.thru(y)}return function(){var B=arguments,j=B[0];if(E&&B.length==1&&Re(j))return E.plant(j).value();for(var q=0,le=l?o[q].apply(this,B):j;++q<l;)le=o[q].call(this,le);return le}})}function Pa(r,o,l,d,g,y,E,x,T,B){var j=o&ve,q=o&te,le=o&I,ge=o&(K|ye),Se=o&ae,Be=le?s:ei(r);function Te(){for(var qe=arguments.length,Ke=M(qe),ys=qe;ys--;)Ke[ys]=arguments[ys];if(ge)var ns=Jn(Te),bs=LA(Ke,ns);if(d&&(Ke=km(Ke,d,g,ge)),y&&(Ke=Vm(Ke,y,E,ge)),qe-=bs,ge&&qe<B){var Ct=$r(Ke,ns);return Hm(r,o,Pa,Te.placeholder,l,Ke,Ct,x,T,B-qe)}var js=q?l:this,xr=le?js[r]:r;return qe=Ke.length,x?Ke=zM(Ke,x):Se&&qe>1&&Ke.reverse(),j&&T<qe&&(Ke.length=T),this&&this!==$t&&this instanceof Te&&(xr=Be||ei(xr)),xr.apply(js,Ke)}return Te}function $m(r,o){return function(l,d){return XI(l,r,o(d),{})}}function ka(r,o){return function(l,d){var g;if(l===s&&d===s)return o;if(l!==s&&(g=l),d!==s){if(g===s)return d;typeof l=="string"||typeof d=="string"?(l=_s(l),d=_s(d)):(l=Om(l),d=Om(d)),g=r(l,d)}return g}}function ic(r){return wr(function(o){return o=gt(o,gs(De())),je(function(l){var d=this;return r(o,function(g){return ms(g,d,l)})})})}function Va(r,o){o=o===s?" ":_s(o);var l=o.length;if(l<2)return l?Xu(o,r):o;var d=Xu(o,wa(r/qn(o)));return Hn(o)?Wr(Fs(d),0,r).join(""):d.slice(0,r)}function OM(r,o,l,d){var g=o&te,y=ei(r);function E(){for(var x=-1,T=arguments.length,B=-1,j=d.length,q=M(j+T),le=this&&this!==$t&&this instanceof E?y:r;++B<j;)q[B]=d[B];for(;T--;)q[B++]=arguments[++x];return ms(le,g?l:this,q)}return E}function jm(r){return function(o,l,d){return d&&typeof d!="number"&&rs(o,l,d)&&(l=d=s),o=Dr(o),l===s?(l=o,o=0):l=Dr(l),d=d===s?o<l?1:-1:Dr(d),dM(o,l,d,r)}}function Ra(r){return function(o,l){return typeof o=="string"&&typeof l=="string"||(o=As(o),l=As(l)),r(o,l)}}function Hm(r,o,l,d,g,y,E,x,T,B){var j=o&K,q=j?E:s,le=j?s:E,ge=j?y:s,Se=j?s:y;o|=j?Z:fe,o&=~(j?fe:Z),o&se||(o&=~(te|I));var Be=[r,o,g,ge,q,Se,le,x,T,B],Te=l.apply(s,Be);return pc(r)&&tg(Te,Be),Te.placeholder=d,sg(Te,r,o)}function ac(r){var o=It[r];return function(l,d){if(l=As(l),d=d==null?0:Wt(Fe(d),292),d&&sm(l)){var g=(rt(l)+"e").split("e"),y=o(g[0]+"e"+(+g[1]+d));return g=(rt(y)+"e").split("e"),+(g[0]+"e"+(+g[1]-d))}return o(l)}}var SM=Gn&&1/da(new Gn([,-0]))[1]==Oe?function(r){return new Gn(r)}:Nc;function qm(r){return function(o){var l=Gt(o);return l==b?Lu(o):l==Y?qA(o):RA(o,r(o))}}function br(r,o,l,d,g,y,E,x){var T=o&I;if(!T&&typeof r!="function")throw new xs(u);var B=d?d.length:0;if(B||(o&=~(Z|fe),d=g=s),E=E===s?E:Mt(Fe(E),0),x=x===s?x:Fe(x),B-=g?g.length:0,o&fe){var j=d,q=g;d=g=s}var le=T?s:cc(r),ge=[r,o,l,d,g,j,q,y,E,x];if(le&&jM(ge,le),r=ge[0],o=ge[1],l=ge[2],d=ge[3],g=ge[4],x=ge[9]=ge[9]===s?T?0:r.length:Mt(ge[9]-B,0),!x&&o&(K|ye)&&(o&=~(K|ye)),!o||o==te)var Se=DM(r,o,l);else o==K||o==ye?Se=xM(r,o,x):(o==Z||o==(te|Z))&&!g.length?Se=OM(r,o,l,d):Se=Pa.apply(s,ge);var Be=le?Dm:tg;return sg(Be(Se,ge),r,o)}function zm(r,o,l,d){return r===s||$s(r,Wn[l])&&!it.call(d,l)?o:r}function Wm(r,o,l,d,g,y){return vt(r)&&vt(o)&&(y.set(o,r),Na(r,o,s,Wm,y),y.delete(o)),r}function TM(r){return ri(r)?s:r}function Gm(r,o,l,d,g,y){var E=l&V,x=r.length,T=o.length;if(x!=T&&!(E&&T>x))return!1;var B=y.get(r),j=y.get(o);if(B&&j)return B==o&&j==r;var q=-1,le=!0,ge=l&F?new vn:s;for(y.set(r,o),y.set(o,r);++q<x;){var Se=r[q],Be=o[q];if(d)var Te=E?d(Be,Se,q,o,r,y):d(Se,Be,q,r,o,y);if(Te!==s){if(Te)continue;le=!1;break}if(ge){if(!Iu(o,function(qe,Ke){if(!Ho(ge,Ke)&&(Se===qe||g(Se,qe,l,d,y)))return ge.push(Ke)})){le=!1;break}}else if(!(Se===Be||g(Se,Be,l,d,y))){le=!1;break}}return y.delete(r),y.delete(o),le}function NM(r,o,l,d,g,y,E){switch(l){case Ue:if(r.byteLength!=o.byteLength||r.byteOffset!=o.byteOffset)return!1;r=r.buffer,o=o.buffer;case Ce:return!(r.byteLength!=o.byteLength||!y(new _a(r),new _a(o)));case oe:case ne:case C:return $s(+r,+o);case ke:return r.name==o.name&&r.message==o.message;case X:case J:return r==o+"";case b:var x=Lu;case Y:var T=d&V;if(x||(x=da),r.size!=o.size&&!T)return!1;var B=E.get(r);if(B)return B==o;d|=F,E.set(r,o);var j=Gm(x(r),x(o),d,g,y,E);return E.delete(r),j;case W:if(Ko)return Ko.call(r)==Ko.call(o)}return!1}function AM(r,o,l,d,g,y){var E=l&V,x=lc(r),T=x.length,B=lc(o),j=B.length;if(T!=j&&!E)return!1;for(var q=T;q--;){var le=x[q];if(!(E?le in o:it.call(o,le)))return!1}var ge=y.get(r),Se=y.get(o);if(ge&&Se)return ge==o&&Se==r;var Be=!0;y.set(r,o),y.set(o,r);for(var Te=E;++q<T;){le=x[q];var qe=r[le],Ke=o[le];if(d)var ys=E?d(Ke,qe,le,o,r,y):d(qe,Ke,le,r,o,y);if(!(ys===s?qe===Ke||g(qe,Ke,l,d,y):ys)){Be=!1;break}Te||(Te=le=="constructor")}if(Be&&!Te){var ns=r.constructor,bs=o.constructor;ns!=bs&&"constructor"in r&&"constructor"in o&&!(typeof ns=="function"&&ns instanceof ns&&typeof bs=="function"&&bs instanceof bs)&&(Be=!1)}return y.delete(r),y.delete(o),Be}function wr(r){return gc(Xm(r,s,lg),r+"")}function lc(r){return hm(r,kt,fc)}function uc(r){return hm(r,as,Km)}var cc=Ca?function(r){return Ca.get(r)}:Nc;function La(r){for(var o=r.name+"",l=Kn[o],d=it.call(Kn,o)?l.length:0;d--;){var g=l[d],y=g.func;if(y==null||y==r)return g.name}return o}function Jn(r){var o=it.call(v,"placeholder")?v:r;return o.placeholder}function De(){var r=v.iteratee||Sc;return r=r===Sc?gm:r,arguments.length?r(arguments[0],arguments[1]):r}function Ua(r,o){var l=r.__data__;return UM(o)?l[typeof o=="string"?"string":"hash"]:l.map}function dc(r){for(var o=kt(r),l=o.length;l--;){var d=o[l],g=r[d];o[l]=[d,g,Zm(g)]}return o}function wn(r,o){var l=$A(r,o);return mm(l)?l:s}function IM(r){var o=it.call(r,gn),l=r[gn];try{r[gn]=s;var d=!0}catch{}var g=ma.call(r);return d&&(o?r[gn]=l:delete r[gn]),g}var fc=Fu?function(r){return r==null?[]:(r=at(r),Fr(Fu(r),function(o){return em.call(r,o)}))}:Ac,Km=Fu?function(r){for(var o=[];r;)Br(o,fc(r)),r=va(r);return o}:Ac,Gt=ss;(Bu&&Gt(new Bu(new ArrayBuffer(1)))!=Ue||zo&&Gt(new zo)!=b||$u&&Gt($u.resolve())!=H||Gn&&Gt(new Gn)!=Y||Wo&&Gt(new Wo)!=re)&&(Gt=function(r){var o=ss(r),l=o==U?r.constructor:s,d=l?En(l):"";if(d)switch(d){case hI:return Ue;case pI:return b;case mI:return H;case gI:return Y;case _I:return re}return o});function MM(r,o,l){for(var d=-1,g=l.length;++d<g;){var y=l[d],E=y.size;switch(y.type){case"drop":r+=E;break;case"dropRight":o-=E;break;case"take":o=Wt(o,r+E);break;case"takeRight":r=Mt(r,o-E);break}}return{start:r,end:o}}function PM(r){var o=r.match(FN);return o?o[1].split(BN):[]}function Ym(r,o,l){o=zr(o,r);for(var d=-1,g=o.length,y=!1;++d<g;){var E=Xs(o[d]);if(!(y=r!=null&&l(r,E)))break;r=r[E]}return y||++d!=g?y:(g=r==null?0:r.length,!!g&&za(g)&&Er(E,g)&&(Re(r)||Cn(r)))}function kM(r){var o=r.length,l=new r.constructor(o);return o&&typeof r[0]=="string"&&it.call(r,"index")&&(l.index=r.index,l.input=r.input),l}function Qm(r){return typeof r.constructor=="function"&&!ti(r)?Yn(va(r)):{}}function VM(r,o,l){var d=r.constructor;switch(o){case Ce:return oc(r);case oe:case ne:return new d(+r);case Ue:return vM(r,l);case Xe:case Qe:case Ft:case Ot:case ts:case Bt:case gr:case Bn:case At:return Mm(r,l);case b:return new d;case C:case J:return new d(r);case X:return yM(r);case Y:return new d;case W:return bM(r)}}function RM(r,o){var l=o.length;if(!l)return r;var d=l-1;return o[d]=(l>1?"& ":"")+o[d],o=o.join(l>2?", ":" "),r.replace(UN,`{
/* [wrapped with `+o+`] */
`)}function LM(r){return Re(r)||Cn(r)||!!(tm&&r&&r[tm])}function Er(r,o){var l=typeof r;return o=o??we,!!o&&(l=="number"||l!="symbol"&&YN.test(r))&&r>-1&&r%1==0&&r<o}function rs(r,o,l){if(!vt(l))return!1;var d=typeof o;return(d=="number"?is(l)&&Er(o,l.length):d=="string"&&o in l)?$s(l[o],r):!1}function hc(r,o){if(Re(r))return!1;var l=typeof r;return l=="number"||l=="symbol"||l=="boolean"||r==null||vs(r)?!0:kN.test(r)||!PN.test(r)||o!=null&&r in at(o)}function UM(r){var o=typeof r;return o=="string"||o=="number"||o=="symbol"||o=="boolean"?r!=="__proto__":r===null}function pc(r){var o=La(r),l=v[o];if(typeof l!="function"||!(o in We.prototype))return!1;if(r===l)return!0;var d=cc(l);return!!d&&r===d[0]}function FM(r){return!!Zp&&Zp in r}var BM=ha?Cr:Ic;function ti(r){var o=r&&r.constructor,l=typeof o=="function"&&o.prototype||Wn;return r===l}function Zm(r){return r===r&&!vt(r)}function Jm(r,o){return function(l){return l==null?!1:l[r]===o&&(o!==s||r in at(l))}}function $M(r){var o=Ha(r,function(d){return l.size===m&&l.clear(),d}),l=o.cache;return o}function jM(r,o){var l=r[1],d=o[1],g=l|d,y=g<(te|I|ve),E=d==ve&&l==K||d==ve&&l==Ae&&r[7].length<=o[8]||d==(ve|Ae)&&o[7].length<=o[8]&&l==K;if(!(y||E))return r;d&te&&(r[2]=o[2],g|=l&te?0:se);var x=o[3];if(x){var T=r[3];r[3]=T?km(T,x,o[4]):x,r[4]=T?$r(r[3],p):o[4]}return x=o[5],x&&(T=r[5],r[5]=T?Vm(T,x,o[6]):x,r[6]=T?$r(r[5],p):o[6]),x=o[7],x&&(r[7]=x),d&ve&&(r[8]=r[8]==null?o[8]:Wt(r[8],o[8])),r[9]==null&&(r[9]=o[9]),r[0]=o[0],r[1]=g,r}function HM(r){var o=[];if(r!=null)for(var l in at(r))o.push(l);return o}function qM(r){return ma.call(r)}function Xm(r,o,l){return o=Mt(o===s?r.length-1:o,0),function(){for(var d=arguments,g=-1,y=Mt(d.length-o,0),E=M(y);++g<y;)E[g]=d[o+g];g=-1;for(var x=M(o+1);++g<o;)x[g]=d[g];return x[o]=l(E),ms(r,this,x)}}function eg(r,o){return o.length<2?r:bn(r,Ts(o,0,-1))}function zM(r,o){for(var l=r.length,d=Wt(o.length,l),g=os(r);d--;){var y=o[d];r[d]=Er(y,l)?g[y]:s}return r}function mc(r,o){if(!(o==="constructor"&&typeof r[o]=="function")&&o!="__proto__")return r[o]}var tg=rg(Dm),si=iI||function(r,o){return $t.setTimeout(r,o)},gc=rg(pM);function sg(r,o,l){var d=o+"";return gc(r,RM(d,WM(PM(d),l)))}function rg(r){var o=0,l=0;return function(){var d=cI(),g=Ge-(d-l);if(l=d,g>0){if(++o>=ue)return arguments[0]}else o=0;return r.apply(s,arguments)}}function Fa(r,o){var l=-1,d=r.length,g=d-1;for(o=o===s?d:o;++l<o;){var y=Ju(l,g),E=r[y];r[y]=r[l],r[l]=E}return r.length=o,r}var ng=$M(function(r){var o=[];return r.charCodeAt(0)===46&&o.push(""),r.replace(VN,function(l,d,g,y){o.push(g?y.replace(HN,"$1"):d||l)}),o});function Xs(r){if(typeof r=="string"||vs(r))return r;var o=r+"";return o=="0"&&1/r==-Oe?"-0":o}function En(r){if(r!=null){try{return pa.call(r)}catch{}try{return r+""}catch{}}return""}function WM(r,o){return Ds(hs,function(l){var d="_."+l[0];o&l[1]&&!ua(r,d)&&r.push(d)}),r.sort()}function og(r){if(r instanceof We)return r.clone();var o=new Os(r.__wrapped__,r.__chain__);return o.__actions__=os(r.__actions__),o.__index__=r.__index__,o.__values__=r.__values__,o}function GM(r,o,l){(l?rs(r,o,l):o===s)?o=1:o=Mt(Fe(o),0);var d=r==null?0:r.length;if(!d||o<1)return[];for(var g=0,y=0,E=M(wa(d/o));g<d;)E[y++]=Ts(r,g,g+=o);return E}function KM(r){for(var o=-1,l=r==null?0:r.length,d=0,g=[];++o<l;){var y=r[o];y&&(g[d++]=y)}return g}function YM(){var r=arguments.length;if(!r)return[];for(var o=M(r-1),l=arguments[0],d=r;d--;)o[d-1]=arguments[d];return Br(Re(l)?os(l):[l],jt(o,1))}var QM=je(function(r,o){return Et(r)?Qo(r,jt(o,1,Et,!0)):[]}),ZM=je(function(r,o){var l=Ns(o);return Et(l)&&(l=s),Et(r)?Qo(r,jt(o,1,Et,!0),De(l,2)):[]}),JM=je(function(r,o){var l=Ns(o);return Et(l)&&(l=s),Et(r)?Qo(r,jt(o,1,Et,!0),s,l):[]});function XM(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Fe(o),Ts(r,o<0?0:o,d)):[]}function e2(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Fe(o),o=d-o,Ts(r,0,o<0?0:o)):[]}function t2(r,o){return r&&r.length?Ia(r,De(o,3),!0,!0):[]}function s2(r,o){return r&&r.length?Ia(r,De(o,3),!0):[]}function r2(r,o,l,d){var g=r==null?0:r.length;return g?(l&&typeof l!="number"&&rs(r,o,l)&&(l=0,d=g),YI(r,o,l,d)):[]}function ig(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=l==null?0:Fe(l);return g<0&&(g=Mt(d+g,0)),ca(r,De(o,3),g)}function ag(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=d-1;return l!==s&&(g=Fe(l),g=l<0?Mt(d+g,0):Wt(g,d-1)),ca(r,De(o,3),g,!0)}function lg(r){var o=r==null?0:r.length;return o?jt(r,1):[]}function n2(r){var o=r==null?0:r.length;return o?jt(r,Oe):[]}function o2(r,o){var l=r==null?0:r.length;return l?(o=o===s?1:Fe(o),jt(r,o)):[]}function i2(r){for(var o=-1,l=r==null?0:r.length,d={};++o<l;){var g=r[o];d[g[0]]=g[1]}return d}function ug(r){return r&&r.length?r[0]:s}function a2(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=l==null?0:Fe(l);return g<0&&(g=Mt(d+g,0)),jn(r,o,g)}function l2(r){var o=r==null?0:r.length;return o?Ts(r,0,-1):[]}var u2=je(function(r){var o=gt(r,rc);return o.length&&o[0]===r[0]?Gu(o):[]}),c2=je(function(r){var o=Ns(r),l=gt(r,rc);return o===Ns(l)?o=s:l.pop(),l.length&&l[0]===r[0]?Gu(l,De(o,2)):[]}),d2=je(function(r){var o=Ns(r),l=gt(r,rc);return o=typeof o=="function"?o:s,o&&l.pop(),l.length&&l[0]===r[0]?Gu(l,s,o):[]});function f2(r,o){return r==null?"":lI.call(r,o)}function Ns(r){var o=r==null?0:r.length;return o?r[o-1]:s}function h2(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=d;return l!==s&&(g=Fe(l),g=g<0?Mt(d+g,0):Wt(g,d-1)),o===o?WA(r,o,g):ca(r,Hp,g,!0)}function p2(r,o){return r&&r.length?bm(r,Fe(o)):s}var m2=je(cg);function cg(r,o){return r&&r.length&&o&&o.length?Zu(r,o):r}function g2(r,o,l){return r&&r.length&&o&&o.length?Zu(r,o,De(l,2)):r}function _2(r,o,l){return r&&r.length&&o&&o.length?Zu(r,o,s,l):r}var v2=wr(function(r,o){var l=r==null?0:r.length,d=Hu(r,o);return Cm(r,gt(o,function(g){return Er(g,l)?+g:g}).sort(Pm)),d});function y2(r,o){var l=[];if(!(r&&r.length))return l;var d=-1,g=[],y=r.length;for(o=De(o,3);++d<y;){var E=r[d];o(E,d,r)&&(l.push(E),g.push(d))}return Cm(r,g),l}function _c(r){return r==null?r:fI.call(r)}function b2(r,o,l){var d=r==null?0:r.length;return d?(l&&typeof l!="number"&&rs(r,o,l)?(o=0,l=d):(o=o==null?0:Fe(o),l=l===s?d:Fe(l)),Ts(r,o,l)):[]}function w2(r,o){return Aa(r,o)}function E2(r,o,l){return ec(r,o,De(l,2))}function C2(r,o){var l=r==null?0:r.length;if(l){var d=Aa(r,o);if(d<l&&$s(r[d],o))return d}return-1}function D2(r,o){return Aa(r,o,!0)}function x2(r,o,l){return ec(r,o,De(l,2),!0)}function O2(r,o){var l=r==null?0:r.length;if(l){var d=Aa(r,o,!0)-1;if($s(r[d],o))return d}return-1}function S2(r){return r&&r.length?xm(r):[]}function T2(r,o){return r&&r.length?xm(r,De(o,2)):[]}function N2(r){var o=r==null?0:r.length;return o?Ts(r,1,o):[]}function A2(r,o,l){return r&&r.length?(o=l||o===s?1:Fe(o),Ts(r,0,o<0?0:o)):[]}function I2(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Fe(o),o=d-o,Ts(r,o<0?0:o,d)):[]}function M2(r,o){return r&&r.length?Ia(r,De(o,3),!1,!0):[]}function P2(r,o){return r&&r.length?Ia(r,De(o,3)):[]}var k2=je(function(r){return qr(jt(r,1,Et,!0))}),V2=je(function(r){var o=Ns(r);return Et(o)&&(o=s),qr(jt(r,1,Et,!0),De(o,2))}),R2=je(function(r){var o=Ns(r);return o=typeof o=="function"?o:s,qr(jt(r,1,Et,!0),s,o)});function L2(r){return r&&r.length?qr(r):[]}function U2(r,o){return r&&r.length?qr(r,De(o,2)):[]}function F2(r,o){return o=typeof o=="function"?o:s,r&&r.length?qr(r,s,o):[]}function vc(r){if(!(r&&r.length))return[];var o=0;return r=Fr(r,function(l){if(Et(l))return o=Mt(l.length,o),!0}),Vu(o,function(l){return gt(r,Mu(l))})}function dg(r,o){if(!(r&&r.length))return[];var l=vc(r);return o==null?l:gt(l,function(d){return ms(o,s,d)})}var B2=je(function(r,o){return Et(r)?Qo(r,o):[]}),$2=je(function(r){return sc(Fr(r,Et))}),j2=je(function(r){var o=Ns(r);return Et(o)&&(o=s),sc(Fr(r,Et),De(o,2))}),H2=je(function(r){var o=Ns(r);return o=typeof o=="function"?o:s,sc(Fr(r,Et),s,o)}),q2=je(vc);function z2(r,o){return Nm(r||[],o||[],Yo)}function W2(r,o){return Nm(r||[],o||[],Xo)}var G2=je(function(r){var o=r.length,l=o>1?r[o-1]:s;return l=typeof l=="function"?(r.pop(),l):s,dg(r,l)});function fg(r){var o=v(r);return o.__chain__=!0,o}function K2(r,o){return o(r),r}function Ba(r,o){return o(r)}var Y2=wr(function(r){var o=r.length,l=o?r[0]:0,d=this.__wrapped__,g=function(y){return Hu(y,r)};return o>1||this.__actions__.length||!(d instanceof We)||!Er(l)?this.thru(g):(d=d.slice(l,+l+(o?1:0)),d.__actions__.push({func:Ba,args:[g],thisArg:s}),new Os(d,this.__chain__).thru(function(y){return o&&!y.length&&y.push(s),y}))});function Q2(){return fg(this)}function Z2(){return new Os(this.value(),this.__chain__)}function J2(){this.__values__===s&&(this.__values__=Og(this.value()));var r=this.__index__>=this.__values__.length,o=r?s:this.__values__[this.__index__++];return{done:r,value:o}}function X2(){return this}function eP(r){for(var o,l=this;l instanceof xa;){var d=og(l);d.__index__=0,d.__values__=s,o?g.__wrapped__=d:o=d;var g=d;l=l.__wrapped__}return g.__wrapped__=r,o}function tP(){var r=this.__wrapped__;if(r instanceof We){var o=r;return this.__actions__.length&&(o=new We(this)),o=o.reverse(),o.__actions__.push({func:Ba,args:[_c],thisArg:s}),new Os(o,this.__chain__)}return this.thru(_c)}function sP(){return Tm(this.__wrapped__,this.__actions__)}var rP=Ma(function(r,o,l){it.call(r,l)?++r[l]:yr(r,l,1)});function nP(r,o,l){var d=Re(r)?$p:KI;return l&&rs(r,o,l)&&(o=s),d(r,De(o,3))}function oP(r,o){var l=Re(r)?Fr:dm;return l(r,De(o,3))}var iP=Fm(ig),aP=Fm(ag);function lP(r,o){return jt($a(r,o),1)}function uP(r,o){return jt($a(r,o),Oe)}function cP(r,o,l){return l=l===s?1:Fe(l),jt($a(r,o),l)}function hg(r,o){var l=Re(r)?Ds:Hr;return l(r,De(o,3))}function pg(r,o){var l=Re(r)?NA:cm;return l(r,De(o,3))}var dP=Ma(function(r,o,l){it.call(r,l)?r[l].push(o):yr(r,l,[o])});function fP(r,o,l,d){r=is(r)?r:eo(r),l=l&&!d?Fe(l):0;var g=r.length;return l<0&&(l=Mt(g+l,0)),Wa(r)?l<=g&&r.indexOf(o,l)>-1:!!g&&jn(r,o,l)>-1}var hP=je(function(r,o,l){var d=-1,g=typeof o=="function",y=is(r)?M(r.length):[];return Hr(r,function(E){y[++d]=g?ms(o,E,l):Zo(E,o,l)}),y}),pP=Ma(function(r,o,l){yr(r,l,o)});function $a(r,o){var l=Re(r)?gt:_m;return l(r,De(o,3))}function mP(r,o,l,d){return r==null?[]:(Re(o)||(o=o==null?[]:[o]),l=d?s:l,Re(l)||(l=l==null?[]:[l]),wm(r,o,l))}var gP=Ma(function(r,o,l){r[l?0:1].push(o)},function(){return[[],[]]});function _P(r,o,l){var d=Re(r)?Au:zp,g=arguments.length<3;return d(r,De(o,4),l,g,Hr)}function vP(r,o,l){var d=Re(r)?AA:zp,g=arguments.length<3;return d(r,De(o,4),l,g,cm)}function yP(r,o){var l=Re(r)?Fr:dm;return l(r,qa(De(o,3)))}function bP(r){var o=Re(r)?im:fM;return o(r)}function wP(r,o,l){(l?rs(r,o,l):o===s)?o=1:o=Fe(o);var d=Re(r)?HI:hM;return d(r,o)}function EP(r){var o=Re(r)?qI:mM;return o(r)}function CP(r){if(r==null)return 0;if(is(r))return Wa(r)?qn(r):r.length;var o=Gt(r);return o==b||o==Y?r.size:Yu(r).length}function DP(r,o,l){var d=Re(r)?Iu:gM;return l&&rs(r,o,l)&&(o=s),d(r,De(o,3))}var xP=je(function(r,o){if(r==null)return[];var l=o.length;return l>1&&rs(r,o[0],o[1])?o=[]:l>2&&rs(o[0],o[1],o[2])&&(o=[o[0]]),wm(r,jt(o,1),[])}),ja=oI||function(){return $t.Date.now()};function OP(r,o){if(typeof o!="function")throw new xs(u);return r=Fe(r),function(){if(--r<1)return o.apply(this,arguments)}}function mg(r,o,l){return o=l?s:o,o=r&&o==null?r.length:o,br(r,ve,s,s,s,s,o)}function gg(r,o){var l;if(typeof o!="function")throw new xs(u);return r=Fe(r),function(){return--r>0&&(l=o.apply(this,arguments)),r<=1&&(o=s),l}}var yc=je(function(r,o,l){var d=te;if(l.length){var g=$r(l,Jn(yc));d|=Z}return br(r,d,o,l,g)}),_g=je(function(r,o,l){var d=te|I;if(l.length){var g=$r(l,Jn(_g));d|=Z}return br(o,d,r,l,g)});function vg(r,o,l){o=l?s:o;var d=br(r,K,s,s,s,s,s,o);return d.placeholder=vg.placeholder,d}function yg(r,o,l){o=l?s:o;var d=br(r,ye,s,s,s,s,s,o);return d.placeholder=yg.placeholder,d}function bg(r,o,l){var d,g,y,E,x,T,B=0,j=!1,q=!1,le=!0;if(typeof r!="function")throw new xs(u);o=As(o)||0,vt(l)&&(j=!!l.leading,q="maxWait"in l,y=q?Mt(As(l.maxWait)||0,o):y,le="trailing"in l?!!l.trailing:le);function ge(Ct){var js=d,xr=g;return d=g=s,B=Ct,E=r.apply(xr,js),E}function Se(Ct){return B=Ct,x=si(qe,o),j?ge(Ct):E}function Be(Ct){var js=Ct-T,xr=Ct-B,Fg=o-js;return q?Wt(Fg,y-xr):Fg}function Te(Ct){var js=Ct-T,xr=Ct-B;return T===s||js>=o||js<0||q&&xr>=y}function qe(){var Ct=ja();if(Te(Ct))return Ke(Ct);x=si(qe,Be(Ct))}function Ke(Ct){return x=s,le&&d?ge(Ct):(d=g=s,E)}function ys(){x!==s&&Am(x),B=0,d=T=g=x=s}function ns(){return x===s?E:Ke(ja())}function bs(){var Ct=ja(),js=Te(Ct);if(d=arguments,g=this,T=Ct,js){if(x===s)return Se(T);if(q)return Am(x),x=si(qe,o),ge(T)}return x===s&&(x=si(qe,o)),E}return bs.cancel=ys,bs.flush=ns,bs}var SP=je(function(r,o){return um(r,1,o)}),TP=je(function(r,o,l){return um(r,As(o)||0,l)});function NP(r){return br(r,ae)}function Ha(r,o){if(typeof r!="function"||o!=null&&typeof o!="function")throw new xs(u);var l=function(){var d=arguments,g=o?o.apply(this,d):d[0],y=l.cache;if(y.has(g))return y.get(g);var E=r.apply(this,d);return l.cache=y.set(g,E)||y,E};return l.cache=new(Ha.Cache||vr),l}Ha.Cache=vr;function qa(r){if(typeof r!="function")throw new xs(u);return function(){var o=arguments;switch(o.length){case 0:return!r.call(this);case 1:return!r.call(this,o[0]);case 2:return!r.call(this,o[0],o[1]);case 3:return!r.call(this,o[0],o[1],o[2])}return!r.apply(this,o)}}function AP(r){return gg(2,r)}var IP=_M(function(r,o){o=o.length==1&&Re(o[0])?gt(o[0],gs(De())):gt(jt(o,1),gs(De()));var l=o.length;return je(function(d){for(var g=-1,y=Wt(d.length,l);++g<y;)d[g]=o[g].call(this,d[g]);return ms(r,this,d)})}),bc=je(function(r,o){var l=$r(o,Jn(bc));return br(r,Z,s,o,l)}),wg=je(function(r,o){var l=$r(o,Jn(wg));return br(r,fe,s,o,l)}),MP=wr(function(r,o){return br(r,Ae,s,s,s,o)});function PP(r,o){if(typeof r!="function")throw new xs(u);return o=o===s?o:Fe(o),je(r,o)}function kP(r,o){if(typeof r!="function")throw new xs(u);return o=o==null?0:Mt(Fe(o),0),je(function(l){var d=l[o],g=Wr(l,0,o);return d&&Br(g,d),ms(r,this,g)})}function VP(r,o,l){var d=!0,g=!0;if(typeof r!="function")throw new xs(u);return vt(l)&&(d="leading"in l?!!l.leading:d,g="trailing"in l?!!l.trailing:g),bg(r,o,{leading:d,maxWait:o,trailing:g})}function RP(r){return mg(r,1)}function LP(r,o){return bc(nc(o),r)}function UP(){if(!arguments.length)return[];var r=arguments[0];return Re(r)?r:[r]}function FP(r){return Ss(r,D)}function BP(r,o){return o=typeof o=="function"?o:s,Ss(r,D,o)}function $P(r){return Ss(r,_|D)}function jP(r,o){return o=typeof o=="function"?o:s,Ss(r,_|D,o)}function HP(r,o){return o==null||lm(r,o,kt(o))}function $s(r,o){return r===o||r!==r&&o!==o}var qP=Ra(Wu),zP=Ra(function(r,o){return r>=o}),Cn=pm(function(){return arguments}())?pm:function(r){return bt(r)&&it.call(r,"callee")&&!em.call(r,"callee")},Re=M.isArray,WP=Vp?gs(Vp):eM;function is(r){return r!=null&&za(r.length)&&!Cr(r)}function Et(r){return bt(r)&&is(r)}function GP(r){return r===!0||r===!1||bt(r)&&ss(r)==oe}var Gr=aI||Ic,KP=Rp?gs(Rp):tM;function YP(r){return bt(r)&&r.nodeType===1&&!ri(r)}function QP(r){if(r==null)return!0;if(is(r)&&(Re(r)||typeof r=="string"||typeof r.splice=="function"||Gr(r)||Xn(r)||Cn(r)))return!r.length;var o=Gt(r);if(o==b||o==Y)return!r.size;if(ti(r))return!Yu(r).length;for(var l in r)if(it.call(r,l))return!1;return!0}function ZP(r,o){return Jo(r,o)}function JP(r,o,l){l=typeof l=="function"?l:s;var d=l?l(r,o):s;return d===s?Jo(r,o,s,l):!!d}function wc(r){if(!bt(r))return!1;var o=ss(r);return o==ke||o==pe||typeof r.message=="string"&&typeof r.name=="string"&&!ri(r)}function XP(r){return typeof r=="number"&&sm(r)}function Cr(r){if(!vt(r))return!1;var o=ss(r);return o==ot||o==Ve||o==R||o==z}function Eg(r){return typeof r=="number"&&r==Fe(r)}function za(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=we}function vt(r){var o=typeof r;return r!=null&&(o=="object"||o=="function")}function bt(r){return r!=null&&typeof r=="object"}var Cg=Lp?gs(Lp):rM;function ek(r,o){return r===o||Ku(r,o,dc(o))}function tk(r,o,l){return l=typeof l=="function"?l:s,Ku(r,o,dc(o),l)}function sk(r){return Dg(r)&&r!=+r}function rk(r){if(BM(r))throw new Pe(a);return mm(r)}function nk(r){return r===null}function ok(r){return r==null}function Dg(r){return typeof r=="number"||bt(r)&&ss(r)==C}function ri(r){if(!bt(r)||ss(r)!=U)return!1;var o=va(r);if(o===null)return!0;var l=it.call(o,"constructor")&&o.constructor;return typeof l=="function"&&l instanceof l&&pa.call(l)==tI}var Ec=Up?gs(Up):nM;function ik(r){return Eg(r)&&r>=-we&&r<=we}var xg=Fp?gs(Fp):oM;function Wa(r){return typeof r=="string"||!Re(r)&&bt(r)&&ss(r)==J}function vs(r){return typeof r=="symbol"||bt(r)&&ss(r)==W}var Xn=Bp?gs(Bp):iM;function ak(r){return r===s}function lk(r){return bt(r)&&Gt(r)==re}function uk(r){return bt(r)&&ss(r)==_e}var ck=Ra(Qu),dk=Ra(function(r,o){return r<=o});function Og(r){if(!r)return[];if(is(r))return Wa(r)?Fs(r):os(r);if(qo&&r[qo])return HA(r[qo]());var o=Gt(r),l=o==b?Lu:o==Y?da:eo;return l(r)}function Dr(r){if(!r)return r===0?r:0;if(r=As(r),r===Oe||r===-Oe){var o=r<0?-1:1;return o*Ut}return r===r?r:0}function Fe(r){var o=Dr(r),l=o%1;return o===o?l?o-l:o:0}function Sg(r){return r?yn(Fe(r),0,yt):0}function As(r){if(typeof r=="number")return r;if(vs(r))return es;if(vt(r)){var o=typeof r.valueOf=="function"?r.valueOf():r;r=vt(o)?o+"":o}if(typeof r!="string")return r===0?r:+r;r=Wp(r);var l=WN.test(r);return l||KN.test(r)?OA(r.slice(2),l?2:8):zN.test(r)?es:+r}function Tg(r){return Js(r,as(r))}function fk(r){return r?yn(Fe(r),-we,we):r===0?r:0}function rt(r){return r==null?"":_s(r)}var hk=Qn(function(r,o){if(ti(o)||is(o)){Js(o,kt(o),r);return}for(var l in o)it.call(o,l)&&Yo(r,l,o[l])}),Ng=Qn(function(r,o){Js(o,as(o),r)}),Ga=Qn(function(r,o,l,d){Js(o,as(o),r,d)}),pk=Qn(function(r,o,l,d){Js(o,kt(o),r,d)}),mk=wr(Hu);function gk(r,o){var l=Yn(r);return o==null?l:am(l,o)}var _k=je(function(r,o){r=at(r);var l=-1,d=o.length,g=d>2?o[2]:s;for(g&&rs(o[0],o[1],g)&&(d=1);++l<d;)for(var y=o[l],E=as(y),x=-1,T=E.length;++x<T;){var B=E[x],j=r[B];(j===s||$s(j,Wn[B])&&!it.call(r,B))&&(r[B]=y[B])}return r}),vk=je(function(r){return r.push(s,Wm),ms(Ag,s,r)});function yk(r,o){return jp(r,De(o,3),Zs)}function bk(r,o){return jp(r,De(o,3),zu)}function wk(r,o){return r==null?r:qu(r,De(o,3),as)}function Ek(r,o){return r==null?r:fm(r,De(o,3),as)}function Ck(r,o){return r&&Zs(r,De(o,3))}function Dk(r,o){return r&&zu(r,De(o,3))}function xk(r){return r==null?[]:Ta(r,kt(r))}function Ok(r){return r==null?[]:Ta(r,as(r))}function Cc(r,o,l){var d=r==null?s:bn(r,o);return d===s?l:d}function Sk(r,o){return r!=null&&Ym(r,o,QI)}function Dc(r,o){return r!=null&&Ym(r,o,ZI)}var Tk=$m(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=ma.call(o)),r[o]=l},Oc(ls)),Nk=$m(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=ma.call(o)),it.call(r,o)?r[o].push(l):r[o]=[l]},De),Ak=je(Zo);function kt(r){return is(r)?om(r):Yu(r)}function as(r){return is(r)?om(r,!0):aM(r)}function Ik(r,o){var l={};return o=De(o,3),Zs(r,function(d,g,y){yr(l,o(d,g,y),d)}),l}function Mk(r,o){var l={};return o=De(o,3),Zs(r,function(d,g,y){yr(l,g,o(d,g,y))}),l}var Pk=Qn(function(r,o,l){Na(r,o,l)}),Ag=Qn(function(r,o,l,d){Na(r,o,l,d)}),kk=wr(function(r,o){var l={};if(r==null)return l;var d=!1;o=gt(o,function(y){return y=zr(y,r),d||(d=y.length>1),y}),Js(r,uc(r),l),d&&(l=Ss(l,_|w|D,TM));for(var g=o.length;g--;)tc(l,o[g]);return l});function Vk(r,o){return Ig(r,qa(De(o)))}var Rk=wr(function(r,o){return r==null?{}:uM(r,o)});function Ig(r,o){if(r==null)return{};var l=gt(uc(r),function(d){return[d]});return o=De(o),Em(r,l,function(d,g){return o(d,g[0])})}function Lk(r,o,l){o=zr(o,r);var d=-1,g=o.length;for(g||(g=1,r=s);++d<g;){var y=r==null?s:r[Xs(o[d])];y===s&&(d=g,y=l),r=Cr(y)?y.call(r):y}return r}function Uk(r,o,l){return r==null?r:Xo(r,o,l)}function Fk(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:Xo(r,o,l,d)}var Mg=qm(kt),Pg=qm(as);function Bk(r,o,l){var d=Re(r),g=d||Gr(r)||Xn(r);if(o=De(o,4),l==null){var y=r&&r.constructor;g?l=d?new y:[]:vt(r)?l=Cr(y)?Yn(va(r)):{}:l={}}return(g?Ds:Zs)(r,function(E,x,T){return o(l,E,x,T)}),l}function $k(r,o){return r==null?!0:tc(r,o)}function jk(r,o,l){return r==null?r:Sm(r,o,nc(l))}function Hk(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:Sm(r,o,nc(l),d)}function eo(r){return r==null?[]:Ru(r,kt(r))}function qk(r){return r==null?[]:Ru(r,as(r))}function zk(r,o,l){return l===s&&(l=o,o=s),l!==s&&(l=As(l),l=l===l?l:0),o!==s&&(o=As(o),o=o===o?o:0),yn(As(r),o,l)}function Wk(r,o,l){return o=Dr(o),l===s?(l=o,o=0):l=Dr(l),r=As(r),JI(r,o,l)}function Gk(r,o,l){if(l&&typeof l!="boolean"&&rs(r,o,l)&&(o=l=s),l===s&&(typeof o=="boolean"?(l=o,o=s):typeof r=="boolean"&&(l=r,r=s)),r===s&&o===s?(r=0,o=1):(r=Dr(r),o===s?(o=r,r=0):o=Dr(o)),r>o){var d=r;r=o,o=d}if(l||r%1||o%1){var g=rm();return Wt(r+g*(o-r+xA("1e-"+((g+"").length-1))),o)}return Ju(r,o)}var Kk=Zn(function(r,o,l){return o=o.toLowerCase(),r+(l?kg(o):o)});function kg(r){return xc(rt(r).toLowerCase())}function Vg(r){return r=rt(r),r&&r.replace(QN,UA).replace(mA,"")}function Yk(r,o,l){r=rt(r),o=_s(o);var d=r.length;l=l===s?d:yn(Fe(l),0,d);var g=l;return l-=o.length,l>=0&&r.slice(l,g)==o}function Qk(r){return r=rt(r),r&&AN.test(r)?r.replace(fp,FA):r}function Zk(r){return r=rt(r),r&&RN.test(r)?r.replace(bu,"\\$&"):r}var Jk=Zn(function(r,o,l){return r+(l?"-":"")+o.toLowerCase()}),Xk=Zn(function(r,o,l){return r+(l?" ":"")+o.toLowerCase()}),eV=Um("toLowerCase");function tV(r,o,l){r=rt(r),o=Fe(o);var d=o?qn(r):0;if(!o||d>=o)return r;var g=(o-d)/2;return Va(Ea(g),l)+r+Va(wa(g),l)}function sV(r,o,l){r=rt(r),o=Fe(o);var d=o?qn(r):0;return o&&d<o?r+Va(o-d,l):r}function rV(r,o,l){r=rt(r),o=Fe(o);var d=o?qn(r):0;return o&&d<o?Va(o-d,l)+r:r}function nV(r,o,l){return l||o==null?o=0:o&&(o=+o),dI(rt(r).replace(wu,""),o||0)}function oV(r,o,l){return(l?rs(r,o,l):o===s)?o=1:o=Fe(o),Xu(rt(r),o)}function iV(){var r=arguments,o=rt(r[0]);return r.length<3?o:o.replace(r[1],r[2])}var aV=Zn(function(r,o,l){return r+(l?"_":"")+o.toLowerCase()});function lV(r,o,l){return l&&typeof l!="number"&&rs(r,o,l)&&(o=l=s),l=l===s?yt:l>>>0,l?(r=rt(r),r&&(typeof o=="string"||o!=null&&!Ec(o))&&(o=_s(o),!o&&Hn(r))?Wr(Fs(r),0,l):r.split(o,l)):[]}var uV=Zn(function(r,o,l){return r+(l?" ":"")+xc(o)});function cV(r,o,l){return r=rt(r),l=l==null?0:yn(Fe(l),0,r.length),o=_s(o),r.slice(l,l+o.length)==o}function dV(r,o,l){var d=v.templateSettings;l&&rs(r,o,l)&&(o=s),r=rt(r),o=Ga({},o,d,zm);var g=Ga({},o.imports,d.imports,zm),y=kt(g),E=Ru(g,y),x,T,B=0,j=o.interpolate||ia,q="__p += '",le=Uu((o.escape||ia).source+"|"+j.source+"|"+(j===hp?qN:ia).source+"|"+(o.evaluate||ia).source+"|$","g"),ge="//# sourceURL="+(it.call(o,"sourceURL")?(o.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++bA+"]")+`
`;r.replace(le,function(Te,qe,Ke,ys,ns,bs){return Ke||(Ke=ys),q+=r.slice(B,bs).replace(ZN,BA),qe&&(x=!0,q+=`' +
__e(`+qe+`) +
'`),ns&&(T=!0,q+=`';
`+ns+`;
__p += '`),Ke&&(q+=`' +
((__t = (`+Ke+`)) == null ? '' : __t) +
'`),B=bs+Te.length,Te}),q+=`';
`;var Se=it.call(o,"variable")&&o.variable;if(!Se)q=`with (obj) {
`+q+`
}
`;else if(jN.test(Se))throw new Pe(c);q=(T?q.replace(Es,""):q).replace(oa,"$1").replace(TN,"$1;"),q="function("+(Se||"obj")+`) {
`+(Se?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(x?", __e = _.escape":"")+(T?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+q+`return __p
}`;var Be=Lg(function(){return et(y,ge+"return "+q).apply(s,E)});if(Be.source=q,wc(Be))throw Be;return Be}function fV(r){return rt(r).toLowerCase()}function hV(r){return rt(r).toUpperCase()}function pV(r,o,l){if(r=rt(r),r&&(l||o===s))return Wp(r);if(!r||!(o=_s(o)))return r;var d=Fs(r),g=Fs(o),y=Gp(d,g),E=Kp(d,g)+1;return Wr(d,y,E).join("")}function mV(r,o,l){if(r=rt(r),r&&(l||o===s))return r.slice(0,Qp(r)+1);if(!r||!(o=_s(o)))return r;var d=Fs(r),g=Kp(d,Fs(o))+1;return Wr(d,0,g).join("")}function gV(r,o,l){if(r=rt(r),r&&(l||o===s))return r.replace(wu,"");if(!r||!(o=_s(o)))return r;var d=Fs(r),g=Gp(d,Fs(o));return Wr(d,g).join("")}function _V(r,o){var l=A,d=be;if(vt(o)){var g="separator"in o?o.separator:g;l="length"in o?Fe(o.length):l,d="omission"in o?_s(o.omission):d}r=rt(r);var y=r.length;if(Hn(r)){var E=Fs(r);y=E.length}if(l>=y)return r;var x=l-qn(d);if(x<1)return d;var T=E?Wr(E,0,x).join(""):r.slice(0,x);if(g===s)return T+d;if(E&&(x+=T.length-x),Ec(g)){if(r.slice(x).search(g)){var B,j=T;for(g.global||(g=Uu(g.source,rt(pp.exec(g))+"g")),g.lastIndex=0;B=g.exec(j);)var q=B.index;T=T.slice(0,q===s?x:q)}}else if(r.indexOf(_s(g),x)!=x){var le=T.lastIndexOf(g);le>-1&&(T=T.slice(0,le))}return T+d}function vV(r){return r=rt(r),r&&NN.test(r)?r.replace(dp,GA):r}var yV=Zn(function(r,o,l){return r+(l?" ":"")+o.toUpperCase()}),xc=Um("toUpperCase");function Rg(r,o,l){return r=rt(r),o=l?s:o,o===s?jA(r)?QA(r):PA(r):r.match(o)||[]}var Lg=je(function(r,o){try{return ms(r,s,o)}catch(l){return wc(l)?l:new Pe(l)}}),bV=wr(function(r,o){return Ds(o,function(l){l=Xs(l),yr(r,l,yc(r[l],r))}),r});function wV(r){var o=r==null?0:r.length,l=De();return r=o?gt(r,function(d){if(typeof d[1]!="function")throw new xs(u);return[l(d[0]),d[1]]}):[],je(function(d){for(var g=-1;++g<o;){var y=r[g];if(ms(y[0],this,d))return ms(y[1],this,d)}})}function EV(r){return GI(Ss(r,_))}function Oc(r){return function(){return r}}function CV(r,o){return r==null||r!==r?o:r}var DV=Bm(),xV=Bm(!0);function ls(r){return r}function Sc(r){return gm(typeof r=="function"?r:Ss(r,_))}function OV(r){return vm(Ss(r,_))}function SV(r,o){return ym(r,Ss(o,_))}var TV=je(function(r,o){return function(l){return Zo(l,r,o)}}),NV=je(function(r,o){return function(l){return Zo(r,l,o)}});function Tc(r,o,l){var d=kt(o),g=Ta(o,d);l==null&&!(vt(o)&&(g.length||!d.length))&&(l=o,o=r,r=this,g=Ta(o,kt(o)));var y=!(vt(l)&&"chain"in l)||!!l.chain,E=Cr(r);return Ds(g,function(x){var T=o[x];r[x]=T,E&&(r.prototype[x]=function(){var B=this.__chain__;if(y||B){var j=r(this.__wrapped__),q=j.__actions__=os(this.__actions__);return q.push({func:T,args:arguments,thisArg:r}),j.__chain__=B,j}return T.apply(r,Br([this.value()],arguments))})}),r}function AV(){return $t._===this&&($t._=sI),this}function Nc(){}function IV(r){return r=Fe(r),je(function(o){return bm(o,r)})}var MV=ic(gt),PV=ic($p),kV=ic(Iu);function Ug(r){return hc(r)?Mu(Xs(r)):cM(r)}function VV(r){return function(o){return r==null?s:bn(r,o)}}var RV=jm(),LV=jm(!0);function Ac(){return[]}function Ic(){return!1}function UV(){return{}}function FV(){return""}function BV(){return!0}function $V(r,o){if(r=Fe(r),r<1||r>we)return[];var l=yt,d=Wt(r,yt);o=De(o),r-=yt;for(var g=Vu(d,o);++l<r;)o(l);return g}function jV(r){return Re(r)?gt(r,Xs):vs(r)?[r]:os(ng(rt(r)))}function HV(r){var o=++eI;return rt(r)+o}var qV=ka(function(r,o){return r+o},0),zV=ac("ceil"),WV=ka(function(r,o){return r/o},1),GV=ac("floor");function KV(r){return r&&r.length?Sa(r,ls,Wu):s}function YV(r,o){return r&&r.length?Sa(r,De(o,2),Wu):s}function QV(r){return qp(r,ls)}function ZV(r,o){return qp(r,De(o,2))}function JV(r){return r&&r.length?Sa(r,ls,Qu):s}function XV(r,o){return r&&r.length?Sa(r,De(o,2),Qu):s}var eR=ka(function(r,o){return r*o},1),tR=ac("round"),sR=ka(function(r,o){return r-o},0);function rR(r){return r&&r.length?ku(r,ls):0}function nR(r,o){return r&&r.length?ku(r,De(o,2)):0}return v.after=OP,v.ary=mg,v.assign=hk,v.assignIn=Ng,v.assignInWith=Ga,v.assignWith=pk,v.at=mk,v.before=gg,v.bind=yc,v.bindAll=bV,v.bindKey=_g,v.castArray=UP,v.chain=fg,v.chunk=GM,v.compact=KM,v.concat=YM,v.cond=wV,v.conforms=EV,v.constant=Oc,v.countBy=rP,v.create=gk,v.curry=vg,v.curryRight=yg,v.debounce=bg,v.defaults=_k,v.defaultsDeep=vk,v.defer=SP,v.delay=TP,v.difference=QM,v.differenceBy=ZM,v.differenceWith=JM,v.drop=XM,v.dropRight=e2,v.dropRightWhile=t2,v.dropWhile=s2,v.fill=r2,v.filter=oP,v.flatMap=lP,v.flatMapDeep=uP,v.flatMapDepth=cP,v.flatten=lg,v.flattenDeep=n2,v.flattenDepth=o2,v.flip=NP,v.flow=DV,v.flowRight=xV,v.fromPairs=i2,v.functions=xk,v.functionsIn=Ok,v.groupBy=dP,v.initial=l2,v.intersection=u2,v.intersectionBy=c2,v.intersectionWith=d2,v.invert=Tk,v.invertBy=Nk,v.invokeMap=hP,v.iteratee=Sc,v.keyBy=pP,v.keys=kt,v.keysIn=as,v.map=$a,v.mapKeys=Ik,v.mapValues=Mk,v.matches=OV,v.matchesProperty=SV,v.memoize=Ha,v.merge=Pk,v.mergeWith=Ag,v.method=TV,v.methodOf=NV,v.mixin=Tc,v.negate=qa,v.nthArg=IV,v.omit=kk,v.omitBy=Vk,v.once=AP,v.orderBy=mP,v.over=MV,v.overArgs=IP,v.overEvery=PV,v.overSome=kV,v.partial=bc,v.partialRight=wg,v.partition=gP,v.pick=Rk,v.pickBy=Ig,v.property=Ug,v.propertyOf=VV,v.pull=m2,v.pullAll=cg,v.pullAllBy=g2,v.pullAllWith=_2,v.pullAt=v2,v.range=RV,v.rangeRight=LV,v.rearg=MP,v.reject=yP,v.remove=y2,v.rest=PP,v.reverse=_c,v.sampleSize=wP,v.set=Uk,v.setWith=Fk,v.shuffle=EP,v.slice=b2,v.sortBy=xP,v.sortedUniq=S2,v.sortedUniqBy=T2,v.split=lV,v.spread=kP,v.tail=N2,v.take=A2,v.takeRight=I2,v.takeRightWhile=M2,v.takeWhile=P2,v.tap=K2,v.throttle=VP,v.thru=Ba,v.toArray=Og,v.toPairs=Mg,v.toPairsIn=Pg,v.toPath=jV,v.toPlainObject=Tg,v.transform=Bk,v.unary=RP,v.union=k2,v.unionBy=V2,v.unionWith=R2,v.uniq=L2,v.uniqBy=U2,v.uniqWith=F2,v.unset=$k,v.unzip=vc,v.unzipWith=dg,v.update=jk,v.updateWith=Hk,v.values=eo,v.valuesIn=qk,v.without=B2,v.words=Rg,v.wrap=LP,v.xor=$2,v.xorBy=j2,v.xorWith=H2,v.zip=q2,v.zipObject=z2,v.zipObjectDeep=W2,v.zipWith=G2,v.entries=Mg,v.entriesIn=Pg,v.extend=Ng,v.extendWith=Ga,Tc(v,v),v.add=qV,v.attempt=Lg,v.camelCase=Kk,v.capitalize=kg,v.ceil=zV,v.clamp=zk,v.clone=FP,v.cloneDeep=$P,v.cloneDeepWith=jP,v.cloneWith=BP,v.conformsTo=HP,v.deburr=Vg,v.defaultTo=CV,v.divide=WV,v.endsWith=Yk,v.eq=$s,v.escape=Qk,v.escapeRegExp=Zk,v.every=nP,v.find=iP,v.findIndex=ig,v.findKey=yk,v.findLast=aP,v.findLastIndex=ag,v.findLastKey=bk,v.floor=GV,v.forEach=hg,v.forEachRight=pg,v.forIn=wk,v.forInRight=Ek,v.forOwn=Ck,v.forOwnRight=Dk,v.get=Cc,v.gt=qP,v.gte=zP,v.has=Sk,v.hasIn=Dc,v.head=ug,v.identity=ls,v.includes=fP,v.indexOf=a2,v.inRange=Wk,v.invoke=Ak,v.isArguments=Cn,v.isArray=Re,v.isArrayBuffer=WP,v.isArrayLike=is,v.isArrayLikeObject=Et,v.isBoolean=GP,v.isBuffer=Gr,v.isDate=KP,v.isElement=YP,v.isEmpty=QP,v.isEqual=ZP,v.isEqualWith=JP,v.isError=wc,v.isFinite=XP,v.isFunction=Cr,v.isInteger=Eg,v.isLength=za,v.isMap=Cg,v.isMatch=ek,v.isMatchWith=tk,v.isNaN=sk,v.isNative=rk,v.isNil=ok,v.isNull=nk,v.isNumber=Dg,v.isObject=vt,v.isObjectLike=bt,v.isPlainObject=ri,v.isRegExp=Ec,v.isSafeInteger=ik,v.isSet=xg,v.isString=Wa,v.isSymbol=vs,v.isTypedArray=Xn,v.isUndefined=ak,v.isWeakMap=lk,v.isWeakSet=uk,v.join=f2,v.kebabCase=Jk,v.last=Ns,v.lastIndexOf=h2,v.lowerCase=Xk,v.lowerFirst=eV,v.lt=ck,v.lte=dk,v.max=KV,v.maxBy=YV,v.mean=QV,v.meanBy=ZV,v.min=JV,v.minBy=XV,v.stubArray=Ac,v.stubFalse=Ic,v.stubObject=UV,v.stubString=FV,v.stubTrue=BV,v.multiply=eR,v.nth=p2,v.noConflict=AV,v.noop=Nc,v.now=ja,v.pad=tV,v.padEnd=sV,v.padStart=rV,v.parseInt=nV,v.random=Gk,v.reduce=_P,v.reduceRight=vP,v.repeat=oV,v.replace=iV,v.result=Lk,v.round=tR,v.runInContext=S,v.sample=bP,v.size=CP,v.snakeCase=aV,v.some=DP,v.sortedIndex=w2,v.sortedIndexBy=E2,v.sortedIndexOf=C2,v.sortedLastIndex=D2,v.sortedLastIndexBy=x2,v.sortedLastIndexOf=O2,v.startCase=uV,v.startsWith=cV,v.subtract=sR,v.sum=rR,v.sumBy=nR,v.template=dV,v.times=$V,v.toFinite=Dr,v.toInteger=Fe,v.toLength=Sg,v.toLower=fV,v.toNumber=As,v.toSafeInteger=fk,v.toString=rt,v.toUpper=hV,v.trim=pV,v.trimEnd=mV,v.trimStart=gV,v.truncate=_V,v.unescape=vV,v.uniqueId=HV,v.upperCase=yV,v.upperFirst=xc,v.each=hg,v.eachRight=pg,v.first=ug,Tc(v,function(){var r={};return Zs(v,function(o,l){it.call(v.prototype,l)||(r[l]=o)}),r}(),{chain:!1}),v.VERSION=i,Ds(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){v[r].placeholder=v}),Ds(["drop","take"],function(r,o){We.prototype[r]=function(l){l=l===s?1:Mt(Fe(l),0);var d=this.__filtered__&&!o?new We(this):this.clone();return d.__filtered__?d.__takeCount__=Wt(l,d.__takeCount__):d.__views__.push({size:Wt(l,yt),type:r+(d.__dir__<0?"Right":"")}),d},We.prototype[r+"Right"]=function(l){return this.reverse()[r](l).reverse()}}),Ds(["filter","map","takeWhile"],function(r,o){var l=o+1,d=l==_t||l==ft;We.prototype[r]=function(g){var y=this.clone();return y.__iteratees__.push({iteratee:De(g,3),type:l}),y.__filtered__=y.__filtered__||d,y}}),Ds(["head","last"],function(r,o){var l="take"+(o?"Right":"");We.prototype[r]=function(){return this[l](1).value()[0]}}),Ds(["initial","tail"],function(r,o){var l="drop"+(o?"":"Right");We.prototype[r]=function(){return this.__filtered__?new We(this):this[l](1)}}),We.prototype.compact=function(){return this.filter(ls)},We.prototype.find=function(r){return this.filter(r).head()},We.prototype.findLast=function(r){return this.reverse().find(r)},We.prototype.invokeMap=je(function(r,o){return typeof r=="function"?new We(this):this.map(function(l){return Zo(l,r,o)})}),We.prototype.reject=function(r){return this.filter(qa(De(r)))},We.prototype.slice=function(r,o){r=Fe(r);var l=this;return l.__filtered__&&(r>0||o<0)?new We(l):(r<0?l=l.takeRight(-r):r&&(l=l.drop(r)),o!==s&&(o=Fe(o),l=o<0?l.dropRight(-o):l.take(o-r)),l)},We.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},We.prototype.toArray=function(){return this.take(yt)},Zs(We.prototype,function(r,o){var l=/^(?:filter|find|map|reject)|While$/.test(o),d=/^(?:head|last)$/.test(o),g=v[d?"take"+(o=="last"?"Right":""):o],y=d||/^find/.test(o);g&&(v.prototype[o]=function(){var E=this.__wrapped__,x=d?[1]:arguments,T=E instanceof We,B=x[0],j=T||Re(E),q=function(qe){var Ke=g.apply(v,Br([qe],x));return d&&le?Ke[0]:Ke};j&&l&&typeof B=="function"&&B.length!=1&&(T=j=!1);var le=this.__chain__,ge=!!this.__actions__.length,Se=y&&!le,Be=T&&!ge;if(!y&&j){E=Be?E:new We(this);var Te=r.apply(E,x);return Te.__actions__.push({func:Ba,args:[q],thisArg:s}),new Os(Te,le)}return Se&&Be?r.apply(this,x):(Te=this.thru(q),Se?d?Te.value()[0]:Te.value():Te)})}),Ds(["pop","push","shift","sort","splice","unshift"],function(r){var o=fa[r],l=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",d=/^(?:pop|shift)$/.test(r);v.prototype[r]=function(){var g=arguments;if(d&&!this.__chain__){var y=this.value();return o.apply(Re(y)?y:[],g)}return this[l](function(E){return o.apply(Re(E)?E:[],g)})}}),Zs(We.prototype,function(r,o){var l=v[o];if(l){var d=l.name+"";it.call(Kn,d)||(Kn[d]=[]),Kn[d].push({name:o,func:l})}}),Kn[Pa(s,I).name]=[{name:"wrapper",func:s}],We.prototype.clone=vI,We.prototype.reverse=yI,We.prototype.value=bI,v.prototype.at=Y2,v.prototype.chain=Q2,v.prototype.commit=Z2,v.prototype.next=J2,v.prototype.plant=eP,v.prototype.reverse=tP,v.prototype.toJSON=v.prototype.valueOf=v.prototype.value=sP,v.prototype.first=v.prototype.head,qo&&(v.prototype[qo]=X2),v},zn=ZA();mn?((mn.exports=zn)._=zn,Su._=zn):$t._=zn}).call(Ro)}(Ji,Ji.exports);var Jh=Ji.exports;const Le=async(e,t)=>{try{const s=window.M.cfg.wwwroot+"/lib/ajax/service.php?sesskey="+window.M.cfg.sesskey+"&info="+e,n=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify([{index:0,methodname:e,args:t}])}),a=n.clone();try{return(await n.json())[0]}catch{return{error:await a.text()}}}catch(s){throw console.error("Erro na chamada AJAX:",s),s}};async function Zw(e={}){try{return await Le("local_offermanager_fetch",{search_string:e.search||"",type:e.type||null,only_active:e.onlyActive===!0,page:e.page||1,per_page:e.perPage||25,sort_by:e.sortBy||"name",sort_direction:e.sortDesc?"DESC":"ASC"})}catch(t){throw new Error(t.message||"Erro ao buscar ofertas")}}async function Xh(e){try{return await Le("local_offermanager_get",{id:e})}catch(t){throw new Error(t.message||"Erro ao buscar oferta")}}async function ep(e){try{return await Le("local_offermanager_save",{id:e.id||0,name:e.name,description:e.description||"",type:e.type||"",audienceids:e.audiences||[]})}catch(t){throw new Error(t.message||"Erro ao salvar oferta")}}async function Jw(e){try{return await Le("local_offermanager_delete",{id:e})}catch(t){throw new Error(t.message||"Erro ao excluir oferta")}}async function fu(){const e=await Le("local_offermanager_get_type_options",{});if(e.error)throw new Error(error.message||"Erro ao buscar opções de tipos");return e}async function Xw(e,t){try{return await Le("local_offermanager_add_course_to_offer",{offer_id:e,course:t})}catch(s){throw new Error(s.message||"Erro ao adicionar curso à oferta")}}async function e0(e,t){try{return await Le("local_offermanager_delete_course",{offercourseid:t})}catch(s){throw new Error(s.message||"Erro ao remover curso da oferta")}}async function t0(e,t,s){try{return await Le("local_offermanager_set_course_status",{id:t,status:s?1:0})}catch(i){throw new Error(i.message||"Erro ao alterar status do curso")}}async function tp(e){var t;try{const s=await Le("local_offermanager_get_audiences",{offerid:0});return(t=s==null?void 0:s.data)!=null&&t.all_audiences?{items:s.data.all_audiences.filter(n=>n.name.toLowerCase().includes(e.toLowerCase())).map(n=>({id:n.id,name:n.name}))}:{items:[]}}catch(s){throw new Error(s)}}async function s0(e,t){try{return await Le("local_offermanager_update_audiences",{offerid:e,audienceids:t})}catch(s){throw new Error(s)}}async function r0(e,t){try{return await Le("local_offermanager_set_status",{id:e,status:!t})}catch(s){throw new Error(s.message||"Erro ao alterar status da oferta")}}async function Lo(e="",t=0){try{return await Le("local_offermanager_get_categories",{search_string:e,offerid:t})}catch(s){throw new Error(s.message||"Erro ao buscar categorias")}}async function sp(e,t,s="",i=1,n=20){try{console.log(`getCoursesByCategory - Parâmetros: offerId=${e}, categoryId=${t}, search=${s}, page=${i}, perPage=${n}`);const a=parseInt(e,10),u=parseInt(t,10),c=parseInt(i,10),h=parseInt(n,10);(isNaN(a)||isNaN(u)||isNaN(c)||isNaN(h))&&console.error("Parâmetros inválidos para getCoursesByCategory");const m={offerid:a,categoryid:u,search_string:s||"",page:c,per_page:h,exclude_courseids:[]};console.log("Chamando endpoint com parâmetros:",m);const p=await Le("local_offermanager_fetch_potential_courses",m);return console.log("Resposta bruta do endpoint:",p),p}catch(a){throw console.error("Erro em getCoursesByCategory:",a),new Error(a.message||"Erro ao buscar cursos")}}async function rp(e,t=""){try{return await Le("local_offermanager_fetch_current_courses",{offerid:e,categoryid:0,search_string:t,exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por nome")}}async function np(e,t){try{return await Le("local_offermanager_fetch_current_courses",{offerid:e,categoryid:t,search_string:"",exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por categoria")}}async function n0(e,t){try{return await Le("local_offermanager_add_courses",{offerid:e,courseids:t})}catch(s){throw new Error(s.message||"Erro ao adicionar cursos à oferta")}}async function Xi(e,t={}){try{return t.sortBy==="name"&&(t.sortBy="fullname"),t.sortBy==="turmasCount"&&(t.sortBy="class_counter"),await Le("local_offermanager_get_current_courses",{offerid:e,only_active:t.onlyActive||!1,courseids:t.courseIds||[],page:t.page||1,per_page:t.perPage||100,sort_by:t.sortBy||"id",sort_direction:t.sortDesc?"DESC":"ASC",course_search:t.courseSearch||"",category_search:t.categorySearch||""})}catch(s){throw new Error(s.message||"Erro ao buscar cursos da oferta")}}async function o0(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={optional_fields:{}};e.offercourseid?s.offercourseid=parseInt(e.offercourseid):console.error("offercourseid não está definido nos parâmetros"),e.classname?s.classname=e.classname:console.error("classname não está definido nos parâmetros"),e.startdate?s.startdate=e.startdate:console.error("startdate não está definido nos parâmetros"),e.teachers&&Array.isArray(e.teachers)?s.teachers=[...e.teachers]:(console.warn("teachers não está definido nos parâmetros ou não é um array"),s.teachers=[]),e.enrol?s.enrol=e.enrol:console.error("enrol não está definido nos parâmetros"),e.optional_fields&&t.forEach(u=>{if(u in e.optional_fields){const c=e.optional_fields[u];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(u)?c!==0&&c!==null&&c!==void 0&&c!==""&&(s.optional_fields[u]=c):typeof c=="boolean"?s.optional_fields[u]=c:Array.isArray(c)?c.length>0&&(s.optional_fields[u]=c):c!=null&&c!==""&&(s.optional_fields[u]=c)}});const n=["offercourseid","classname","startdate","enrol"].filter(u=>!s[u]);if(n.length>0)throw console.error("Campos obrigatórios ausentes no serviço:",n),new Error(`Campos obrigatórios ausentes: ${n.join(", ")}`);return await Le("local_offermanager_add_class",s)}catch(t){throw console.error("Erro ao criar turma:",t),new Error(t.message||"Erro ao criar turma")}}async function hu(e){try{return await Le("local_offermanager_get_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao buscar turma")}}async function i0(e){try{const t=await Le("local_offermanager_get_course",{offercourseid:e});return t.error?[]:t}catch(t){throw new Error(t.message||"Erro ao buscar curso da oferta")}}async function a0(e){try{const t=await Le("local_offermanager_get_classes",{offercourseid:e});return Array.isArray(t)&&t.length===0?(console.log(`Curso ${e} não tem turmas (array vazio)`),[]):Array.isArray(t)&&t.length>0&&t[0].error===!1&&Array.isArray(t[0].data)&&t[0].data.length===0?(console.log(`Curso ${e} não tem turmas (data vazio)`),[]):t}catch(t){throw console.error(`Erro ao buscar turmas do curso ${e}:`,t),new Error(t.message||"Erro ao buscar curso da oferta")}}async function l0(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={offerclassid:e.offerclassid,classname:e.classname,startdate:e.startdate,teachers:e.teachers,optional_fields:{}};return e.optional_fields&&t.forEach(n=>{if(n in e.optional_fields){const a=e.optional_fields[n];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(n)?a!==0&&a!==null&&a!==void 0&&a!==""&&(s.optional_fields[n]=a):typeof a=="boolean"?s.optional_fields[n]=a:Array.isArray(a)?a.length>0&&(s.optional_fields[n]=a):a!=null&&a!==""&&(s.optional_fields[n]=a)}}),console.log("Campos enviados para a API de atualização:",Object.keys(s.optional_fields)),console.log("Objeto completo enviado para a API de atualização:",s),"enrol"in s&&delete s.enrol,await Le("local_offermanager_update_class",s)}catch(t){throw new Error(t.message||"Erro ao atualizar turma")}}async function u0(e){try{return await Le("local_offermanager_delete_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao excluir turma")}}async function c0(e,t=0,s="",i=[]){try{return await Le("local_offermanager_get_potential_teachers",{offercourseid:e,search_string:s,offerclassid:t,excluded_userids:i})}catch(n){throw new Error(n.message||"Erro ao buscar professores")}}async function d0(){try{const e=await Le("local_offermanager_get_situation_list",{});if(e.error)throw new Error(e.exception.message||"Erro ao buscar situações de matrícula");return e}catch(e){throw new Error(e.message||"Erro ao buscar situações de matrícula")}}async function f0(e,t){try{if(!t)throw new Error("É necessário especificar um curso de destino para duplicar a turma");const s=parseInt(e,10),i=parseInt(t,10);if(isNaN(s)||isNaN(i))throw new Error("IDs inválidos para duplicação de turma");return await Le("local_offermanager_duplicate_class",{offerclassid:s,targetoffercourseid:i})}catch(s){throw new Error(s.message||"Erro ao duplicar turma")}}async function h0(e){try{const t=parseInt(e,10);if(isNaN(t))throw new Error("ID da turma inválido");const s=await Le("local_offermanager_get_duplication_courses",{offerclassid:t});let i;return s&&s.data&&Array.isArray(s.data)?i=s.data:i=s,Array.isArray(i)?i.map(a=>({id:a.id,name:a.name||a.fullname,courseid:a.courseid||null,offercourseid:a.offercourseid||a.id,categoryid:a.categoryid||null,category_name:a.category_name||""})):[]}catch(t){try{const s=await Le("local_offermanager_get_class",{id:parseInt(e,10)});let i,n;if(s&&s.data)i=s.data.offerid,n=s.data.offercourseid;else if(s)i=s.offerid,n=s.offercourseid;else throw new Error("Não foi possível determinar a oferta da turma");const a=await Le("local_offermanager_get_offer_courses",{offerid:parseInt(i,10)});let u=[];return a&&Array.isArray(a.data)?u=a.data:a&&a.data&&Array.isArray(a.data.courses)?u=a.data.courses:Array.isArray(a)&&(u=a),u.filter(m=>(m.id||m.offercourseid)!=n).map(m=>({id:m.id,name:m.fullname||m.name,courseid:m.courseid||null,offercourseid:m.id,categoryid:m.categoryid||null,category_name:m.category_name||""}))}catch{throw new Error(t.message||"Erro ao buscar cursos para duplicação")}}}async function pu(e){try{const t=await Le("local_offermanager_get_course_roles",{offercourseid:e});if(t.error)throw new Error(error.message||"Erro ao buscar papéis do curso");return t}catch(t){throw new Error(t.message||"Erro ao buscar papéis do curso")}}async function op(e=!0){try{return await Le("local_offermanager_get_class_methods",{enabled:e})}catch(t){throw new Error(t.message||"Erro ao buscar métodos de inscrição")}}async function p0(e,t){try{return await Le("local_offermanager_set_class_status",{id:e,status:t?1:0})}catch(s){throw new Error(s.message||"Erro ao alterar status da turma")}}const uR="",m0={name:"CustomTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})}}},g0={class:"table-responsive"},_0={class:"table"},v0=["data-value"],y0=["onClick"],b0=["data-column"];function w0(e,t,s,i,n,a){return O(),N("div",g0,[f("table",_0,[f("thead",null,[f("tr",null,[(O(!0),N(Me,null,dt(s.headers,u=>(O(),N("th",{key:u.value,class:he({"text-right":u.align==="right"}),style:us(u.width?{width:u.width}:{}),"data-value":u.value},[u.value==="select"?Vt(e.$slots,"header-select",{key:0},()=>[nt(G(u.text),1)],!0):(O(),N(Me,{key:1},[nt(G(u.text)+" ",1),u.sortable?(O(),N("span",{key:0,onClick:c=>u.sortable?a.handleSort(u.value):null,class:"sort-icon"},[f("i",{class:he(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)],8,y0)):ie("",!0)],64))],14,v0))),128))])]),f("tbody",null,[(O(!0),N(Me,null,dt(s.items,u=>(O(),N("tr",{key:u.id},[(O(!0),N(Me,null,dt(s.headers,c=>(O(),N("td",{key:c.value,class:he({"text-right":c.align==="right"}),"data-column":c.value},[Vt(e.$slots,"item-"+c.value,{item:u},()=>[nt(G(u[c.value]),1)],!0)],10,b0))),128))]))),128))])])])}const hn=He(m0,[["render",w0],["__scopeId","data-v-35ce6ca5"]]),cR="",E0={name:"CustomSelect",props:{modelValue:{type:[Number,String],default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},C0={class:"select-wrapper"},D0=["value","disabled"],x0=["value"],O0={key:1,class:"error-message"};function S0(e,t,s,i,n,a){return O(),N("div",{ref:"selectContainer",class:"custom-select-container",style:us(a.customWidth)},[s.label?(O(),N("div",{key:0,class:he(["select-label",{disabled:s.disabled}])},G(s.label),3)):ie("",!0),f("div",C0,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:he(["custom-select",{error:s.hasError}]),disabled:s.disabled},[(O(!0),N(Me,null,dt(s.options,u=>(O(),N("option",{key:u.value,value:u.value},G(u.label),9,x0))),128))],42,D0),f("div",{class:he(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(O(),N("div",O0,G(s.errorMessage),1)):ie("",!0)],4)}const mr=He(E0,[["render",S0],["__scopeId","data-v-c65f2fc1"]]),dR="",T0={name:"CustomInput",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite aqui..."},type:{type:String,default:"text"},hasSearchIcon:{type:Boolean,default:!1},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},max:{type:[String,Number],default:null}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}},isDateType(){return this.type==="date"},isNumberType(){return this.type==="number"}},methods:{handleInput(e){let t=e.target.value;if(this.isNumberType&&(t.includes("-")&&(t=t.replace(/-/g,""),e.target.value=t),t!=="")){const s=parseFloat(t);s<0||isNaN(s)?(t="",e.target.value=t):this.max!==null&&s>parseFloat(this.max)&&(t=this.max.toString(),e.target.value=t,this.$emit("validate"))}this.$emit("update:modelValue",t),this.hasError&&t&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},N0={key:0,class:"input-label"},A0=["type","placeholder","value","disabled","min","max"],I0={key:0,class:"search-icon"},M0={key:2,class:"error-message"};function P0(e,t,s,i,n,a){return O(),N("div",{class:"custom-input-container",style:us(a.customWidth)},[s.label?(O(),N("div",N0,G(s.label),1)):ie("",!0),f("div",{class:he(["input-wrapper",{"with-icon":s.hasSearchIcon||a.isDateType}])},[f("input",{type:s.type,placeholder:s.placeholder,value:s.modelValue,onInput:t[0]||(t[0]=(...u)=>a.handleInput&&a.handleInput(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),disabled:s.disabled,class:he(["form-control custom-input",{error:s.hasError}]),min:a.isNumberType?0:null,max:s.max},null,42,A0),s.hasSearchIcon?(O(),N("div",I0,t[2]||(t[2]=[f("i",{class:"fas fa-search"},null,-1)]))):ie("",!0),a.isDateType?(O(),N("div",{key:1,class:he(["calendar-icon",{disabled:s.disabled}])},t[3]||(t[3]=[f("i",{class:"fas fa-calendar-alt"},null,-1)]),2)):ie("",!0),s.hasError&&s.errorMessage?(O(),N("div",M0,G(s.errorMessage),1)):ie("",!0)],2)],4)}const Uo=He(T0,[["render",P0],["__scopeId","data-v-ee21b46c"]]),fR="",k0={name:"CustomCheckbox",props:{modelValue:{type:Boolean,default:!1},label:{type:String,default:""},id:{type:String,required:!0},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"]},V0=["id","checked","disabled"],R0=["for"];function L0(e,t,s,i,n,a){return O(),N("div",{class:he(["checkbox-container",{disabled:s.disabled}])},[f("input",{type:"checkbox",id:s.id,checked:s.modelValue,onChange:t[0]||(t[0]=u=>e.$emit("update:modelValue",u.target.checked)),class:"custom-checkbox",disabled:s.disabled},null,40,V0),f("label",{for:s.id,class:he(["checkbox-label",{disabled:s.disabled}])},[Vt(e.$slots,"default",{},()=>[nt(G(s.label),1)],!0)],10,R0)],2)}const ea=He(k0,[["render",L0],["__scopeId","data-v-bb633156"]]),hR="",U0={name:"CustomButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info"].includes(e)},label:{type:String,default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1}},emits:["click"]},F0=["disabled"],B0={key:1};function $0(e,t,s,i,n,a){return O(),N("button",{class:he(["custom-button",[`btn-${s.variant}`]]),disabled:s.disabled,onClick:t[0]||(t[0]=u=>e.$emit("click",u))},[s.icon?(O(),N("i",{key:0,class:he(s.icon)},null,2)):ie("",!0),s.label?(O(),N("span",B0,G(s.label),1)):ie("",!0),Vt(e.$slots,"default",{},void 0,!0)],10,F0)}const Fn=He(U0,[["render",$0],["__scopeId","data-v-9dc7585a"]]),pR="",j0={name:"FilterSection",props:{title:{type:String,default:"FILTRO"},hasActiveTags:{type:Boolean,default:!1}}},H0={class:"filter-section"},q0={key:0},z0={class:"filter-content"},W0={key:1,class:"filter-tags"};function G0(e,t,s,i,n,a){return O(),N("div",H0,[s.title?(O(),N("h2",q0,G(s.title),1)):ie("",!0),f("div",z0,[Vt(e.$slots,"default",{},void 0,!0)]),s.hasActiveTags?(O(),N("div",W0,[Vt(e.$slots,"tags",{},void 0,!0)])):ie("",!0)])}const ip=He(j0,[["render",G0],["__scopeId","data-v-0a9c42cf"]]),mR="",K0={name:"FilterRow",props:{inline:{type:Boolean,default:!1}}};function Y0(e,t,s,i,n,a){return O(),N("div",{class:he(["filter-row",{"filter-row-inline":s.inline}])},[Vt(e.$slots,"default",{},void 0,!0)],2)}const ta=He(K0,[["render",Y0],["__scopeId","data-v-6725a4ba"]]),gR="",Q0={name:"FilterGroup",props:{label:{type:String,default:""},isCheckbox:{type:Boolean,default:!1}}},Z0={key:0,class:"filter-label"},J0={class:"filter-input"};function X0(e,t,s,i,n,a){return O(),N("div",{class:he(["filter-group",{"checkbox-group":s.isCheckbox}])},[s.label?(O(),N("div",Z0,G(s.label),1)):ie("",!0),f("div",J0,[Vt(e.$slots,"default",{},void 0,!0)])],2)}const sa=He(Q0,[["render",X0],["__scopeId","data-v-f69fad7e"]]),_R="",e1={name:"FilterActions"},t1={class:"filter-actions"};function s1(e,t,s,i,n,a){return O(),N("div",t1,[Vt(e.$slots,"default",{},void 0,!0)])}const ap=He(e1,[["render",s1],["__scopeId","data-v-b9facd34"]]),vR="",r1={name:"LFLoading",props:{isLoading:{type:Boolean,default:!1}}},n1={key:0};function o1(e,t,s,i,n,a){return O(),Rt(Rf,null,{default:Ne(()=>[s.isLoading?(O(),N("div",n1,t[0]||(t[0]=[f("div",{class:"modal-overlay"},null,-1),f("div",{class:"loader-wrapper"},[f("span",{class:"loader",role:"status"},[f("span",{class:"sr-only"},"Carregando...")])],-1)]))):ie("",!0)]),_:1})}const mu=He(r1,[["render",o1],["__scopeId","data-v-a4a23ca1"]]),yR="",i1={name:"Toast",props:{show:{type:Boolean,required:!0},message:{type:String,required:!0},type:{type:String,default:"success",validator:function(e){return["success","error","warning","info"].includes(e)}},duration:{type:Number,default:3e3}},computed:{icon(){return{success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"}[this.type]},progressStyle(){return{animation:`progress ${this.duration}ms linear`}}}},a1={class:"toast-content"};function l1(e,t,s,i,n,a){return O(),Rt(J_,{to:"body"},[k(Rf,{name:"toast"},{default:Ne(()=>[s.show?(O(),N("div",{key:0,class:he(["toast",s.type])},[f("div",a1,[f("i",{class:he(a.icon)},null,2),f("span",null,G(s.message),1)]),f("div",{class:"toast-progress",style:us(a.progressStyle)},null,4)],2)):ie("",!0)]),_:1})])}const Fo=He(i1,[["render",l1],["__scopeId","data-v-4e0ca8ca"]]),bR="",u1={name:"Pagination",props:{currentPage:{type:Number,required:!0},perPage:{type:Number,required:!0},total:{type:Number,required:!0},perPageOptions:{type:Array,default:()=>[5,10,20,50]}},emits:["update:currentPage","update:perPage"],computed:{totalPages(){return Math.ceil(this.total/this.perPage)},visiblePages(){const t=Math.floor(2.5);let s=Math.max(1,this.currentPage-t),i=Math.min(this.totalPages,s+5-1);i-s+1<5&&(s=Math.max(1,i-5+1));const n=[];for(let a=s;a<=i;a++)n.push(a);return n},from(){return this.total===0?0:(this.currentPage-1)*this.perPage+1},to(){return Math.min(this.from+this.perPage-1,this.total)},perPageModel:{get(){return this.perPage},set(e){this.$emit("update:perPage",e)}}},methods:{handlePageChange(e){e>=1&&e<=this.totalPages&&this.$emit("update:currentPage",e)},handlePerPageChange(){this.$emit("update:currentPage",1)}}},c1={class:"pagination-container mt-3"},d1={class:"pagination-info"},f1=["value"],h1={class:"pagination-text"},p1={class:"pagination-controls"},m1=["disabled"],g1=["onClick"],_1=["disabled"];function v1(e,t,s,i,n,a){return O(),N("div",c1,[f("div",d1,[lt(f("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>a.perPageModel=u),class:"per-page-select",onChange:t[1]||(t[1]=(...u)=>a.handlePerPageChange&&a.handlePerPageChange(...u))},[(O(!0),N(Me,null,dt(s.perPageOptions,u=>(O(),N("option",{key:u,value:u},G(u),9,f1))),128))],544),[[Yl,a.perPageModel]]),f("span",h1," Mostrando de "+G(a.from)+" até "+G(a.to)+" de "+G(s.total)+" resultados ",1)]),f("div",p1,[f("button",{class:"page-item",disabled:s.currentPage<=1,onClick:t[2]||(t[2]=u=>a.handlePageChange(s.currentPage-1))},t[4]||(t[4]=[f("i",{class:"fas fa-chevron-left"},null,-1)]),8,m1),(O(!0),N(Me,null,dt(a.visiblePages,u=>(O(),N("button",{key:u,class:he(["page-item",{active:u===s.currentPage}]),onClick:c=>a.handlePageChange(u)},G(u),11,g1))),128)),f("button",{class:"page-item",disabled:s.currentPage>=a.totalPages,onClick:t[3]||(t[3]=u=>a.handlePageChange(s.currentPage+1))},t[5]||(t[5]=[f("i",{class:"fas fa-chevron-right"},null,-1)]),8,_1)])])}const pn=He(u1,[["render",v1],["__scopeId","data-v-cd2746ef"]]),wR="",y1={name:"PageHeader",props:{title:{type:String,required:!0}}},b1={class:"page-header"},w1={class:"header-actions"};function E1(e,t,s,i,n,a){return O(),N("div",b1,[f("h2",null,G(s.title),1),f("div",w1,[Vt(e.$slots,"actions",{},void 0,!0)])])}const ra=He(y1,[["render",E1],["__scopeId","data-v-5266bf48"]]),ER="",C1={name:"Modal",components:{CustomButton:Fn},props:{show:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1}},emits:["close","confirm"],mounted(){document.addEventListener("keydown",this.handleKeyDown),this.show&&(document.body.style.overflow="hidden")},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.body.style.overflow=""},watch:{show(e){document.body.style.overflow=e?"hidden":""}},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")}}},D1={class:"modal-body"},x1={key:0,class:"modal-footer"},O1={key:1,class:"modal-footer"};function S1(e,t,s,i,n,a){const u=ee("custom-button");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=c=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:he(["modal-container",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=Lt(()=>{},["stop"]))},[f("div",D1,[Vt(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(O(),N("div",x1,[Vt(e.$slots,"footer",{},void 0,!0)])):s.showDefaultFooter?(O(),N("div",O1,[k(u,{variant:"secondary",label:s.cancelButtonText,onClick:t[0]||(t[0]=c=>e.$emit("close"))},null,8,["label"]),k(u,{variant:"primary",label:s.confirmButtonText,onClick:t[1]||(t[1]=c=>e.$emit("confirm")),disabled:s.confirmDisabled},null,8,["label","disabled"])])):ie("",!0)],2)])):ie("",!0)}const T1=He(C1,[["render",S1],["__scopeId","data-v-87998e77"]]),CR="",N1={name:"ConfirmationModal",components:{Modal:T1},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Confirmação"},message:{type:String,default:""},listTitle:{type:String,default:""},listItems:{type:Array,default:()=>[]},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1},icon:{type:String,default:"warning",validator:e=>["warning","info","error","success","question",""].includes(e)}},emits:["close","confirm"],computed:{iconClass(){return{warning:"fas fa-exclamation-triangle text-warning",info:"fas fa-info-circle text-info",error:"fas fa-times-circle text-danger",success:"fas fa-check-circle text-success",question:"fas fa-question-circle text-primary"}[this.icon]||""},hasListContent(){return this.listItems&&this.listItems.length>0}}},A1={key:0,class:"icon-container"},I1={class:"modal-custom-title"},M1={key:1,class:"message-list"},P1={key:0,class:"list-title"},k1={key:2,class:"message"},V1={class:"modal-custom-footer"},R1=["disabled"];function L1(e,t,s,i,n,a){const u=ee("modal");return O(),Rt(u,{show:s.show,"confirm-button-text":s.confirmButtonText,"cancel-button-text":s.cancelButtonText,"confirm-disabled":s.confirmDisabled,size:"sm","show-default-footer":!1,onClose:t[2]||(t[2]=c=>e.$emit("close")),onConfirm:t[3]||(t[3]=c=>e.$emit("confirm"))},{default:Ne(()=>[f("div",{class:he(["confirmation-content",{"has-list":a.hasListContent}])},[s.icon?(O(),N("div",A1,[f("i",{class:he(a.iconClass)},null,2)])):ie("",!0),f("h3",I1,G(s.title),1),a.hasListContent?(O(),N("div",M1,[s.listTitle?(O(),N("p",P1,G(s.listTitle),1)):ie("",!0),f("ul",null,[(O(!0),N(Me,null,dt(s.listItems,(c,h)=>(O(),N("li",{key:h},G(c),1))),128))])])):(O(),N("div",k1,G(s.message),1)),f("div",V1,[f("button",{class:"btn-cancel",onClick:t[0]||(t[0]=c=>e.$emit("close"))},G(s.cancelButtonText),1),f("button",{class:"btn-danger",disabled:s.confirmDisabled,onClick:t[1]||(t[1]=c=>e.$emit("confirm"))},G(s.confirmButtonText),9,R1)])],2)]),_:1},8,["show","confirm-button-text","cancel-button-text","confirm-disabled"])}const gu=He(N1,[["render",L1],["__scopeId","data-v-3be135e0"]]),DR="",xR="",U1={name:"OfferManagerView",components:{CustomTable:hn,CustomSelect:mr,CustomInput:Uo,CustomCheckbox:ea,CustomButton:Fn,FilterSection:ip,FilterRow:ta,FilterGroup:sa,FilterActions:ap,Pagination:pn,PageHeader:ra,ConfirmationModal:gu,LFLoading:mu,Toast:Fo},setup(){return{router:Zi()}},mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},data(){return{icons:{edit:Qw},inputFilters:{search:"",type:"",hideInactive:!1},appliedFilters:{search:"",type:"",hideInactive:!1},typeOptions:[],typeOptionsEnabled:!1,tableHeaders:[{text:"NOME DA OFERTA",value:"name",sortable:!0},{text:"DESCRIÇÃO",value:"description",sortable:!0},{text:"STATUS DA OFERTA",value:"status",sortable:!0},{text:"TIPO DA OFERTA",value:"type",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offers:[],totalOffers:0,loading:!1,error:null,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,currentPage:1,perPage:10,sortBy:"name",sortDesc:!1,showDeleteModal:!1,offerToDelete:null,showStatusModal:!1,selectedOffer:null}},computed:{typeSelectOptions(){return[{value:"",label:"Todos"},...this.typeOptions]},hasActiveFilters(){return this.appliedFilters.search||this.appliedFilters.hideInactive}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.loadOffers())},currentPage(e,t){e!==t&&this.loadOffers()}},async created(){this.debouncedSearch=Jh.debounce(this.handleSearchInput,300),this.loadTypeOptions(),this.loadOffers()},methods:{getTypeLabel(e){if(!e)return"";const t=this.typeOptions.find(s=>s.value===e||s.label===e);return t?t.label:e},async loadTypeOptions(){const e=await fu();e.data.types&&(this.typeOptionsEnabled=e.data.enabled,e.data.default&&(this.inputFilters.type=e.data.default),this.typeOptions=e.data.types.map(t=>({value:t,label:t})))},async loadOffers(){this.loading=!0,this.error=null;const e={search:this.appliedFilters.search||"",type:this.appliedFilters.type||null,onlyActive:this.appliedFilters.hideInactive===!0,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc},t=await Zw(e);if(t.error)throw new Error(t.message||"Erro ao carregar ofertas");this.offers=t.data.offers||[],this.totalOffers=t.data.total_items||0,this.loading=!1},async handlePageChange(e){e!==this.currentPage&&(this.currentPage=e,await this.loadOffers())},async handlePerPageChange(e){e!==this.perPage&&(this.perPage=e,this.currentPage=1,await this.loadOffers())},async clearFilters(){this.inputFilters.type,this.inputFilters={search:"",type:"",hideInactive:!1},this.appliedFilters={search:"",type:"",hideInactive:!1},this.currentPage=1,await this.loadOffers()},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadOffers()},createNewOffer(){this.router.push({name:"nova-oferta"})},editOffer(e){this.router.push({name:"editar-oferta",params:{id:e.id.toString()}})},deleteOffer(e){e.can_delete&&(this.offerToDelete=e,this.showDeleteModal=!0)},async confirmDeleteOffer(){if(this.offerToDelete)try{this.loading=!0,await Jw(this.offerToDelete.id),await this.loadOffers(),this.showSuccessMessage(`Oferta "${this.offerToDelete.name}" excluída com sucesso`),this.offerToDelete=null,this.showDeleteModal=!1}catch(e){this.error=e.message,this.showErrorMessage(`Erro ao excluir oferta "${this.offerToDelete.name}"`)}finally{this.loading=!1}},toggleOfferStatus(e){e.status===0&&!e.can_activate||(this.selectedOffer=e,this.showStatusModal=!0)},async confirmToggleStatus(){if(this.selectedOffer)try{this.loading=!0,await r0(this.selectedOffer.id,this.selectedOffer.status===1),await this.loadOffers(),this.showSuccessMessage(this.selectedOffer.status===1?`Oferta "${this.selectedOffer.name}" inativada com sucesso`:`Oferta "${this.selectedOffer.name}" inativada com sucesso`),this.selectedOffer=null,this.showStatusModal=!1}catch(e){this.error=e.message,this.showErrorMessage(this.selectedOffer.status===1?`Erro ao inativar oferta "${this.selectedOffer.name}"`:`Erro ao ativar oferta "${this.selectedOffer.name}"`)}finally{this.loading=!1}},getStatusButtonTitle(e){return e.status===1?"Desativar":e.can_activate?"Ativar":"Não é possível ativar esta oferta"},async handleTypeChange(e){this.appliedFilters.type=e,this.currentPage=1,await this.loadOffers()},async handleHideInactiveChange(e){const t=e===!0;this.inputFilters.hideInactive=t,this.appliedFilters.hideInactive=t,this.currentPage=1,await this.loadOffers()},async handleSearchInput(){(this.inputFilters.search.length>=3||this.inputFilters.search==="")&&(this.appliedFilters.search=this.inputFilters.search,this.currentPage=1,await this.loadOffers())},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},F1={id:"offer-manager-component",class:"offer-manager"},B1={class:"new-offer-container"},$1={key:0,class:"alert alert-danger"},j1={class:"table-container"},H1=["title"],q1={class:"action-buttons"},z1=["onClick"],W1=["onClick","disabled","title"],G1={key:0,class:"fas fa-eye"},K1={key:1,class:"fas fa-eye-slash"},Y1=["onClick","disabled","title"];function Q1(e,t,s,i,n,a){var ye,Z,fe,ve,Ae,ae;const u=ee("CustomButton"),c=ee("PageHeader"),h=ee("CustomInput"),m=ee("FilterGroup"),p=ee("CustomSelect"),_=ee("CustomCheckbox"),w=ee("FilterActions"),D=ee("FilterRow"),V=ee("FilterSection"),F=ee("CustomTable"),te=ee("Pagination"),I=ee("ConfirmationModal"),se=ee("LFLoading"),K=ee("Toast");return O(),N("div",F1,[k(c,{title:"Gerenciar Ofertas"},{actions:Ne(()=>[f("div",B1,[k(u,{variant:"primary",label:"Nova Oferta",onClick:a.createNewOffer},null,8,["onClick"])])]),_:1}),k(V,{title:"FILTRO"},{default:Ne(()=>[k(D,{inline:!0},{default:Ne(()=>[k(m,{label:"Oferta"},{default:Ne(()=>[k(h,{modelValue:n.inputFilters.search,"onUpdate:modelValue":t[0]||(t[0]=A=>n.inputFilters.search=A),placeholder:"Buscar...",width:339,"has-search-icon":!0,onInput:e.debouncedSearch},null,8,["modelValue","onInput"])]),_:1}),n.typeOptionsEnabled?(O(),Rt(m,{key:0,label:"Tipo"},{default:Ne(()=>[k(p,{modelValue:n.inputFilters.type,"onUpdate:modelValue":[t[1]||(t[1]=A=>n.inputFilters.type=A),a.handleTypeChange],options:a.typeSelectOptions,width:144},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1})):ie("",!0),k(m,{"is-checkbox":!0},{default:Ne(()=>[k(_,{modelValue:n.inputFilters.hideInactive,"onUpdate:modelValue":[t[2]||(t[2]=A=>n.inputFilters.hideInactive=A),a.handleHideInactiveChange],id:"hideInactive",label:"Não exibir inativas"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),k(w,null,{default:Ne(()=>[k(u,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])]),_:1})]),_:1})]),_:1}),n.error?(O(),N("div",$1,[t[7]||(t[7]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),nt(" "+G(n.error),1)])):ie("",!0),f("div",j1,[k(F,{headers:n.tableHeaders,items:n.offers,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-description":Ne(({item:A})=>[f("span",{title:A.description},G(A.description.length>50?A.description.slice(0,50)+"...":A.description),9,H1)]),"item-type":Ne(({item:A})=>[nt(G(A.type.charAt(0).toUpperCase()+A.type.slice(1)),1)]),"item-status":Ne(({item:A})=>[nt(G(A.status===1?"Ativa":"Inativa"),1)]),"item-actions":Ne(({item:A})=>[f("div",q1,[f("button",{class:"btn-action btn-edit",onClick:be=>a.editOffer(A),title:"Editar"},t[8]||(t[8]=[f("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("g",{"clip-path":"url(#clip0_9_197955)"},[f("path",{d:"M12.854 0.145905C12.7602 0.0521694 12.6331 -0.000488281 12.5005 -0.000488281C12.3679 -0.000488281 12.2408 0.0521694 12.147 0.145905L10.5 1.7929L14.207 5.49991L15.854 3.8539C15.9006 3.80746 15.9375 3.75228 15.9627 3.69154C15.9879 3.63079 16.0009 3.56567 16.0009 3.4999C16.0009 3.43414 15.9879 3.36902 15.9627 3.30827C15.9375 3.24753 15.9006 3.19235 15.854 3.1459L12.854 0.145905ZM13.5 6.2069L9.793 2.4999L3.293 8.9999H3.5C3.63261 8.9999 3.75978 9.05258 3.85355 9.14635C3.94732 9.24012 4 9.3673 4 9.4999V9.9999H4.5C4.63261 9.9999 4.75978 10.0526 4.85355 10.1464C4.94732 10.2401 5 10.3673 5 10.4999V10.9999H5.5C5.63261 10.9999 5.75978 11.0526 5.85355 11.1464C5.94732 11.2401 6 11.3673 6 11.4999V11.9999H6.5C6.63261 11.9999 6.75978 12.0526 6.85355 12.1464C6.94732 12.2401 7 12.3673 7 12.4999V12.7069L13.5 6.2069ZM6.032 13.6749C6.01095 13.619 6.00012 13.5597 6 13.4999V12.9999H5.5C5.36739 12.9999 5.24021 12.9472 5.14644 12.8535C5.05268 12.7597 5 12.6325 5 12.4999V11.9999H4.5C4.36739 11.9999 4.24021 11.9472 4.14644 11.8535C4.05268 11.7597 4 11.6325 4 11.4999V10.9999H3.5C3.36739 10.9999 3.24021 10.9472 3.14644 10.8535C3.05268 10.7597 3 10.6325 3 10.4999V9.9999H2.5C2.44022 9.99981 2.38094 9.98897 2.325 9.96791L2.146 10.1459C2.09835 10.1939 2.06093 10.251 2.036 10.3139L0.0359968 15.3139C-0.000373859 15.4048 -0.00927736 15.5043 0.0103901 15.6002C0.0300575 15.6961 0.077431 15.7841 0.146638 15.8533C0.215844 15.9225 0.30384 15.9698 0.399716 15.9895C0.495593 16.0092 0.595133 16.0003 0.685997 15.9639L5.686 13.9639C5.74886 13.939 5.80601 13.9016 5.854 13.8539L6.032 13.6759V13.6749Z",fill:"var(--primary)"})]),f("defs",null,[f("clipPath",{id:"clip0_9_197955"},[f("rect",{width:"16",height:"16",fill:"white"})])])],-1)]),8,z1),f("button",{class:he(["btn-action",A.status===1?"btn-deactivate":"btn-activate"]),onClick:be=>a.toggleOfferStatus(A),disabled:A.status===0&&!A.can_activate,title:a.getStatusButtonTitle(A)},[A.status===1?(O(),N("i",G1)):(O(),N("i",K1))],10,W1),f("button",{class:"btn-action btn-delete",onClick:be=>a.deleteOffer(A),disabled:!A.can_delete,title:A.can_delete?"Excluir":"Não é possível excluir esta oferta"},t[9]||(t[9]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,Y1)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),k(te,{"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=A=>n.currentPage=A),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=A=>n.perPage=A),total:n.totalOffers,loading:n.loading},null,8,["current-page","per-page","total","loading"]),k(I,{show:n.showDeleteModal,title:"A exclusão desta instância de oferta é uma ação irreversível.",message:"Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[5]||(t[5]=A=>n.showDeleteModal=!1),onConfirm:a.confirmDeleteOffer},null,8,["show","onConfirm"]),k(I,{show:n.showStatusModal,title:((ye=n.selectedOffer)==null?void 0:ye.status)===1?"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((Z=n.selectedOffer)==null?void 0:Z.status)===1?"":"Tem certeza que deseja ativar esta oferta?","list-title":((fe=n.selectedOffer)==null?void 0:fe.status)===1?"Comportamento para os cursos, turmas e matrículas:":"","list-items":((ve=n.selectedOffer)==null?void 0:ve.status)===1?["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((Ae=n.selectedOffer)==null?void 0:Ae.status)===1?"Inativar oferta":"Ativar","cancel-button-text":"Cancelar",icon:((ae=n.selectedOffer)==null?void 0:ae.status)===1?"warning":"question",onClose:t[6]||(t[6]=A=>n.showStatusModal=!1),onConfirm:a.confirmToggleStatus},null,8,["show","title","message","list-title","list-items","confirm-button-text","icon","onConfirm"]),k(se,{"is-loading":n.loading},null,8,["is-loading"]),k(K,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const Z1=He(U1,[["render",Q1],["__scopeId","data-v-78bb8b76"]]);async function J1(e={}){try{const t=await Le("local_offermanager_fetch_enrolments",{offerclassid:e.offerclassid,userids:e.userids||[],page:e.page||1,perpage:e.perpage||20,orderby:e.orderby||"fullname",direction:e.direction||"ASC"});if(t.error)throw new Error(error.message||"Erro ao buscar matrículas");return t}catch(t){throw new Error(t.message||"Erro ao buscar matrículas")}}async function _u(e={}){try{const t=await Le("local_offermanager_get_enroled_users",{offerclassid:e.offerclassid,searchstring:e.searchstring||"",fieldstring:e.fieldstring||"name",excludeduserids:e.excludeduserids||[]});if(t.error)throw new Error(t.message||"Erro ao buscar opções de filtro");return t}catch{throw new Error(response.message||"Erro ao buscar opções de filtro")}}async function X1(e={}){try{const t=await Le("local_offermanager_enrol_users",{offerclassid:e.offerclassid,userids:e.userids||[],roleid:e.roleid||5});return t.error,t}catch(t){return{error:!0,data:[],message:t.message||"Erro ao matricular usuários"}}}async function eE(e,t="",s){const i=await Le("local_offermanager_get_potential_users_to_enrol",{offerclassid:e,search_string:t,excluded_userids:s});return i.error?(console.error("Erro na resposta de getPotentialUsersToEnrol:",i.error),[]):i}async function tE(e={}){try{const t=await Le("local_offermanager_edit_offer_user_enrol",{offeruserenrolid:e.offeruserenrolid,status:e.status,timestart:e.timestart,timeend:e.timeend,roleid:e.roleid});return t==null?(console.error("Resposta vazia de editEnrolment"),!1):typeof t=="boolean"?t:t&&typeof t.success=="boolean"?t.success:t&&t.error===!1?t.data===!0:(console.warn("Formato de resposta não reconhecido:",t),!1)}catch(t){return console.error("Erro ao editar matrícula:",t),!1}}async function sE(e={}){try{const t=await Le("local_offermanager_edit_offer_user_enrol_bulk",{offeruserenrolids:e.offeruserenrolids||[],status:e.status,timestart:e.timestart,timeend:e.timeend});return t&&t.error===!1&&t.data===!0?e.offeruserenrolids.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.error("Formato de resposta não reconhecido:",t),[])}catch(t){throw console.error("Erro ao editar matrículas em lote:",t),t}}async function rE(e){try{const t=await Le("local_offermanager_delete_offer_user_enrol_bulk",{offeruserenrolids:e});return t&&t.error===!1&&t.data===!0?e.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.warn("Formato de resposta não reconhecido:",t),[])}catch(t){return console.error("Erro ao excluir matrículas em lote:",t),[]}}async function nE(e){try{const t=await Le("local_offermanager_get_roles",{offeruserenrolid:e});return t?t.error?(console.error("Erro na resposta de getUserRoles:",t.error),[]):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:[]:(console.error("Resposta vazia de getUserRoles"),[])}catch(t){return console.error("Erro ao buscar papéis do usuário:",t),[]}}async function oE(e,t){try{const s=await Le("local_offermanager_update_roles",{offeruserenrolid:e,roleids:Array.isArray(t)?t:[t]});return s==null?(console.error("Resposta vazia de updateUserRoles"),!1):typeof s=="boolean"?s:s&&typeof s.success=="boolean"?s.success:s&&s.error===!1?s.data===!0:(console.warn("Formato de resposta não reconhecido:",s),!1)}catch(s){return console.error("Erro ao atualizar papéis do usuário:",s),!1}}const OR="",iE={name:"HierarchicalSelect",props:{modelValue:{type:String,default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.$emit("navigate",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate","navigate"]},aE={class:"select-wrapper"},lE=["value","disabled"],uE=["label"],cE=["value"],dE={key:1,class:"error-message"};function fE(e,t,s,i,n,a){return O(),N("div",{ref:"selectContainer",class:"hierarchical-select-container",style:us(a.customWidth)},[s.label?(O(),N("div",{key:0,class:he(["select-label",{disabled:s.disabled}])},G(s.label),3)):ie("",!0),f("div",aE,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:he(["hierarchical-select",{error:s.hasError}]),disabled:s.disabled},[(O(!0),N(Me,null,dt(s.options,u=>(O(),N("optgroup",{key:u.value,label:u.label},[(O(!0),N(Me,null,dt(u.children,c=>(O(),N("option",{key:c.value,value:c.value,class:"child-option"},G(c.label),9,cE))),128))],8,uE))),128))],42,lE),f("div",{class:he(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(O(),N("div",dE,G(s.errorMessage),1)):ie("",!0)],4)}const hE=He(iE,[["render",fE],["__scopeId","data-v-ca8af705"]]),SR="",pE={name:"FilterTag",emits:["remove"]};function mE(e,t,s,i,n,a){return O(),N("div",{class:"tag badge badge-primary",onClick:t[0]||(t[0]=u=>e.$emit("remove"))},[t[1]||(t[1]=f("i",{class:"fas fa-times"},null,-1)),Vt(e.$slots,"default",{},void 0,!0)])}const Bo=He(pE,[["render",mE],["__scopeId","data-v-cf6f2168"]]),TR="",gE={name:"FilterTags"},_E={class:"filter-tags"};function vE(e,t,s,i,n,a){return O(),N("div",_E,[Vt(e.$slots,"default",{},void 0,!0)])}const na=He(gE,[["render",vE],["__scopeId","data-v-746bf68d"]]),NR="",yE={name:"Autocomplete",components:{FilterTag:Bo,FilterTags:na},props:{modelValue:{type:[Array,String,Number],default:()=>[]},items:{type:Array,default:()=>[]},placeholder:{type:String,default:""},label:{type:String,default:""},width:{type:[Number,String],default:"auto"},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},minChars:{type:Number,default:3},showAllOption:{type:Boolean,default:!1},inputMaxWidth:{type:[String,Number],default:null},autoOpen:{type:Boolean,default:!0},noResultsText:{type:String,default:"Nenhum item disponível"},hasSearchIcon:{type:Boolean,default:!1},showFilterTags:{type:Boolean,default:!0},showSelectedInInput:{type:Boolean,default:!1},maxLabelLength:{type:Number,default:30},loading:{type:Boolean,default:!1},keepOpenOnSelect:{type:Boolean,default:!1}},emits:["update:modelValue","select","select-all","load-more","search"],data(){return{searchQuery:"",isOpen:!1,selectedIndex:-1,internalItems:[],uniqueId:`autocomplete-${Math.random().toString(36).substring(2,9)}`,focusedOptionIndex:-1,blurTimeout:null,debouncedSearch:null}},computed:{displayItems(){let e=this.internalItems;if(this.searchQuery){const t=this.searchQuery.toLowerCase();e=this.internalItems.filter(s=>s.label.toLowerCase().includes(t))}return this.showAllOption&&Array.isArray(this.modelValue)?[{label:"Todos",value:"__ALL__"},...e]:e},inputMaxWidthStyle(){return this.inputMaxWidth?typeof this.inputMaxWidth=="number"?`${this.inputMaxWidth}px`:this.inputMaxWidth:null},getSelectedItemLabel(){if(!this.modelValue)return"";const e=this.internalItems.find(t=>t.value===this.modelValue);return e?e.label:""}},created(){this.debouncedSearch=Jh.debounce(e=>{this.$emit("search",e)},300)},watch:{items:{handler(e){this.internalItems=Array.isArray(e)?[...e]:[],this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$refs.inputElement===document.activeElement&&(this.isOpen=!0)},immediate:!0,deep:!0},searchQuery(e){this.isOpen=!0,this.selectedIndex=-1,(e.length===0||e.length>=this.minChars)&&this.debouncedSearch(e)}},methods:{handleFocus(){this.autoOpen&&!this.disabled&&(this.isOpen=!0,this.selectedIndex=-1,this.searchQuery&&(this.searchQuery="",this.$emit("search",""))),this.blurTimeout&&(clearTimeout(this.blurTimeout),this.blurTimeout=null)},openDropdown(){this.disabled||(this.isOpen=!0)},handleBlur(){this.blurTimeout=setTimeout(()=>{this.$el.contains(document.activeElement)||(this.isOpen=!1,this.selectedIndex=-1)},150)},handleInput(){this.disabled||(this.isOpen=!0)},selectItem(e){if(e.value==="__ALL__"){Array.isArray(this.modelValue)&&this.$emit("select-all"),this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()});return}if(Array.isArray(this.modelValue)){const t=[...this.modelValue],s=t.findIndex(i=>i.value===e.value);s===-1?t.push(e):t.splice(s,1),this.$emit("update:modelValue",t)}else this.$emit("update:modelValue",e.value),this.$emit("select",e);this.searchQuery="",this.isOpen=!!this.keepOpenOnSelect,this.selectedIndex=-1,this.$nextTick(()=>{this.autoOpen&&this.focusInput()})},removeItem(e){if(Array.isArray(this.modelValue)){const t=this.modelValue.filter(s=>s.value!==e.value);this.$emit("update:modelValue",t)}else this.$emit("update:modelValue","");Array.isArray(this.modelValue)?(this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1):(this.selectedIndex=-1,this.$nextTick(()=>{this.isOpen=!1})),this.$nextTick(()=>{this.focusInput()})},removeSelectedItem(){this.$emit("update:modelValue",""),this.searchQuery="",this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()})},handleKeydown(e){if(!this.isOpen&&e.key!=="Tab"){this.isOpen=!0;return}switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.min(this.selectedIndex+1,this.displayItems.length-1),this.focusOption(this.selectedIndex);break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.max(this.selectedIndex-1,-1),this.selectedIndex===-1?this.focusInput():this.focusOption(this.selectedIndex);break;case"Enter":e.preventDefault(),this.selectedIndex>=0?this.selectItem(this.displayItems[this.selectedIndex]):this.searchQuery&&this.searchQuery.length>=this.minChars&&this.$emit("search",this.searchQuery);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1;break;case"Tab":this.isOpen&&!e.shiftKey&&this.displayItems.length>0&&(e.preventDefault(),e.stopPropagation(),this.selectedIndex=0,this.focusOption(0));break}},handleOptionKeydown(e,t,s){switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1&&(this.selectedIndex=s+1,this.focusOption(this.selectedIndex));break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput());break;case"Enter":case" ":e.preventDefault(),this.selectItem(t);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1,this.focusInput();break;case"Tab":e.shiftKey?(e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput())):(e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1?(this.selectedIndex=s+1,this.focusOption(this.selectedIndex)):(this.selectedIndex=0,this.focusOption(0)));break}},focusInput(){this.$refs.inputElement&&this.$refs.inputElement.focus()},focusOption(e){requestAnimationFrame(()=>{var s;const t=(s=this.$refs.optionElements)==null?void 0:s[e];t&&t.focus()})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1,this.selectedIndex=-1)},truncateLabel(e){return e?e.length<=this.maxLabelLength?e:e.substring(0,this.maxLabelLength)+"...":""},handleScroll(e){if(!e||!e.target)return;const t=e.target;t.scrollHeight&&t.scrollTop!==void 0&&t.clientHeight&&t.scrollHeight-t.scrollTop-t.clientHeight<50&&this.$emit("load-more")}},mounted(){document.addEventListener("click",this.handleClickOutside),this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$nextTick(()=>{this.isOpen=!0})},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside)}},bE={class:"autocomplete-container"},wE=["id"],EE={class:"autocomplete-wrapper"},CE=["placeholder","disabled","aria-expanded","aria-owns","aria-labelledby","aria-controls"],DE={key:0,class:"selected-item"},xE=["title"],OE=["id"],SE=["id","data-index","aria-selected","tabindex","onClick","onKeydown","title"],TE={class:"item-label"},NE={key:0,class:"fas fa-check"},AE={key:0,class:"dropdown-item loading-item"},IE={key:1,class:"dropdown-item no-results"},ME={key:0,class:"tags-container"};function PE(e,t,s,i,n,a){const u=ee("FilterTag"),c=ee("FilterTags");return O(),N("div",bE,[s.label?(O(),N("label",{key:0,class:he(["filter-label",{required:s.required}]),id:`${n.uniqueId}-label`},G(s.label),11,wE)):ie("",!0),f("div",EE,[f("div",{class:"input-container",style:us({maxWidth:a.inputMaxWidthStyle})},[f("div",{class:he(["input-wrapper",{"has-search-icon":s.hasSearchIcon,"has-selected-item":s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery}])},[lt(f("input",{type:"text",class:"form-control",placeholder:s.placeholder,"onUpdate:modelValue":t[0]||(t[0]=h=>n.searchQuery=h),disabled:s.disabled,"aria-expanded":n.isOpen,"aria-owns":`${n.uniqueId}-listbox`,"aria-labelledby":s.label?`${n.uniqueId}-label`:void 0,"aria-autocomplete":"list","aria-controls":`${n.uniqueId}-listbox`,role:"combobox",tabindex:"0",onKeydown:t[1]||(t[1]=(...h)=>a.handleKeydown&&a.handleKeydown(...h)),onFocus:t[2]||(t[2]=h=>!s.disabled&&a.handleFocus),onInput:t[3]||(t[3]=(...h)=>a.handleInput&&a.handleInput(...h)),onClick:t[4]||(t[4]=h=>!s.disabled&&a.openDropdown()),onBlur:t[5]||(t[5]=(...h)=>a.handleBlur&&a.handleBlur(...h)),ref:"inputElement"},null,40,CE),[[Xt,n.searchQuery]]),s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery?(O(),N("div",DE,[f("span",{class:"selected-text",title:a.getSelectedItemLabel},G(a.truncateLabel(a.getSelectedItemLabel)),9,xE),f("i",{class:"fas fa-times remove-selected",onClick:t[6]||(t[6]=Lt((...h)=>a.removeSelectedItem&&a.removeSelectedItem(...h),["stop"]))})])):ie("",!0),s.hasSearchIcon&&!(s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery)?(O(),N("i",{key:1,class:he(["search-icon",{"fas fa-search":!s.loading,"spinner-border spinner-border-sm":s.loading}])},null,2)):ie("",!0)],2),n.isOpen?(O(),N("div",{key:0,class:"dropdown-menu show",id:`${n.uniqueId}-listbox`,role:"listbox",tabindex:"-1",ref:"dropdownMenu",onScroll:t[7]||(t[7]=(...h)=>a.handleScroll&&a.handleScroll(...h))},[a.displayItems.length>0?(O(),N(Me,{key:0},[(O(!0),N(Me,null,dt(a.displayItems,(h,m)=>(O(),N("div",{key:h.value==="__ALL__"?"__ALL__":h.value,class:he(["dropdown-item",{active:n.selectedIndex===m,selected:h.value!=="__ALL__"&&(Array.isArray(s.modelValue)?s.modelValue.some(p=>p.value===h.value):s.modelValue===h.value)}]),id:`${n.uniqueId}-option-${m}`,role:"option","data-index":m,"aria-selected":n.selectedIndex===m,tabindex:n.selectedIndex===m?0:-1,onClick:p=>a.selectItem(h),onKeydown:p=>a.handleOptionKeydown(p,h,m),ref_for:!0,ref:"optionElements",title:h.label},[f("span",TE,G(a.truncateLabel(h.label)),1),h.value!=="__ALL__"&&Array.isArray(s.modelValue)&&s.modelValue.some(p=>p.value===h.value)?(O(),N("i",NE)):ie("",!0)],42,SE))),128)),s.loading?(O(),N("div",AE,t[8]||(t[8]=[f("span",null,"Carregando mais itens...",-1)]))):ie("",!0)],64)):(O(),N("div",IE,G(s.noResultsText||"Nenhum item disponível"),1))],40,OE)):ie("",!0)],4),s.showFilterTags&&Array.isArray(s.modelValue)&&s.modelValue.length>0?(O(),N("div",ME,[k(c,null,{default:Ne(()=>[(O(!0),N(Me,null,dt(s.modelValue,h=>(O(),Rt(u,{key:h.value,onRemove:m=>a.removeItem(h)},{default:Ne(()=>[nt(G(h.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1})])):ie("",!0)])])}const $o=He(yE,[["render",PE],["__scopeId","data-v-e7ee87a5"]]),AR="",kE={name:"EnrolmentModalNew",components:{Toast:Fo,CustomSelect:mr},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Matricular usuários na turma"},size:{type:String,default:"lg",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Salvar"},cancelButtonText:{type:String,default:"Cancelar"},offerclassid:{type:[Number,String],required:!0},roles:{type:Array,required:!0}},emits:["close","success"],data(){return{enrolmentMethod:"manual",enrolmentMethodOptions:[{value:"manual",label:"Manual"},{value:"batch",label:"Em lote"}],selectedRoleId:"",searchQuery:"",isOpen:!1,userOptions:[],selectedUsers:[],debounceTimer:null,selectedFile:null,csvUsers:[],isDragging:!1,csvDelimiter:",",csvEncoding:"UTF-8",delimiterOptions:[{value:",",label:","},{value:";",label:";"},{value:":",label:":"},{value:"	",label:"\\t"},{value:" ",label:"Espaço"}],encodingOptions:[{value:"UTF-8",label:"UTF-8"},{value:"WINDOWS-1252",label:"WINDOWS-1252"},{value:"ISO-8859-1",label:"ISO-8859-1"},{value:"ASCII",label:"ASCII"},{value:"ISO-8859-2",label:"ISO-8859-2"},{value:"ISO-8859-3",label:"ISO-8859-3"},{value:"ISO-8859-4",label:"ISO-8859-4"},{value:"ISO-8859-5",label:"ISO-8859-5"},{value:"ISO-8859-6",label:"ISO-8859-6"},{value:"ISO-8859-7",label:"ISO-8859-7"},{value:"ISO-8859-8",label:"ISO-8859-8"},{value:"ISO-8859-9",label:"ISO-8859-9"},{value:"ISO-8859-10",label:"ISO-8859-10"},{value:"ISO-8859-13",label:"ISO-8859-13"},{value:"ISO-8859-14",label:"ISO-8859-14"},{value:"ISO-8859-15",label:"ISO-8859-15"},{value:"ISO-8859-16",label:"ISO-8859-16"},{value:"WINDOWS-874",label:"WINDOWS-874"},{value:"WINDOWS-1250",label:"WINDOWS-1250"},{value:"WINDOWS-1251",label:"WINDOWS-1251"},{value:"WINDOWS-1253",label:"WINDOWS-1253"},{value:"WINDOWS-1254",label:"WINDOWS-1254"},{value:"WINDOWS-1255",label:"WINDOWS-1255"},{value:"WINDOWS-1256",label:"WINDOWS-1256"},{value:"WINDOWS-1257",label:"WINDOWS-1257"},{value:"WINDOWS-1258",label:"WINDOWS-1258"},{value:"KOI8-R",label:"KOI8-R"},{value:"MACINTOSH",label:"MACINTOSH"},{value:"IBM866",label:"IBM866"},{value:"BIG5",label:"BIG5"},{value:"EUC-JP",label:"EUC-JP"},{value:"SHIFT_JIS",label:"SHIFT_JIS"},{value:"EUC-KR",label:"EUC-KR"},{value:"UTF-7",label:"UTF-7"},{value:"UTF-16",label:"UTF-16"},{value:"UTF-32",label:"UTF-32"},{value:"UCS-2",label:"UCS-2"},{value:"UCS-4",label:"UCS-4"}],loadingUsers:!1,isSubmitting:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null}},computed:{isFormValid(){return this.roles.length===0?!1:this.enrolmentMethod==="manual"?this.selectedUsers.length>0&&this.selectedRoleId:this.enrolmentMethod==="batch"?this.csvUsers.length>0&&this.selectedRoleId:!1}},watch:{show(e){document.body.style.overflow=e?"hidden":"",e&&this.initializeForm()}},mounted(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("click",this.handleClickOutside),this.show&&(document.body.style.overflow="hidden",this.initializeForm())},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("click",this.handleClickOutside),document.body.style.overflow=""},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")},handleClickOutside(e){if(this.show===!1)return;const t=document.querySelector(".custom-autocomplete-wrapper");t&&!t.contains(e.target)&&(this.isOpen=!1)},async initializeForm(){this.resetForm()},resetForm(){let e=this.roles.find(t=>t.value==5);this.enrolmentMethod="manual",this.selectedRoleId=e.value,this.searchQuery="",this.selectedUsers=[],this.selectedFile=null,this.csvUsers=[],this.csvDelimiter=",",this.csvEncoding="UTF-8"},async fetchPotentialUsersToEnrol(e){this.loadingUsers=!0;let t=this.selectedUsers.map(i=>i.value);const s=await eE(this.offerclassid,e,t);this.userOptions=s.data.map(i=>({value:i.id,label:i.fullname})),this.loadingUsers=!1},handleInput(){const e=this.searchQuery.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialUsersToEnrol(e),this.userOptions&&(this.isOpen=!0)},500):(this.isOpen=!1,this.userOptions=[])},selectUser(e){const t=this.selectedUsers.findIndex(s=>s.value===e.value);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1),this.searchQuery="",this.isOpen=!1},removeUser(e){this.selectedUsers=this.selectedUsers.filter(t=>t.value!==e.value)},onDragOver(){this.isDragging=!0},onDragLeave(){this.isDragging=!1},onDrop(e){this.isDragging=!1;const t=e.dataTransfer.files;t.length>0&&this.processFile(t[0])},handleFileSelect(e){const t=e.target.files;t.length>0&&this.processFile(t[0])},removeFile(){this.selectedFile=null,this.csvUsers=[],this.$refs.fileInput&&(this.$refs.fileInput.value="")},processFile(e){if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");return}this.selectedFile=e,this.readCSVFile(e)},readCSVFile(e){const t=new FileReader;t.onload=s=>{const i=s.target.result;this.parseCSV(i)},t.onerror=s=>{if(console.error("Erro ao ler o arquivo:",s),this.csvEncoding!=="UTF-8"){console.log("Tentando ler com UTF-8 como fallback...");const i=new FileReader;i.onload=n=>{const a=n.target.result;this.parseCSV(a)},i.onerror=()=>{this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos.")},i.readAsText(e,"UTF-8")}else this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato está correto.")};try{t.readAsText(e,this.csvEncoding)}catch(s){console.error("Erro ao tentar ler o arquivo com a codificação selecionada:",s),this.showErrorMessage(`Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`)}},parseCSV(e){try{const t=this.csvDelimiter,s=/�/.test(e);s&&console.warn("O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada.");const i=e.split(/\r?\n/),n=[];if(i.length<2){console.log("EnrolmentModalNew - Linhas do CSV:",i),this.showErrorMessage("O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados.");return}const a=(m,p)=>{if(p==="\\t")return m.split("	");if(p===" ")return m.split(/\s+/);{const _=p.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return m.split(new RegExp(_))}},u=a(i[0].toLowerCase(),t);if(u.length<2||!u.some(m=>m.includes("userid"))||!u.some(m=>m.includes("firstname"))){this.showErrorMessage("O arquivo CSV deve conter colunas para UserID e firstname do usuário.");return}const c=u.findIndex(m=>m.includes("userid")),h=u.findIndex(m=>m.includes("firstname"));for(let m=1;m<i.length;m++){const p=i[m].trim();if(!p)continue;const _=a(p,t);if(_.length>Math.max(c,h)){const w=_[c].trim(),D=_[h].trim();if(w&&D){if(!/^\d+$/.test(w)){console.warn(`Linha ${m+1}: ID inválido '${w}'. Deve ser um número.`);continue}n.push({id:w,name:D})}}}if(n.length===0){s?this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."):this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo.");return}this.csvUsers=n}catch(t){console.error("Erro ao processar arquivo CSV:",t),this.showErrorMessage("Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente.")}},formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},async handleSubmit(){if(this.isFormValid)try{this.isSubmitting=!0;let e=[];this.enrolmentMethod==="manual"?e=this.selectedUsers.map(a=>a.value):this.enrolmentMethod==="batch"&&(e=this.csvUsers.map(a=>parseInt(a.id)));const t=await X1({offerclassid:parseInt(this.offerclassid),userids:e,roleid:parseInt(this.selectedRoleId)});let s=[],i=!1;if(Array.isArray(t))s=t;else if(t&&Array.isArray(t.data))s=t.data;else if(Array.isArray(t)&&t.length>0&&t[0].data)s=t[0].data;else if(Array.isArray(t)&&t.length>0&&t[0].error===!1&&Array.isArray(t[0].data))s=t[0].data;else{this.showErrorMessage("Erro ao processar a resposta do servidor. Verifique o console para mais detalhes.");return}const n=s.filter(a=>a.success).length;if(i=n>0,i)this.showSuccessMessage(`${n} de ${e.length} usuário(s) matriculado(s) com sucesso.`),setTimeout(()=>{this.$emit("success",{count:n,total:e.length}),this.$emit("close")},1500);else{console.error("Nenhum usuário foi matriculado com sucesso");let a="Não foi possível completar a matrícula. Por favor, tente novamente ou entre em contato com o suporte técnico para assistência.";s.length>0&&s[0].message&&(s[0].message.includes("já está matriculado")||s[0].message.includes("already enrolled")?a=s[0].message:s[0].message.includes("Error enrolling user")||s[0].message.includes("[[message:")||s[0].message.includes("enrolment_failed")?a="Não foi possível completar a matrícula. Por favor, tente novamente ou entre em contato com o suporte técnico para assistência.":a=s[0].message),this.showErrorMessage(a)}}catch(e){console.error("Erro ao matricular usuários:",e),this.showErrorMessage(e.message||"Erro ao matricular usuários. Tente novamente.")}finally{this.isSubmitting=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},VE={class:"modal-header"},RE={class:"modal-title"},LE={class:"modal-body"},UE={class:"enrolment-modal"},FE={class:"form-row"},BE={class:"form-group"},$E={class:"limited-width-input"},jE={class:"form-group"},HE={class:"limited-width-input"},qE={key:0,class:"error-message"},zE={key:0,class:"form-group"},WE={class:"user-select-container"},GE={class:"custom-autocomplete-wrapper"},KE={key:0,class:"dropdown-menu show"},YE=["onClick"],QE={key:0,class:"fas fa-check"},ZE={key:0,class:"selected-users-container"},JE={class:"filter-tags"},XE=["onClick"],eC={key:1,class:"form-group"},tC={class:"file-name"},sC={class:"file-size"},rC={key:0,class:"csv-users-preview"},nC={class:"preview-header"},oC={class:"selected-users-container"},iC={class:"filter-tags"},aC={key:0,class:"more-users"},lC={class:"csv-info"},uC={class:"csv-example"},cC=["href"],dC={class:"csv-options-row"},fC={class:"csv-option"},hC={class:"csv-option"},pC={class:"modal-footer"},mC=["disabled"];function gC(e,t,s,i,n,a){const u=ee("CustomSelect"),c=ee("Toast");return O(),N(Me,null,[s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:he(["modal-container",[`modal-${s.size}`]]),onClick:t[14]||(t[14]=Lt(()=>{},["stop"]))},[f("div",VE,[f("h3",RE,G(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",LE,[f("div",UE,[t[31]||(t[31]=f("h3",{class:"section-title"},"OPÇÕES DE MATRÍCULA",-1)),f("div",FE,[f("div",BE,[t[17]||(t[17]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Forma de matrícula"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",$E,[k(u,{modelValue:n.enrolmentMethod,"onUpdate:modelValue":t[1]||(t[1]=h=>n.enrolmentMethod=h),options:n.enrolmentMethodOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"])])]),f("div",jE,[t[18]||(t[18]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Papel para atribuir"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",HE,[k(u,{modelValue:n.selectedRoleId,"onUpdate:modelValue":t[2]||(t[2]=h=>n.selectedRoleId=h),options:s.roles,class:"w-100",required:""},null,8,["modelValue","options"]),s.roles.length===0?(O(),N("div",qE," Não foi possível carregar os papéis disponíveis para esta turma. ")):ie("",!0)])])]),n.enrolmentMethod==="manual"?(O(),N("div",zE,[t[21]||(t[21]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Selecionar usuários"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",WE,[f("div",GE,[lt(f("input",{type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[3]||(t[3]=h=>n.searchQuery=h),onInput:t[4]||(t[4]=(...h)=>a.handleInput&&a.handleInput(...h))},null,544),[[Xt,n.searchQuery]]),t[19]||(t[19]=f("div",{class:"select-arrow"},null,-1)),n.isOpen?(O(),N("div",KE,[(O(!0),N(Me,null,dt(n.userOptions,(h,m)=>(O(),N("div",{key:h.value,class:"dropdown-item",onClick:p=>a.selectUser(h)},[nt(G(h.label)+" ",1),n.selectedUsers.some(p=>p.value===h.value)?(O(),N("i",QE)):ie("",!0)],8,YE))),128))])):ie("",!0)])]),n.selectedUsers.length>0?(O(),N("div",ZE,[f("div",JE,[(O(!0),N(Me,null,dt(n.selectedUsers,h=>(O(),N("div",{key:h.value,class:"tag badge badge-primary",onClick:m=>a.removeUser(h)},[t[20]||(t[20]=f("i",{class:"fas fa-times"},null,-1)),nt(" "+G(h.label),1)],8,XE))),128))])])):ie("",!0)])):ie("",!0),n.enrolmentMethod==="batch"?(O(),N("div",eC,[t[30]||(t[30]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Matricular usuários a partir de um arquivo CSV"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",{class:he(["csv-upload-area",{"drag-over":n.isDragging}]),onDragover:t[6]||(t[6]=Lt((...h)=>a.onDragOver&&a.onDragOver(...h),["prevent"])),onDragleave:t[7]||(t[7]=Lt((...h)=>a.onDragLeave&&a.onDragLeave(...h),["prevent"])),onDrop:t[8]||(t[8]=Lt((...h)=>a.onDrop&&a.onDrop(...h),["prevent"])),onClick:t[9]||(t[9]=h=>e.$refs.fileInput.click())},[f("input",{type:"file",ref:"fileInput",accept:".csv",style:{display:"none"},onChange:t[5]||(t[5]=(...h)=>a.handleFileSelect&&a.handleFileSelect(...h))},null,544),n.selectedFile?(O(),N(Me,{key:1},[t[24]||(t[24]=f("div",{class:"file-icon"},[f("i",{class:"fas fa-file-alt"})],-1)),f("p",tC,G(n.selectedFile.name),1),f("p",sC," ("+G(a.formatFileSize(n.selectedFile.size))+") ",1),t[25]||(t[25]=f("p",{class:"file-replace-text"}," Clique ou arraste outro arquivo para substituir ",-1))],64)):(O(),N(Me,{key:0},[t[22]||(t[22]=f("div",{class:"upload-icon"},[f("i",{class:"fas fa-arrow-down"})],-1)),t[23]||(t[23]=f("p",{class:"upload-text"}," Você pode arrastar e soltar arquivos aqui para adicioná-los. ",-1))],64))],34),n.csvUsers.length>0?(O(),N("div",rC,[f("div",nC,[f("span",null,"Usuários encontrados no arquivo ("+G(n.csvUsers.length)+"):",1)]),f("div",oC,[f("div",iC,[(O(!0),N(Me,null,dt(n.csvUsers.slice(0,5),h=>(O(),N("div",{key:h.id,class:"tag badge badge-primary"},G(h.name),1))),128)),n.csvUsers.length>5?(O(),N("span",aC,"+"+G(n.csvUsers.length-5)+" mais",1)):ie("",!0)])])])):ie("",!0),f("div",lC,[t[29]||(t[29]=f("p",{class:"csv-format-text"},"Formatos aceitos: CSV",-1)),f("div",uC,[t[26]||(t[26]=f("span",{class:"example-label"},"Exemplo CSV",-1)),f("a",{href:`/local/offermanager/export_potential_users.php?offerclassid=${s.offerclassid}`,class:"example-csv"},"example.csv",8,cC)]),f("div",dC,[f("div",fC,[t[27]||(t[27]=f("label",null,"Delimitador do CSV",-1)),k(u,{modelValue:n.csvDelimiter,"onUpdate:modelValue":t[10]||(t[10]=h=>n.csvDelimiter=h),options:n.delimiterOptions,width:160},null,8,["modelValue","options"])]),f("div",hC,[t[28]||(t[28]=f("label",null,"Codificação",-1)),k(u,{modelValue:n.csvEncoding,"onUpdate:modelValue":t[11]||(t[11]=h=>n.csvEncoding=h),options:n.encodingOptions,width:160},null,8,["modelValue","options"])])])])])):ie("",!0),t[32]||(t[32]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1))])]),f("div",pC,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...h)=>a.handleSubmit&&a.handleSubmit(...h)),disabled:n.isSubmitting||!a.isFormValid},G(s.confirmButtonText),9,mC),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=h=>e.$emit("close"))},G(s.cancelButtonText),1)])],2)])):ie("",!0),k(c,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],64)}const _C=He(kE,[["render",gC],["__scopeId","data-v-2c11b891"]]),IR="",vC={name:"EnrollmentDetailsModal",props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},courseName:{type:String,default:""}},emits:["close"],methods:{getEnrolmentMethod(e){if(console.log("EnrollmentDetailsModal - Método de inscrição recebido:",e),!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}}}},yC={class:"modal-header"},bC={key:0,class:"modal-body"},wC={class:"details-container"},EC={class:"detail-row"},CC={class:"detail-value"},DC={class:"detail-row"},xC={class:"detail-value"},OC={class:"detail-row"},SC={class:"detail-value"},TC={class:"detail-row"},NC={class:"detail-value"},AC={class:"detail-row"},IC={class:"detail-value"},MC={key:1,class:"modal-body no-data"},PC={class:"modal-footer"};function kC(e,t,s,i,n,a){return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=u=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[2]||(t[2]=Lt(()=>{},["stop"]))},[f("div",yC,[t[5]||(t[5]=f("h3",{class:"modal-title"},"Informações da matrícula",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=u=>e.$emit("close"))},t[4]||(t[4]=[f("i",{class:"fas fa-times"},null,-1)]))]),s.user?(O(),N("div",bC,[f("div",wC,[f("div",EC,[t[6]||(t[6]=f("div",{class:"detail-label"},"Nome completo",-1)),f("div",CC,G(s.user.fullName),1)]),f("div",DC,[t[7]||(t[7]=f("div",{class:"detail-label"},"Curso",-1)),f("div",xC,G(s.courseName),1)]),f("div",OC,[t[8]||(t[8]=f("div",{class:"detail-label"},"Método de inscrição",-1)),f("div",SC,G(a.getEnrolmentMethod(s.user.enrol)),1)]),f("div",TC,[t[9]||(t[9]=f("div",{class:"detail-label"},"Estado",-1)),f("div",NC,[f("span",{class:he(["status-tag",s.user.status===0?"status-ativo":"status-inativo"])},G(s.user.statusName),3)])]),f("div",AC,[t[10]||(t[10]=f("div",{class:"detail-label"},"Matrícula criada",-1)),f("div",IC,G(s.user.createdDate),1)])])])):(O(),N("div",MC,"Nenhum dado disponível")),f("div",PC,[f("button",{class:"btn btn-secondary",onClick:t[1]||(t[1]=u=>e.$emit("close"))}," Cancelar ")])])])):ie("",!0)}const VC=He(vC,[["render",kC],["__scopeId","data-v-ffabdbe2"]]),MR="",RC={name:"EditEnrollmentModal",components:{CustomSelect:mr},props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",enableStartDate:!0,startDateStr:"",startTimeStr:"00:00",enableEndDate:!1,endDateStr:"",endTimeStr:"00:00",validityPeriod:"unlimited"},statusOptions:[{value:0,label:"Ativo"},{value:1,label:"Suspenso"}],validityPeriodOptions:[{value:"unlimited",label:"Ilimitado"},...Array.from({length:365},(e,t)=>{const s=t+1;return{value:s.toString(),label:s===1?"1 dia":`${s} dias`}})]}},watch:{show(e){e&&this.user&&this.initializeForm()},user(e){e&&this.show&&this.initializeForm()}},methods:{getEnrolmentMethod(e){if(!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}},initializeForm(){if(!this.user)return;this.formData.status=this.user.status;const e=this.user.timestart,t=e?new Date(e*1e3):new Date;this.formData.startDateStr=this.formatDateForInput(t),this.formData.startTimeStr=this.formatTimeForInput(t),this.formData.enableStartDate=!0;const s=this.validityPeriodOptions.filter(i=>i.value!=="unlimited");if(this.user.timeend){const i=new Date(this.user.timeend*1e3);this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.enableEndDate=this.user.timeend>0;const a=i-t,u=Math.ceil(a/(1e3*60*60*24)),c=s.find(h=>parseInt(h.value)===u);this.formData.validityPeriod=c?c.value:"unlimited"}else{const i=new Date;i.setMonth(i.getMonth()+3),this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.validityPeriod="unlimited",this.formData.enableEndDate=!1}},handleValidityPeriodChange(){if(this.formData.validityPeriod!=="unlimited"){this.formData.enableEndDate=!1;const e=this.formData.enableStartDate&&this.formData.startDateStr?new Date(this.formData.startDateStr):new Date,t=parseInt(this.formData.validityPeriod),s=new Date(e);s.setDate(s.getDate()+t),this.formData.endDateStr=this.formatDateForInput(s),this.formData.endTimeStr=this.formData.startTimeStr}},handleEnableEndDateChange(){this.formData.enableEndDate&&(this.formData.validityPeriod="unlimited")},formatDateForInput(e){const t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${s}-${i}`},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},async saveChanges(){var e;if((e=this.user)!=null&&e.offeruserenrolid)try{this.isSubmitting=!0;const t=Number(this.formData.status)||0,s=this.getStartTimestamp(),i=this.getEndTimestamp(s);if(s>i&&i!==0){this.$emit("error","A data de início da matrícula deve ser menor que a data de fim da matrícula.");return}await tE({offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i})?(this.$emit("success",{userId:this.user.id,offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i}),this.$emit("close")):this.$emit("error","Não foi possível editar a matrícula. Por favor, tente novamente.")}catch{this.$emit("error","Ocorreu um erro ao editar a matrícula. Por favor, tente novamente.")}finally{this.isSubmitting=!1}},getStartTimestamp(){if(this.formData.enableStartDate&&this.formData.startDateStr){const e=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr);return Math.floor(e.getTime()/1e3)}return 0},getEndTimestamp(e){if(this.formData.enableEndDate&&this.formData.endDateStr){const t=this.parseDateTime(this.formData.endDateStr,this.formData.endTimeStr);return Math.floor(t.getTime()/1e3)}if(this.formData.validityPeriod!=="unlimited"){const t=parseInt(this.formData.validityPeriod);if(this.formData.enableStartDate&&this.formData.startDateStr){const s=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr),i=new Date(s);return i.setDate(i.getDate()+t),Math.floor(i.getTime()/1e3)}}return 0},parseDateTime(e,t){const[s,i,n]=e.split("-").map(Number),[a,u]=t.split(":").map(Number);return new Date(s,i-1,n,a,u,0,0)}}},LC={class:"modal-header"},UC={class:"modal-title"},FC={class:"modal-body"},BC={class:"enrollment-form"},$C={class:"form-row"},jC={class:"form-value"},HC={class:"form-row"},qC={class:"form-field"},zC={class:"select-wrapper"},WC={class:"form-row"},GC={class:"form-field date-time-field"},KC={class:"date-field"},YC={class:"time-field"},QC={class:"enable-checkbox"},ZC={class:"form-row"},JC={class:"form-field"},XC={class:"select-wrapper"},eD={class:"form-row"},tD={class:"date-field"},sD=["disabled"],rD={class:"time-field"},nD=["disabled"],oD={class:"enable-checkbox"},iD={class:"form-row"},aD={class:"form-value"},lD={class:"modal-footer"},uD={class:"footer-buttons"},cD=["disabled"];function dD(e,t,s,i,n,a){const u=ee("CustomSelect");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=c=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[14]||(t[14]=Lt(()=>{},["stop"]))},[f("div",LC,[f("h3",UC," Editar matrícula de "+G(s.user?s.user.fullName:""),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=c=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",FC,[f("div",BC,[f("div",$C,[t[17]||(t[17]=f("div",{class:"form-label"},"Método de inscrição",-1)),f("div",jC,G(a.getEnrolmentMethod(s.user&&s.user.enrol?s.user.enrol:"")),1)]),f("div",HC,[t[18]||(t[18]=f("div",{class:"form-label"},"Estado",-1)),f("div",qC,[f("div",zC,[k(u,{modelValue:n.formData.status,"onUpdate:modelValue":t[1]||(t[1]=c=>n.formData.status=c),options:n.statusOptions,width:120,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",WC,[t[20]||(t[20]=f("div",{class:"form-label"},"Matrícula começa",-1)),f("div",GC,[f("div",KC,[lt(f("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=c=>n.formData.startDateStr=c),class:"form-control",onChange:t[3]||(t[3]=(...c)=>e.handleStartDateChange&&e.handleStartDateChange(...c))},null,544),[[Xt,n.formData.startDateStr]])]),f("div",YC,[lt(f("input",{type:"time","onUpdate:modelValue":t[4]||(t[4]=c=>n.formData.startTimeStr=c),class:"form-control",onChange:t[5]||(t[5]=(...c)=>e.handleStartTimeChange&&e.handleStartTimeChange(...c))},null,544),[[Xt,n.formData.startTimeStr]])]),f("div",QC,[lt(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[6]||(t[6]=c=>n.formData.enableStartDate=c),class:"custom-checkbox"},null,512),[[$i,n.formData.enableStartDate]]),t[19]||(t[19]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",ZC,[t[21]||(t[21]=f("div",{class:"form-label"},"Período de validade da matrícula",-1)),f("div",JC,[f("div",XC,[k(u,{modelValue:n.formData.validityPeriod,"onUpdate:modelValue":t[7]||(t[7]=c=>n.formData.validityPeriod=c),options:n.validityPeriodOptions,width:120,class:"smaller-select",onChange:a.handleValidityPeriodChange,disabled:n.formData.enableEndDate},null,8,["modelValue","options","onChange","disabled"])])])]),f("div",eD,[t[23]||(t[23]=f("div",{class:"form-label"},"Matrícula termina",-1)),f("div",{class:he(["form-field date-time-field",{"disabled-inputs-only":!n.formData.enableEndDate}])},[f("div",tD,[lt(f("input",{type:"date","onUpdate:modelValue":t[8]||(t[8]=c=>n.formData.endDateStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,sD),[[Xt,n.formData.endDateStr]])]),f("div",rD,[lt(f("input",{type:"time","onUpdate:modelValue":t[9]||(t[9]=c=>n.formData.endTimeStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,nD),[[Xt,n.formData.endTimeStr]])]),f("div",oD,[lt(f("input",{type:"checkbox",id:"enable-enddate","onUpdate:modelValue":t[10]||(t[10]=c=>n.formData.enableEndDate=c),class:"custom-checkbox",onChange:t[11]||(t[11]=(...c)=>a.handleEnableEndDateChange&&a.handleEnableEndDateChange(...c))},null,544),[[$i,n.formData.enableEndDate]]),t[22]||(t[22]=f("label",{for:"enable-enddate"},"Habilitar",-1))])],2)]),f("div",iD,[t[24]||(t[24]=f("div",{class:"form-label"},"Matrícula criada",-1)),f("div",aD,G(s.user&&s.user.createdDate?s.user.createdDate:"Não disponível"),1)])])]),f("div",lD,[t[25]||(t[25]=f("div",{class:"footer-spacer"},null,-1)),f("div",uD,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...c)=>a.saveChanges&&a.saveChanges(...c)),disabled:n.isSubmitting},G(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,cD),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=c=>e.$emit("close"))}," Cancelar ")])])])])):ie("",!0)}const fD=He(RC,[["render",dD],["__scopeId","data-v-f9509e2b"]]),PR="",kR="",hD={name:"BulkEditEnrollmentModal",components:{Pagination:pn,CustomTable:hn,CustomSelect:mr},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",startDateStr:"",startTimeStr:"00:00",enableStartDate:!1,endDateStr:"",endTimeStr:"23:59",enableEndDate:!1},statusOptions:[{value:1,label:"Ativo"},{value:0,label:"Suspenso"}],currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}},watch:{show(e){e&&this.initializeForm()}},methods:{initializeForm(){const e=new Date;this.formData={status:"1",startDateStr:this.formatDateForInput(e),startTimeStr:"00:00",enableStartDate:!1,endDateStr:this.formatDateForInput(e),endTimeStr:"23:59",enableEndDate:!1}},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},handleStartDateChange(){},handleStartTimeChange(){},handleEndDateChange(){},handleEndTimeChange(){},async saveChanges(){if(!this.users||this.users.length===0){console.error("Nenhum usuário selecionado"),this.$emit("error","Nenhum usuário selecionado para edição em lote.");return}try{this.isSubmitting=!0;const e=parseInt(this.formData.status);let t=0;if(this.formData.enableStartDate&&this.formData.startDateStr){const[a,u,c]=this.formData.startDateStr.split("-").map(Number),[h,m]=this.formData.startTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,m,0,0);t=Math.floor(p.getTime()/1e3);const _=p.getTimezoneOffset()*60;t+=_}let s=0;if(this.formData.enableEndDate&&this.formData.endDateStr){const[a,u,c]=this.formData.endDateStr.split("-").map(Number),[h,m]=this.formData.endTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,m,0,0);s=Math.floor(p.getTime()/1e3);const _=p.getTimezoneOffset()*60;s+=_}const i=this.users.filter(a=>a.offeruserenrolid).map(a=>a.offeruserenrolid);if(i.length===0){console.error("Nenhum ID de matrícula encontrado"),this.$emit("error","Não foi possível encontrar os IDs das matrículas dos usuários selecionados.");return}const n=await sE({offeruserenrolids:i,status:e,timestart:t,timeend:s});if(Array.isArray(n)&&n.length>0){const a=n.filter(h=>h.operation_status).length,u=n.length-a;let c="";if(a===n.length)c=`${a} matrícula(s) editada(s) com sucesso.`;else if(a>0)c=`${a} de ${n.length} matrícula(s) editada(s) com sucesso. ${u} matrícula(s) não puderam ser editadas.`;else{c="Nenhuma matrícula pôde ser editada.",this.$emit("error",c);return}this.$emit("success",{message:c,count:a,total:n.length}),this.$emit("close")}else console.error("Resposta inválida da API:",n),this.$emit("error","Não foi possível editar as matrículas. Por favor, tente novamente.")}catch(e){console.error("Erro ao salvar alterações:",e),this.$emit("error","Ocorreu um erro ao editar as matrículas. Por favor, tente novamente.")}finally{this.isSubmitting=!1}}}},pD={class:"modal-header"},mD={class:"modal-body"},gD={class:"enrollment-form"},_D={class:"table-container"},vD={class:"form-row"},yD={class:"form-field"},bD={class:"select-wrapper"},wD={class:"form-row"},ED={class:"form-field date-time-field"},CD={class:"date-field"},DD=["disabled"],xD={class:"time-field"},OD=["disabled"],SD={class:"enable-checkbox"},TD={class:"form-row"},ND={class:"form-field date-time-field"},AD={class:"date-field"},ID=["disabled"],MD={class:"time-field"},PD=["disabled"],kD={class:"enable-checkbox"},VD={class:"modal-footer"},RD={class:"footer-buttons"},LD=["disabled"];function UD(e,t,s,i,n,a){const u=ee("CustomTable"),c=ee("Pagination"),h=ee("CustomSelect");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[17]||(t[17]=m=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[16]||(t[16]=Lt(()=>{},["stop"]))},[f("div",pD,[t[19]||(t[19]=f("h3",{class:"modal-title"},"Edição de Matrículas em Lote",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=m=>e.$emit("close"))},t[18]||(t[18]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",mD,[f("div",gD,[f("div",null,[f("div",_D,[k(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?lt((O(),Rt(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=m=>n.currentPage=m),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=m=>n.perPage=m),total:s.users.length},null,8,["current-page","per-page","total"])),[[Wl,s.users.length>n.perPage]]):ie("",!0),t[20]||(t[20]=f("span",{class:"d-block w-100 border-bottom mt-4"},null,-1))]),f("div",vD,[t[21]||(t[21]=f("div",{class:"form-label"},"Alterar o status",-1)),f("div",yD,[f("div",bD,[k(h,{modelValue:n.formData.status,"onUpdate:modelValue":t[3]||(t[3]=m=>n.formData.status=m),options:n.statusOptions,width:235,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",wD,[t[23]||(t[23]=f("div",{class:"form-label"},"Alterar data de início",-1)),f("div",ED,[f("div",CD,[lt(f("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=m=>n.formData.startDateStr=m),class:"form-control",onChange:t[5]||(t[5]=(...m)=>a.handleStartDateChange&&a.handleStartDateChange(...m)),disabled:!n.formData.enableStartDate},null,40,DD),[[Xt,n.formData.startDateStr]])]),f("div",xD,[lt(f("input",{type:"time","onUpdate:modelValue":t[6]||(t[6]=m=>n.formData.startTimeStr=m),class:"form-control",onChange:t[7]||(t[7]=(...m)=>a.handleStartTimeChange&&a.handleStartTimeChange(...m)),disabled:!n.formData.enableStartDate},null,40,OD),[[Xt,n.formData.startTimeStr]])]),f("div",SD,[lt(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[8]||(t[8]=m=>n.formData.enableStartDate=m),class:"custom-checkbox"},null,512),[[$i,n.formData.enableStartDate]]),t[22]||(t[22]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",TD,[t[25]||(t[25]=f("div",{class:"form-label"},"Alterar data de fim",-1)),f("div",ND,[f("div",AD,[lt(f("input",{type:"date","onUpdate:modelValue":t[9]||(t[9]=m=>n.formData.endDateStr=m),class:"form-control",onChange:t[10]||(t[10]=(...m)=>a.handleEndDateChange&&a.handleEndDateChange(...m)),disabled:!n.formData.enableEndDate},null,40,ID),[[Xt,n.formData.endDateStr]])]),f("div",MD,[lt(f("input",{type:"time","onUpdate:modelValue":t[11]||(t[11]=m=>n.formData.endTimeStr=m),class:"form-control",onChange:t[12]||(t[12]=(...m)=>a.handleEndTimeChange&&a.handleEndTimeChange(...m)),disabled:!n.formData.enableEndDate},null,40,PD),[[Xt,n.formData.endTimeStr]])]),f("div",kD,[lt(f("input",{type:"checkbox",id:"enable-end-date","onUpdate:modelValue":t[13]||(t[13]=m=>n.formData.enableEndDate=m),class:"custom-checkbox"},null,512),[[$i,n.formData.enableEndDate]]),t[24]||(t[24]=f("label",{for:"enable-end-date"},"Habilitar",-1))])])])])]),f("div",VD,[t[26]||(t[26]=f("div",{class:"footer-spacer"},null,-1)),f("div",RD,[f("button",{class:"btn btn-primary",onClick:t[14]||(t[14]=(...m)=>a.saveChanges&&a.saveChanges(...m)),disabled:n.isSubmitting},G(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,LD),f("button",{class:"btn btn-secondary",onClick:t[15]||(t[15]=m=>e.$emit("close"))}," Cancelar ")])])])])):ie("",!0)}const FD=He(hD,[["render",UD],["__scopeId","data-v-1ade848f"]]),VR="",BD={name:"BulkDeleteEnrollmentModal",components:{Pagination:pn,CustomSelect:mr,CustomTable:hn},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}}},$D={class:"modal-header"},jD={class:"modal-body"},HD={class:"enrollment-form"},qD={class:"table-container"},zD={class:"modal-footer"},WD={class:"footer-buttons"},GD=["disabled"];function KD(e,t,s,i,n,a){const u=ee("CustomTable"),c=ee("Pagination");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[6]||(t[6]=h=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[5]||(t[5]=Lt(()=>{},["stop"]))},[f("div",$D,[t[8]||(t[8]=f("h3",{class:"modal-title"},"Remoção de Matrículas",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",jD,[f("div",HD,[f("div",qD,[k(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?lt((O(),Rt(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=h=>n.currentPage=h),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=h=>n.perPage=h),total:s.users.length},null,8,["current-page","per-page","total"])),[[Wl,s.users.length>n.perPage]]):ie("",!0)]),t[9]||(t[9]=f("div",{class:"text-center mt-5"},[f("h5",{class:"mt-1"}," Tem certeza de que deseja excluir essas inscrições de usuário? ")],-1))]),f("div",zD,[f("div",WD,[f("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=h=>e.$emit("confirm")),disabled:n.isSubmitting},G(n.isSubmitting?"Removendo...":"Remover matrículas"),9,GD),f("button",{class:"btn btn-secondary",onClick:t[4]||(t[4]=h=>e.$emit("close"))}," Cancelar ")])])])])):ie("",!0)}const YD=He(BD,[["render",KD],["__scopeId","data-v-cd4191df"]]),RR="",QD={name:"BackButton",props:{label:{type:String,default:"Voltar"},route:{type:String,default:"/local/offermanager/"}},methods:{goBack(){this.$emit("click")}}};function ZD(e,t,s,i,n,a){return O(),N("button",{class:"btn-back",onClick:t[0]||(t[0]=(...u)=>a.goBack&&a.goBack(...u))},[t[1]||(t[1]=f("i",{class:"fas fa-angle-left"},null,-1)),nt(" "+G(s.label),1)])}const vu=He(QD,[["render",ZD],["__scopeId","data-v-eb293b5c"]]),LR="",JD={name:"UserAvatar",props:{imageUrl:{type:String,default:""},fullName:{type:String,required:!0},size:{type:Number,default:32}},computed:{hasImage(){return!!this.imageUrl},initials(){if(!this.fullName)return"";const e=this.fullName.split(" ").filter(i=>i.length>0);if(e.length===0)return"";if(e.length===1)return e[0].substring(0,2).toUpperCase();const t=e[0].charAt(0),s=e[e.length-1].charAt(0);return(t+s).toUpperCase()},backgroundColor(){const e=["#1976D2","#388E3C","#D32F2F","#7B1FA2","#FFA000","#0097A7","#E64A19","#5D4037","#455A64","#616161"];let t=0;for(let i=0;i<this.fullName.length;i++)t=this.fullName.charCodeAt(i)+((t<<5)-t);const s=Math.abs(t)%e.length;return e[s]},avatarStyle(){return{width:`${this.size}px`,height:`${this.size}px`,minWidth:`${this.size}px`,minHeight:`${this.size}px`}}}},XD=["src"];function ex(e,t,s,i,n,a){return O(),N("div",{class:"user-avatar",style:us(a.avatarStyle)},[a.hasImage?(O(),N("img",{key:0,src:s.imageUrl,alt:"Foto de perfil",class:"avatar-image"},null,8,XD)):(O(),N("div",{key:1,class:"avatar-initials",style:us({backgroundColor:a.backgroundColor})},G(a.initials),5))],4)}const tx=He(JD,[["render",ex],["__scopeId","data-v-0a49f249"]]),UR="",sx={name:"RoleSelector",props:{userId:{type:[Number,String],required:!0},offeruserenrolid:{type:[Number,String],required:!0},currentRole:{type:[String,Array],required:!0},offerclassid:{type:[Number,String],required:!0}},data(){return{isEditing:!1,selectedRoles:[],roles:[],loading:!1,initialLoading:!0}},computed:{displayRoleNames(){return Array.isArray(this.currentRole)?this.currentRole.join(", "):String(this.currentRole||"")}},mounted(){this.loadRoles()},methods:{async loadRoles(){var e;this.initialLoading||(this.loading=!0);try{const t=await hu(parseInt(this.offerclassid)),s=((e=t==null?void 0:t.data)==null?void 0:e.offercourseid)||t.offercourseid;if(!s)throw new Error("offercourseid não encontrado");const i=await pu(s);this.roles=Array.isArray(i==null?void 0:i.data)?i.data:Array.isArray(i)?i:[];const n=await nE(this.offeruserenrolid);if(Array.isArray(n)&&n.length)this.selectedRoles=n.map(a=>a.id);else if(Array.isArray(this.currentRole))this.selectedRoles=this.roles.filter(a=>this.currentRole.includes(a.name)).map(a=>a.id);else if(this.currentRole){const a=this.roles.find(u=>u.name.toLowerCase()===String(this.currentRole).toLowerCase());a&&(this.selectedRoles=[a.id])}}catch{this.$emit("error","Não foi possível carregar papéis.")}finally{this.loading=!1,this.initialLoading=!1}},startEditing(){this.isEditing=!0,this.$nextTick(()=>{var e;return(e=this.$refs.roleSelect)==null?void 0:e.focus()})},cancelEdit(){this.isEditing=!1},close(){this.isEditing&&(this.isEditing=!1)},async saveRoles(){if(!this.selectedRoles.length){this.$emit("error","Selecione ao menos um papel.");return}this.loading=!0;try{const e=await oE(this.offeruserenrolid,this.selectedRoles.map(t=>parseInt(t)));if(e===!0||e&&e.error===!1||e&&e.success===!0){const t=this.roles.filter(s=>this.selectedRoles.includes(s.id)).map(s=>s.name);this.$emit("success",{userId:this.userId,offeruserenrolid:this.offeruserenrolid,roleids:this.selectedRoles,roleNames:t}),this.isEditing=!1,this.$emit("reload-table")}else throw new Error("Resposta inesperada do servidor: "+JSON.stringify(e))}catch(e){console.error("Erro ao salvar papéis:",e),this.$emit("error","Não foi possível salvar papéis.")}finally{this.loading=!1}}}},rx={class:"role-selector"},nx={key:1,class:"role-edit-wrapper"},ox={class:"role-edit-container"},ix={class:"select-wrapper"},ax=["value"],lx={class:"role-actions"},ux={key:2,class:"loading-overlay"};function cx(e,t,s,i,n,a){return O(),N("div",rx,[n.isEditing?(O(),N("div",nx,[f("div",ox,[f("div",ix,[lt(f("select",{"onUpdate:modelValue":t[1]||(t[1]=u=>n.selectedRoles=u),class:"role-select",ref:"roleSelect",multiple:"",onClick:t[2]||(t[2]=Lt(()=>{},["stop"])),style:us({height:Math.max(4,n.roles.length)*25+"px"})},[(O(!0),N(Me,null,dt(n.roles,u=>(O(),N("option",{key:u.id,value:u.id},G(u.name),9,ax))),128))],4),[[Yl,n.selectedRoles]])]),f("div",lx,[f("button",{class:"btn-save",onClick:t[3]||(t[3]=Lt((...u)=>a.saveRoles&&a.saveRoles(...u),["stop"])),title:"Salvar"},t[6]||(t[6]=[f("i",{class:"fas fa-check"},null,-1)])),f("button",{class:"btn-cancel",onClick:t[4]||(t[4]=Lt((...u)=>a.cancelEdit&&a.cancelEdit(...u),["stop"])),title:"Cancelar"},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))])])])):(O(),N("div",{key:0,class:"role-display",onClick:t[0]||(t[0]=Lt((...u)=>a.startEditing&&a.startEditing(...u),["stop"]))},[f("span",null,G(a.displayRoleNames),1),t[5]||(t[5]=f("i",{class:"fas fa-pencil-alt edit-icon","aria-hidden":"true"},null,-1))])),n.loading&&n.isEditing?(O(),N("div",ux,t[8]||(t[8]=[f("div",{class:"spinner"},null,-1)]))):ie("",!0)])}const dx=He(sx,[["render",cx],["__scopeId","data-v-21b55063"]]),FR="",fx={name:"RegisteredUsers",components:{CustomTable:hn,CustomSelect:mr,HierarchicalSelect:hE,CustomInput:Uo,CustomCheckbox:ea,CustomButton:Fn,FilterSection:ip,FilterRow:ta,FilterGroup:sa,FilterActions:ap,FilterTag:Bo,FilterTags:na,Pagination:pn,PageHeader:ra,ConfirmationModal:gu,Autocomplete:$o,EnrolmentModalNew:_C,EnrollmentDetailsModal:VC,Toast:Fo,EditEnrollmentModal:fD,BulkEditEnrollmentModal:FD,BulkDeleteEnrollmentModal:YD,BackButton:vu,UserAvatar:tx,RoleSelector:dx,LFLoading:mu},data(){return{offerid:null,offerclassid:null,offercourseid:null,courseid:null,courseContextId:null,filteredUsers:[],nameOptions:[],cpfOptions:[],emailOptions:[],nameSearchInput:"",cpfSearchInput:"",emailSearchInput:"",showNameDropdown:!1,showCpfDropdown:!1,showEmailDropdown:!1,nameDebounceTimer:null,cpfDebounceTimer:null,emailDebounceTimer:null,tableHeaders:[{text:"",value:"select",sortable:!1,width:"50px"},{text:"NOME/SOBRENOME",value:"fullName",sortable:!0,width:"220px"},{text:"E-MAIL",value:"email",sortable:!0},{text:"CPF",value:"cpf",sortable:!0},{text:"PAPÉIS",value:"roles",sortable:!1},{text:"GRUPOS",value:"groups",sortable:!1},{text:"DATA INÍCIO DA MATRÍCULA",value:"startDate",sortable:!0},{text:"DATA FIM DA MATRÍCULA",value:"endDate",sortable:!0},{text:"PRAZO DE CONCLUSÃO",value:"deadline",sortable:!0},{text:"PROGRESSO",value:"progress",sortable:!1},{text:"SITUAÇÃO DE MATRÍCULA",value:"situation",sortable:!0},{text:"NOTA",value:"grade",sortable:!1},{text:"ESTADO",value:"status",sortable:!0}],enrolments:[],totalEnrolments:0,loading:!1,error:null,currentPage:1,perPage:10,sortBy:"fullName",sortDesc:!1,showBulkDeleteEnrollmentModal:!1,showEnrollmentModal:!1,selectedUser:null,showEnrolmentModal:!1,roleOptions:[],showEditEnrollmentModal:!1,showBulkEditEnrollmentModal:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,classDetails:{},selectedUsers:[],selectedBulkAction:"",selectedPageView:"usuarios_matriculados",pageViewOptions:[{value:"matriculas",label:"Matrículas",children:[{value:"usuarios_matriculados",label:"Usuários matriculados"}]},{value:"grupos",label:"Grupos",children:[{value:"grupos",label:"Grupos"},{value:"agrupamentos",label:"Agrupamentos"},{value:"visao_geral",label:"Visão geral"}]},{value:"permissoes",label:"Permissões",children:[{value:"permissoes",label:"Permissões"},{value:"outros_usuarios",label:"Outros usuários"},{value:"verificar_permissoes",label:"Verificar permissões"}]}]}},setup(){return{router:Zi()}},async created(){var t,s,i,n;if(this.offerclassid=this.offerclassid??this.$route.params.offerclassid,!this.offerclassid)throw new Error("ID da turma não foi definido.");this.offerclassid=parseInt(this.offerclassid);const e=await hu(this.offerclassid);if(e.error)throw new Error("Erro ao requisitar informações da turma");this.classDetails=e.data,this.offerid=parseInt((t=this.classDetails)==null?void 0:t.offerid),this.offercourseid=parseInt((s=this.classDetails)==null?void 0:s.offercourseid),this.corseid=(i=this.classDetails)==null?void 0:i.courseid,this.courseContextId=(n=this.classDetails)==null?void 0:n.course_context_id,await this.loadRoles(),await this.loadRegisteredUsers()},mounted(){document.addEventListener("click",this.handleDocumentClick)},beforeUnmount(){document.removeEventListener("click",this.handleDocumentClick),this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer)},computed:{allSelected(){return this.enrolments.length>0&&this.selectedUsers.length===this.enrolments.length},someSelected(){return this.selectedUsers.length>0&&!this.allSelected},excludedUserIds(){return this.filteredUsers.map(e=>e.id||e.value)}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.selectedUsers=[],this.loadRegisteredUsers())},currentPage(e,t){e!==t&&this.loadRegisteredUsers()}},methods:{async loadRegisteredUsers(){this.loading=!0,this.error=null;let e=[];this.filteredUsers.length>0&&(e=this.excludedUserIds);const t={offerclassid:this.offerclassid,userids:e,page:this.currentPage,perpage:this.perPage,orderby:this.mapSortFieldToBackend(this.sortBy||"fullName"),direction:this.sortDesc?"DESC":"ASC"},s=await J1(t);if(s.data){const i=s.data;if(Array.isArray(i.enrolments)){const n=await this.calculateDeadline();this.enrolments=i.enrolments.map(a=>({id:a.userid,offeruserenrolid:a.offeruserenrolid,fullName:a.fullname,email:a.email,cpf:a.cpf,enrol:a.enrol,roles:this.formatRoles(a.roles),groups:a.groups,timecreated:a.timecreated,createdDate:this.formatDateTime(a.timecreated),timestart:a.timestart,timeend:a.timeend,startDate:this.formatDate(a.timestart),endDate:this.formatDate(a.timeend),deadline:n==null?"Imilitado":n===1?"1 dia":`${n} dias`,progress:this.formatProgress(a.progress),situation:a.situation,situationName:a.situation_name,grade:a.grade||"-",status:a.status,statusName:a.status!==void 0?a.status===0?"Ativo":"Suspenso":"-"})),this.totalEnrolments=i.total||this.enrolments.length}}else this.enrolments=[],this.totalEnrolments=0;this.loading=!1},formatDate(e){return!e||e===0?"-":new Date(e*1e3).toLocaleDateString("pt-BR")},formatDateTime(e,t={}){return!e||e===0?"-":(Object.keys(t).length===0&&(t={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),new Date(e*1e3).toLocaleString("pt-BR",t))},async calculateDeadline(){const e=this.classDetails.optional_fields,t=e.enrolperiod,s=e.enableenrolperiod,i=e.enableenddate,n=this.classDetails.startdate,a=e.enddate;let u;if(!i&&!s)return null;if(t===0&&s===!1)if(n&&a){const c=new Date(n),m=new Date(a)-c;u=Math.ceil(m/(1e3*60*60*24))}else u=null;else u=t;return u},formatProgress(e){return e==null?"-":Math.round(e)+"%"},formatRoles(e){return!e||e==="-"?"-":typeof e=="string"?e.split(",").join(", "):Array.isArray(e)&&e.length>0&&typeof e[0]=="object"&&e[0].name?e.map(t=>t.name).join(", "):Array.isArray(e)?e.join(", "):"-"},async loadNameOptions(e){if(!e||e.length<3){this.nameOptions=[],this.showNameDropdown=!1;return}try{const t=await _u({offerclassid:this.offerclassid,fieldstring:"name",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(console.log(t.data),this.nameOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showNameDropdown=this.nameOptions.length>0):(this.nameOptions=[],this.showNameDropdown=!1)}catch{this.nameOptions=[],this.showNameDropdown=!1}},async loadCpfOptions(e){if(!e||e.length<3){this.cpfOptions=[],this.showCpfDropdown=!1;return}try{const t=await _u({offerclassid:this.offerclassid,fieldstring:"username",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.cpfOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showCpfDropdown=this.cpfOptions.length>0):(this.cpfOptions=[],this.showCpfDropdown=!1)}catch{this.cpfOptions=[],this.showCpfDropdown=!1}},async loadEmailOptions(e){if(!e||e.length<3){this.emailOptions=[],this.showEmailDropdown=!1;return}try{const t=await _u({offerclassid:this.offerclassid,fieldstring:"email",searchstring:e,excludeduserids:this.excludedUserIds});!t.error&&t.data?(this.emailOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showEmailDropdown=this.emailOptions.length>0):(this.emailOptions=[],this.showEmailDropdown=!1)}catch{this.emailOptions=[],this.showEmailDropdown=!1}},handleNameInput(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.clearAllFilterOptions(),this.nameSearchInput.length>=3?this.nameDebounceTimer=setTimeout(()=>{this.loadNameOptions(this.nameSearchInput)},500):this.showNameDropdown=!1},handleCpfInput(){this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.clearAllFilterOptions(),this.cpfSearchInput.length>=3?this.cpfDebounceTimer=setTimeout(()=>{this.loadCpfOptions(this.cpfSearchInput)},500):this.showCpfDropdown=!1},handleEmailInput(){this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer),this.clearAllFilterOptions(),this.emailSearchInput.length>=3?this.emailDebounceTimer=setTimeout(()=>{this.loadEmailOptions(this.emailSearchInput)},500):this.showEmailDropdown=!1},selectNameOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"name"}),this.nameSearchInput="",this.clearAllFilterOptions(),this.showNameDropdown=!1,this.loadRegisteredUsers()},selectCpfOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"cpf"}),this.cpfSearchInput="",this.clearAllFilterOptions(),this.showCpfDropdown=!1,this.loadRegisteredUsers()},selectEmailOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"email"}),this.emailSearchInput="",this.clearAllFilterOptions(),this.showEmailDropdown=!1,this.loadRegisteredUsers()},closeDropdowns(){this.showNameDropdown=!1,this.showCpfDropdown=!1,this.showEmailDropdown=!1},clearAllFilterOptions(){this.nameOptions=[],this.cpfOptions=[],this.emailOptions=[]},handleDocumentClick(e){const t=document.querySelectorAll(".filter-input-container");let s=!0;t.forEach(i=>{i.contains(e.target)&&(s=!1)}),s&&this.closeDropdowns()},removeFilter(e){const t=this.filteredUsers.findIndex(s=>s.id===e||s.value===e);t!==-1&&this.filteredUsers.splice(t,1),this.loadRegisteredUsers()},toggleSelectAll(){this.allSelected?this.selectedUsers=[]:this.selectedUsers=this.enrolments.map(e=>e.id)},toggleSelectUser(e){const t=this.selectedUsers.indexOf(e);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1)},isSelected(e){return this.selectedUsers.includes(e)},async handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,await this.loadRegisteredUsers()},mapSortFieldToBackend(e){return{fullName:"fullname",email:"email",cpf:"cpf",startDate:"startdate",endDate:"enddate",deadline:"enrolperiod",situation:"situation",status:"status"}[e]||"fullname"},addNewUser(){var e;if(this.classDetails&&((e=this.classDetails)==null?void 0:e.operational_cycle)===2){this.error="Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";return}this.showEnrolmentModal=!0},closeEnrolmentModal(){this.showEnrolmentModal=!1},async goBack(){this.router.push({name:"editar-oferta",params:{id:this.offerid}})},viewUserProfile(e){if(!e)return;const t=`/user/view.php?id=${e}&course=${this.courseid}`;window.location.href=t},async handlePageViewChange(e){let t=this.offerclassid,s=this.courseid,i=this.courseContextId;const n={usuarios_matriculados:`/local/offermanager/new-subscribed-users/${t}`,grupos:`/group/index.php?id=${s}`,agrupamentos:`/group/groupings.php?id=${s}`,visao_geral:`/user/index.php?id=${s}`,permissoes:`/admin/roles/permissions.php?contextid=${i}`,outros_usuarios:`/enrol/otherusers.php?id=${s}`,verificar_permissoes:`/admin/roles/check.php?contextid=${i}`};n[e]&&(window.location.href=n[e])},async handleEnrolmentSuccess(){await this.loadRegisteredUsers()},async loadRoles(){const e=await pu(this.offercourseid);if(e.error)throw new Error("Erro ao requisitar papéis do curso");this.roleOptions=e.data.map(t=>({value:t.id,label:t.name}))},showEnrollmentDetails(e){this.selectedUser={fullName:e.fullName,enrol:e.enrol||"Inscrições manuais",status:e.status||0,statusName:e.statusName||"Ativo",startDate:e.startDate||"Não disponível",createdDate:e.createdDate||e.startDate||"Não disponível"},this.showEnrollmentModal=!0},closeEnrollmentModal(){this.showEnrollmentModal=!1,this.selectedUser=null},closeEditEnrollmentModal(){this.showEditEnrollmentModal=!1,this.selectedUser=null},async handleEditEnrollmentSuccess(e){if(this.showSuccessMessage("Matrícula editada com sucesso."),e.roleid){let t=null;if(this.roleOptions&&this.roleOptions.length>0){const i=this.roleOptions.find(n=>n.value===String(e.roleid));i&&(t=i.name)}if(!t){await this.loadRegisteredUsers(),this.showEditEnrollmentModal=!1,this.selectedUser=null;return}const s=this.enrolments.findIndex(i=>i.id===e.userId);if(s!==-1){if(t&&(this.enrolments[s].roles=t),e.status!==void 0&&(this.enrolments[s].status=e.status,e.status===1?this.enrolments[s].statusName="Ativo":e.status===0&&(this.enrolments[s].statusName="Suspenso")),e.timestart){const i=new Date(e.timestart*1e3);this.enrolments[s].startDate=i.toLocaleDateString("pt-BR")}if(e.timeend){const i=new Date(e.timeend*1e3);this.enrolments[s].endDate=i.toLocaleDateString("pt-BR")}}else await this.loadRegisteredUsers()}else await this.loadRegisteredUsers();this.showEditEnrollmentModal=!1,this.selectedUser=null},handleEditEnrollmentError(e){this.showErrorMessage(e||"Não foi possível editar a matrícula. Por favor, tente novamente.")},handleRoleUpdateSuccess(e){this.showSuccessMessage("Papel atualizado com sucesso.");const t=this.enrolments.findIndex(s=>s.id===e.userId);t!==-1?this.enrolments[t].roles=e.roleName:this.reloadTable()},handleRoleUpdateError(e){this.showErrorMessage(e||"Ocorreu um erro ao atualizar o papel do usuário.")},reloadTable(){this.loadRegisteredUsers()},editUser(e){let t=null;e.roles&&e.roleid&&(t=e.roleid),this.selectedUser={id:e.id,offeruserenrolid:e.offeruserenrolid,fullName:e.fullName,enrol:e.enrol,status:e.status,statusName:e.statusName,roles:e.roles,roleid:t,startDate:e.startDate,timestart:e.timestart,timeend:e.timeend,createdDate:e.createdDate||"-"},this.showEditEnrollmentModal=!0},async confirmeBulkDeleteEnrollment(){this.loading=!0;const e=[];for(const i of this.selectedUsers){const n=this.enrolments.find(a=>a.id===i);n&&n.offeruserenrolid&&e.push(n.offeruserenrolid)}if(e.length===0){this.showErrorMessage("Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."),this.loading=!1;return}const t=`Processando exclusão de ${e.length} matrícula(s)...`;this.showSuccessMessage(t);const s=await rE(e);if(s&&s.length>0){const i=s.filter(a=>a.operation_status).length,n=s.length-i;i>0?(this.showSuccessMessage(`${i} matrícula(s) cancelada(s) com sucesso.${n>0?` ${n} matrícula(s) não puderam ser canceladas.`:""}`),await this.loadRegisteredUsers(),this.selectedUsers=[]):this.showErrorMessage("Não foi possível cancelar as matrículas. Por favor, tente novamente.")}else this.showSuccessMessage(`${e.length} matrícula(s) cancelada(s) com sucesso.`),await this.loadRegisteredUsers(),this.selectedUsers=[];this.showBulkDeleteEnrollmentModal=!1,this.loading=!1},handleBulkAction(){if(this.selectedBulkAction){if(this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para realizar esta ação."),this.selectedBulkAction="";return}switch(this.selectedBulkAction){case"message":this.sendMessage();break;case"note":this.writeNote();break;case"download_csv":this.downloadData("csv");break;case"download_xlsx":this.downloadData("xlsx");break;case"download_html":this.downloadData("html");break;case"download_json":this.downloadData("json");break;case"download_ods":this.downloadData("ods");break;case"download_pdf":this.downloadData("pdf");break;case"edit_enrolment":this.editEnrolments();break;case"delete_enrolment":this.bulkDeleteEnrollment();break}this.selectedBulkAction=""}},sendMessage(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para enviar mensagem.");return}this.showSendMessageModal(this.selectedUsers)},showSendMessageModal(e){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}window.require(["core_message/message_send_bulk"],t=>{if(typeof t.showModal!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}t.showModal(e,()=>{this.selectedBulkAction=""})},t=>{this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.")})},writeNote(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para escrever anotação.");return}this.showAddNoteModal(this.courseid,this.selectedUsers)},showAddNoteModal(e,t){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}window.require(["core_user/local/participants/bulkactions"],s=>{if(typeof s.showAddNote!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}const i={personal:"Pessoal",course:"Curso",site:"Site"};s.showAddNote(e,t,i,"").then(n=>(n.getRoot().on("hidden.bs.modal",()=>{this.selectedBulkAction=""}),n)).catch(n=>{this.showErrorMessage("Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},s=>{this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},downloadData(e){this.selectedUsers.length!==0&&this.prepareLocalDownload(e)},prepareLocalDownload(e){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Nenhum usuário selecionado para download.");return}const t=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))for(const s of this.selectedUsers){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else for(const s of this.selectedUsers)if(typeof s=="number"){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else if(typeof s=="object"&&s!==null){const i={ID:s.id||"",Nome:s.fullName||s.name||"",Email:s.email||"",CPF:s.cpf||"",Papéis:s.roles||"",Grupos:s.groups||"","Data de Início":s.startDate||"","Data de Término":s.endDate||"",Prazo:s.deadline||"",Progresso:s.progress||"",Situação:s.situationName||s.situation||"",Nota:s.grade||"",Estado:s.statusName||""};t.push(i)}if(t.length===0){this.showErrorMessage("Nenhum dado disponível para download.");return}switch(e){case"csv":this.downloadCSV(t);break;case"xlsx":this.downloadXLSX(t);break;case"html":this.downloadHTML(t);break;case"json":this.downloadJSON(t);break;case"ods":this.downloadODS(t);break;case"pdf":this.downloadPDF(t);break;default:this.showErrorMessage("Formato de download não suportado.");break}},downloadCSV(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=s.map(h=>h.replace(/([A-Z])/g," $1").replace(/^./,m=>m.toUpperCase()).trim()),n=t+[i.join(","),...e.map(h=>s.map(m=>{const p=h[m]||"";return`"${String(p).replace(/"/g,'""')}"`}).join(","))].join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u)},downloadXLSX(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=t+[s.join(","),...e.map(c=>s.map(h=>{const m=c[h]||"";return`"${String(m).replace(/"/g,'""')}"`}).join(","))].join(`
`),n=new Blob([i],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(n),u=document.createElement("a");u.setAttribute("href",a),u.setAttribute("download","usuarios_matriculados.csv"),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(a),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser aberto no Excel.")},downloadHTML(e){if(e.length===0)return;const t=Object.keys(e[0]),s=[];for(let I=0;I<t.length;I++){const se=t[I].replace(/([A-Z])/g," $1").replace(/^./,K=>K.toUpperCase()).trim();s.push(se)}let i="";for(let I=0;I<s.length;I++)i+="<th>"+s[I]+"</th>";let n="";for(let I=0;I<e.length;I++){let se="<tr>";for(let K=0;K<t.length;K++)se+="<td>"+(e[I][t[K]]||"")+"</td>";se+="</tr>",n+=se}const a='<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>',u="<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>",c="</head><body><h1>Usuários Matriculados</h1>",h="<table><thead><tr>",m="</tr></thead><tbody>",p="</tbody></table>",_='<div class="footer">Gerado em '+new Date().toLocaleString()+"</div>",w="</body></html>",D=a+u+c+h+i+m+n+p+_+w,V=new Blob([D],{type:"text/html;charset=utf-8;"}),F=URL.createObjectURL(V),te=document.createElement("a");te.setAttribute("href",F),te.setAttribute("download","usuarios_matriculados.html"),te.style.visibility="hidden",document.body.appendChild(te),te.click(),document.body.removeChild(te),URL.revokeObjectURL(F),this.showSuccessMessage("Download concluído. O arquivo HTML foi salvo com sucesso.")},downloadJSON(e){if(e.length===0)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json;charset=utf-8;"}),i=URL.createObjectURL(s),n=document.createElement("a");n.setAttribute("href",i),n.setAttribute("download","usuarios_matriculados.json"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(i)},downloadODS(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]);let i=[];i.push(s.join(",")),e.forEach(h=>{const m=s.map(p=>{const _=h[p]||"";return'"'+String(_).replace(/"/g,'""')+'"'});i.push(m.join(","))});const n=t+i.join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS.")},downloadPDF(e){e.length!==0&&(this.downloadHTML(e),this.showSuccessMessage("Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."))},editEnrolments(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para editar matrícula.");return}if(this.selectedUsers.length===1){const e=this.selectedUsers[0],t=this.enrolments.find(s=>s.id===e);t?this.editUser(t):this.showErrorMessage("Usuário não encontrado. Por favor, tente novamente.")}else this.showBulkEditEnrollmentModal=!0},async handleBulkEditEnrollmentSuccess(e){this.showSuccessMessage(e.message||"Matrículas editadas com sucesso."),await this.loadRegisteredUsers(),this.selectedUsers=[],this.showBulkEditEnrollmentModal=!1},handleBulkEditEnrollmentError(e){const t="Não foi possível editar as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},bulkDeleteEnrollment(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para excluir matrícula.");return}this.showBulkDeleteEnrollmentModal=!0},handleBulkDeleteEnrollmentError(e){const t="Não foi possível excluir as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},hx={id:"offer-manager-component",class:"offer-manager"},px={style:{display:"flex","align-items":"center","margin-bottom":"20px",gap:"10px"}},mx={style:{width:"240px"}},gx={class:"filters-section mb-3"},_x={class:"row"},vx={class:"col-md-4"},yx={class:"filter-input-container position-relative"},bx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},wx=["onClick"],Ex={class:"col-md-4"},Cx={class:"filter-input-container position-relative"},Dx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},xx=["onClick"],Ox={class:"col-md-4"},Sx={class:"filter-input-container position-relative"},Tx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},Nx=["onClick"],Ax={key:0,class:"alert alert-danger"},Ix={class:"table-container"},Mx={class:"checkbox-container"},Px=["checked","indeterminate"],kx={class:"checkbox-container"},Vx=["checked","onChange"],Rx=["href","title"],Lx={class:"user-name-link"},Ux={class:"progress-container"},Fx={class:"progress-text"},Bx={class:"status-container"},$x={class:"status-actions"},jx=["onClick"],Hx=["onClick"],qx={class:"selected-users-actions"},zx={class:"bulk-actions-container"},Wx={key:1,class:"bottom-enroll-button"};function Gx(e,t,s,i,n,a){var ve,Ae,ae;const u=ee("BackButton"),c=ee("PageHeader"),h=ee("HierarchicalSelect"),m=ee("CustomButton"),p=ee("FilterTag"),_=ee("FilterTags"),w=ee("UserAvatar"),D=ee("RoleSelector"),V=ee("CustomTable"),F=ee("Pagination"),te=ee("EnrollmentDetailsModal"),I=ee("EnrolmentModalNew"),se=ee("EditEnrollmentModal"),K=ee("BulkEditEnrollmentModal"),ye=ee("BulkDeleteEnrollmentModal"),Z=ee("LFLoading"),fe=ee("Toast");return O(),N("div",hx,[k(c,{title:"Usuários matriculados"},{actions:Ne(()=>[k(u,{onClick:a.goBack},null,8,["onClick"])]),_:1}),f("div",px,[f("div",mx,[k(h,{modelValue:n.selectedPageView,"onUpdate:modelValue":t[0]||(t[0]=A=>n.selectedPageView=A),options:n.pageViewOptions,onNavigate:a.handlePageViewChange},null,8,["modelValue","options","onNavigate"])]),!n.classDetails||((ve=n.classDetails)==null?void 0:ve.operational_cycle)!==2?(O(),Rt(m,{key:0,variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])):ie("",!0)]),f("div",gx,[f("div",_x,[f("div",vx,[f("div",yx,[t[17]||(t[17]=f("label",{for:"name-filter",class:"form-label text-muted small"},"Filtrar por nome",-1)),lt(f("input",{id:"name-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[1]||(t[1]=A=>n.nameSearchInput=A),onInput:t[2]||(t[2]=(...A)=>a.handleNameInput&&a.handleNameInput(...A)),onFocus:t[3]||(t[3]=A=>n.showNameDropdown=n.nameOptions.length>0)},null,544),[[Xt,n.nameSearchInput]]),n.showNameDropdown&&n.nameOptions.length>0?(O(),N("div",bx,[(O(!0),N(Me,null,dt(n.nameOptions,A=>(O(),N("button",{key:A.id,type:"button",class:"dropdown-item",onClick:be=>a.selectNameOption(A)},G(A.label),9,wx))),128))])):ie("",!0)])]),f("div",Ex,[f("div",Cx,[t[18]||(t[18]=f("label",{for:"cpf-filter",class:"form-label text-muted small"},"Filtrar por CPF",-1)),lt(f("input",{id:"cpf-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[4]||(t[4]=A=>n.cpfSearchInput=A),onInput:t[5]||(t[5]=(...A)=>a.handleCpfInput&&a.handleCpfInput(...A)),onFocus:t[6]||(t[6]=A=>n.showCpfDropdown=n.cpfOptions.length>0)},null,544),[[Xt,n.cpfSearchInput]]),n.showCpfDropdown&&n.cpfOptions.length>0?(O(),N("div",Dx,[(O(!0),N(Me,null,dt(n.cpfOptions,A=>(O(),N("button",{key:A.id,type:"button",class:"dropdown-item",onClick:be=>a.selectCpfOption(A)},G(A.label),9,xx))),128))])):ie("",!0)])]),f("div",Ox,[f("div",Sx,[t[19]||(t[19]=f("label",{for:"email-filter",class:"form-label text-muted small"},"Filtrar por E-mail",-1)),lt(f("input",{id:"email-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[7]||(t[7]=A=>n.emailSearchInput=A),onInput:t[8]||(t[8]=(...A)=>a.handleEmailInput&&a.handleEmailInput(...A)),onFocus:t[9]||(t[9]=A=>n.showEmailDropdown=n.emailOptions.length>0)},null,544),[[Xt,n.emailSearchInput]]),n.showEmailDropdown&&n.emailOptions.length>0?(O(),N("div",Tx,[(O(!0),N(Me,null,dt(n.emailOptions,A=>(O(),N("button",{key:A.id,type:"button",class:"dropdown-item",onClick:be=>a.selectEmailOption(A)},G(A.label),9,Nx))),128))])):ie("",!0)])])])]),k(_,null,{default:Ne(()=>[(O(!0),N(Me,null,dt(n.filteredUsers,A=>(O(),Rt(p,{key:A.id,onRemove:be=>a.removeFilter(A.id||A.value)},{default:Ne(()=>[nt(G(A.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1}),n.error?(O(),N("div",Ax,[t[20]||(t[20]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),nt(" "+G(n.error),1)])):ie("",!0),f("div",Ix,[k(V,{headers:n.tableHeaders,items:n.enrolments,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"header-select":Ne(()=>[f("div",Mx,[f("input",{type:"checkbox",checked:a.allSelected,indeterminate:a.someSelected&&!a.allSelected,onChange:t[10]||(t[10]=(...A)=>a.toggleSelectAll&&a.toggleSelectAll(...A)),class:"custom-checkbox"},null,40,Px)])]),"item-select":Ne(({item:A})=>[f("div",kx,[f("input",{type:"checkbox",checked:a.isSelected(A.id),onChange:be=>a.toggleSelectUser(A.id),class:"custom-checkbox"},null,40,Vx)])]),"item-fullName":Ne(({item:A})=>[f("a",{class:"user-name-container",href:`/user/view.php?id=${A.id}`,title:"Ver perfil de "+A.fullName},[k(w,{"full-name":A.fullName,size:36},null,8,["full-name"]),f("span",Lx,G(A.fullName),1)],8,Rx)]),"item-email":Ne(({item:A})=>[nt(G(A.email),1)]),"item-cpf":Ne(({item:A})=>[nt(G(A.cpf),1)]),"item-roles":Ne(({item:A})=>[k(D,{userId:A.id,offeruserenrolid:A.offeruserenrolid,currentRole:A.roles,offerclassid:n.offerclassid,onSuccess:a.handleRoleUpdateSuccess,onError:a.handleRoleUpdateError,onReloadTable:a.reloadTable},null,8,["userId","offeruserenrolid","currentRole","offerclassid","onSuccess","onError","onReloadTable"])]),"item-groups":Ne(({item:A})=>[nt(G(A.groups),1)]),"item-startDate":Ne(({item:A})=>[nt(G(A.startDate),1)]),"item-endDate":Ne(({item:A})=>[nt(G(A.endDate),1)]),"item-deadline":Ne(({item:A})=>[nt(G(A.deadline),1)]),"item-progress":Ne(({item:A})=>[f("div",Ux,[f("div",{class:"progress-bar",style:us({width:A.progress})},null,4),f("span",Fx,G(A.progress),1)])]),"item-situation":Ne(({item:A})=>[nt(G(A.situationName),1)]),"item-grade":Ne(({item:A})=>[nt(G(A.grade),1)]),"item-status":Ne(({item:A})=>[f("div",Bx,[f("span",{class:he(["status-tag badge",A.status===0?"badge-success":"badge-danger"])},G(A.statusName),3),f("div",$x,[f("button",{class:"btn-information",onClick:be=>a.showEnrollmentDetails(A),title:"Informações da matrícula"},t[21]||(t[21]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"10"}),f("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),f("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})],-1)]),8,jx),f("button",{class:"btn-settings",onClick:be=>a.editUser(A),title:"Editar matrícula"},t[22]||(t[22]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"3"}),f("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})],-1)]),8,Hx)])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),k(F,{"current-page":n.currentPage,"onUpdate:currentPage":t[11]||(t[11]=A=>n.currentPage=A),"per-page":n.perPage,"onUpdate:perPage":t[12]||(t[12]=A=>n.perPage=A),total:n.totalEnrolments,loading:n.loading},null,8,["current-page","per-page","total","loading"]),f("div",qx,[f("div",zx,[t[24]||(t[24]=f("label",{for:"bulk-actions"},"Com usuários selecionados...",-1)),lt(f("select",{id:"bulk-actions",class:"form-control bulk-select","onUpdate:modelValue":t[13]||(t[13]=A=>n.selectedBulkAction=A),onChange:t[14]||(t[14]=(...A)=>a.handleBulkAction&&a.handleBulkAction(...A))},t[23]||(t[23]=[oy('<option value="" data-v-37c5bf85>Escolher...</option><optgroup label="Comunicação" data-v-37c5bf85><option value="message" data-v-37c5bf85>Enviar uma mensagem</option><option value="note" data-v-37c5bf85>Escrever uma nova anotação</option></optgroup><optgroup label="Baixar dados da tabela como:" data-v-37c5bf85><option value="download_csv" data-v-37c5bf85> Valores separados por vírgula (.csv) </option><option value="download_xlsx" data-v-37c5bf85>Microsoft excel (.xlsx)</option><option value="download_html" data-v-37c5bf85>Tabela HTML</option><option value="download_json" data-v-37c5bf85> JavaScript Object Notation (.json) </option><option value="download_ods" data-v-37c5bf85>OpenDocument (.ods)</option><option value="download_pdf" data-v-37c5bf85> Formato de documento portável (.pdf) </option></optgroup><optgroup label="Inscrições" data-v-37c5bf85><option value="edit_enrolment" data-v-37c5bf85> Editar matrículas de usuários selecionados </option><option value="delete_enrolment" data-v-37c5bf85> Excluir matrículas de usuários selecionados </option></optgroup>',4)]),544),[[Yl,n.selectedBulkAction]])])]),!n.classDetails||((Ae=n.classDetails)==null?void 0:Ae.operational_cycle)!==2?(O(),N("div",Wx,[k(m,{variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])])):ie("",!0),k(te,{show:n.showEnrollmentModal,user:n.selectedUser,"course-name":((ae=n.classDetails)==null?void 0:ae.course_fullname)||"",onClose:a.closeEnrollmentModal},null,8,["show","user","course-name","onClose"]),k(I,{show:n.showEnrolmentModal,offerclassid:n.offerclassid,roles:n.roleOptions,onClose:a.closeEnrolmentModal,onSuccess:a.handleEnrolmentSuccess},null,8,["show","offerclassid","roles","onClose","onSuccess"]),k(se,{show:n.showEditEnrollmentModal,user:n.selectedUser,offerclassid:n.offerclassid,onClose:a.closeEditEnrollmentModal,onSuccess:a.handleEditEnrollmentSuccess,onError:a.handleEditEnrollmentError},null,8,["show","user","offerclassid","onClose","onSuccess","onError"]),k(K,{show:n.showBulkEditEnrollmentModal,users:n.selectedUsers.map(A=>n.enrolments.find(be=>be.id===A)).filter(Boolean),offerclassid:n.offerclassid,onClose:t[15]||(t[15]=A=>this.showBulkEditEnrollmentModal=!1),onSuccess:a.handleBulkEditEnrollmentSuccess,onError:a.handleBulkEditEnrollmentError},null,8,["show","users","offerclassid","onSuccess","onError"]),k(ye,{show:n.showBulkDeleteEnrollmentModal,users:n.selectedUsers.map(A=>n.enrolments.find(be=>be.id===A)).filter(Boolean),offerclassid:n.offerclassid,onClose:t[16]||(t[16]=A=>n.showBulkDeleteEnrollmentModal=!1),onConfirm:a.confirmeBulkDeleteEnrollment,onError:a.handleBulkDeleteEnrollmentError},null,8,["show","users","offerclassid","onConfirm","onError"]),k(Z,{"is-loading":n.loading},null,8,["is-loading"]),k(fe,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const Kx=He(fx,[["render",Gx],["__scopeId","data-v-37c5bf85"]]),BR="",Yx={name:"CollapsibleTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},expandable:{type:Boolean,default:!1}},data(){return{expandedRows:[]}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})},toggleExpand(e){const t=this.expandedRows.indexOf(e);t===-1?this.expandedRows.push(e):this.expandedRows.splice(t,1)}}},Qx={class:"table-responsive"},Zx={class:"table"},Jx={key:0,class:"expand-column"},Xx=["onClick","data-value"],eO={key:0,class:"sort-icon"},tO={key:0},sO={key:0,class:"expand-column"},rO=["onClick","title"],nO=["colspan"],oO={class:"expanded-content"},iO={key:1},aO=["colspan"];function lO(e,t,s,i,n,a){return O(),N("div",Qx,[f("table",Zx,[f("thead",null,[f("tr",null,[s.expandable?(O(),N("th",Jx)):ie("",!0),(O(!0),N(Me,null,dt(s.headers,u=>(O(),N("th",{key:u.value,onClick:c=>u.sortable?a.handleSort(u.value):null,class:he({sortable:u.sortable}),"data-value":u.value},[nt(G(u.text)+" ",1),u.sortable?(O(),N("span",eO,[f("i",{class:he(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)])):ie("",!0)],10,Xx))),128))])]),s.items.length>0?(O(),N("tbody",tO,[(O(!0),N(Me,null,dt(s.items,(u,c)=>(O(),N(Me,{key:u.id},[f("tr",{class:he({expanded:n.expandedRows.includes(u.id)})},[s.expandable?(O(),N("td",sO,[f("button",{class:"btn-expand",onClick:h=>a.toggleExpand(u.id),title:n.expandedRows.includes(u.id)?"Recolher":"Expandir"},[f("div",{class:he(["icon-container",{"is-expanded":n.expandedRows.includes(u.id)}])},t[0]||(t[0]=[f("svg",{width:"16",height:"16",viewBox:"0 0 24 24",class:"expand-icon"},[f("rect",{x:"5",y:"11",width:"14",height:"2",fill:"var(--primary)"}),f("rect",{x:"11",y:"5",width:"2",height:"14",fill:"var(--primary)",class:"vertical-line"})],-1)]),2)],8,rO)])):ie("",!0),(O(!0),N(Me,null,dt(s.headers,h=>(O(),N("td",{key:`${u.id}-${h.value}`},[Vt(e.$slots,"item-"+h.value,{item:u},()=>[nt(G(u[h.value]),1)],!0)]))),128))],2),s.expandable?(O(),N("tr",{key:0,class:he(["expanded-row",{"is-visible":n.expandedRows.includes(u.id)}])},[f("td",{colspan:s.headers.length+1},[f("div",oO,[Vt(e.$slots,"expanded-content",{item:u},void 0,!0)])],8,nO)],2)):ie("",!0)],64))),128))])):(O(),N("tbody",iO,[f("tr",null,[f("td",{colspan:s.headers.length+(s.expandable?1:0)},[Vt(e.$slots,"empty-state",{},()=>[t[1]||(t[1]=f("div",{class:"empty-state"},[f("span",null,"Não existem registros")],-1))],!0)],8,aO)])]))])])}const uO=He(Yx,[["render",lO],["__scopeId","data-v-049f598f"]]),$R="",cO={name:"TextEditor",props:{modelValue:{type:String,default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite o conteúdo aqui..."},rows:{type:Number,default:5},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],data(){return{showHtmlSource:!1,htmlContent:this.modelValue}},mounted(){this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.modelValue)},watch:{modelValue:{handler(e){this.showHtmlSource?this.htmlContent=e:this.$refs.editableContent&&this.$refs.editableContent.textContent!==e&&(this.$refs.editableContent.textContent=e)},immediate:!0}},methods:{applyFormat(e,t=null){this.showHtmlSource||(document.execCommand(e,!1,t),this.updateContent())},insertLink(){if(this.showHtmlSource)return;const e=prompt("Digite a URL do link:","http://");e&&this.applyFormat("createLink",e)},insertImage(){if(this.showHtmlSource)return;const e=prompt("Digite a URL da imagem:","http://");e&&this.applyFormat("insertImage",e)},toggleHtmlView(){this.showHtmlSource?this.$nextTick(()=>{this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.htmlContent)}):this.htmlContent=this.$refs.editableContent.textContent,this.showHtmlSource=!this.showHtmlSource},updateContent(){if(!this.showHtmlSource&&this.$refs.editableContent){const e=this.$refs.editableContent.textContent;this.$emit("update:modelValue",e)}},updateHtmlContent(){this.showHtmlSource&&this.$emit("update:modelValue",this.htmlContent)}}},dO={class:"text-editor-container"},fO={class:"editor-toolbar"},hO={class:"toolbar-group"},pO=["disabled"],mO=["disabled"],gO=["disabled"],_O=["disabled"],vO={class:"toolbar-group"},yO=["disabled"],bO=["disabled"],wO=["contenteditable"],EO=["rows","placeholder","disabled"];function CO(e,t,s,i,n,a){return O(),N("div",dO,[s.label?(O(),N("label",{key:0,class:he(["filter-label",{disabled:s.disabled}])},G(s.label),3)):ie("",!0),f("div",{class:he(["editor-container",{disabled:s.disabled}])},[f("div",fO,[f("div",hO,[f("button",{class:"btn-editor",onClick:t[0]||(t[0]=u=>!s.disabled&&a.applyFormat("bold")),title:"Negrito",disabled:s.disabled},t[10]||(t[10]=[f("i",{class:"fas fa-bold"},null,-1)]),8,pO),f("button",{class:"btn-editor",onClick:t[1]||(t[1]=u=>!s.disabled&&a.applyFormat("italic")),title:"Itálico",disabled:s.disabled},t[11]||(t[11]=[f("i",{class:"fas fa-italic"},null,-1)]),8,mO),f("button",{class:"btn-editor",onClick:t[2]||(t[2]=u=>!s.disabled&&a.applyFormat("underline")),title:"Sublinhado",disabled:s.disabled},t[12]||(t[12]=[f("i",{class:"fas fa-underline"},null,-1)]),8,gO),f("button",{class:"btn-editor",onClick:t[3]||(t[3]=u=>!s.disabled&&a.applyFormat("strikethrough")),title:"Tachado",disabled:s.disabled},t[13]||(t[13]=[f("i",{class:"fas fa-strikethrough"},null,-1)]),8,_O)]),t[16]||(t[16]=f("div",{class:"toolbar-divider"},null,-1)),f("div",vO,[f("button",{class:"btn-editor",onClick:t[4]||(t[4]=u=>!s.disabled&&a.applyFormat("insertUnorderedList")),title:"Lista não ordenada",disabled:s.disabled},t[14]||(t[14]=[f("i",{class:"fas fa-list-ul"},null,-1)]),8,yO),f("button",{class:"btn-editor",onClick:t[5]||(t[5]=u=>!s.disabled&&a.applyFormat("insertOrderedList")),title:"Lista ordenada",disabled:s.disabled},t[15]||(t[15]=[f("i",{class:"fas fa-list-ol"},null,-1)]),8,bO)])]),n.showHtmlSource?lt((O(),N("textarea",{key:1,"onUpdate:modelValue":t[8]||(t[8]=u=>n.htmlContent=u),class:"editor-textarea",rows:s.rows,placeholder:s.placeholder,onInput:t[9]||(t[9]=(...u)=>a.updateHtmlContent&&a.updateHtmlContent(...u)),disabled:s.disabled},null,40,EO)),[[Xt,n.htmlContent]]):(O(),N("div",{key:0,class:"editor-content",contenteditable:!s.disabled,onInput:t[6]||(t[6]=(...u)=>a.updateContent&&a.updateContent(...u)),onKeyup:t[7]||(t[7]=(...u)=>a.updateContent&&a.updateContent(...u)),ref:"editableContent"},null,40,wO))],2)])}const lp=He(cO,[["render",CO],["__scopeId","data-v-672cb06c"]]),jR="",HR="",DO={name:"AddCourseModal",components:{CustomInput:Uo,CustomButton:Fn,CustomTable:hn,Pagination:pn,Autocomplete:$o,FilterTag:Bo},props:{modelValue:{type:Boolean,required:!0},offerId:{type:Number,required:!0}},emits:["update:modelValue","confirm"],data(){return{selectedCategory:null,selectedCategoryObject:null,selectedCourse:null,categoryOptions:[],courseOptions:[],currentPage:1,perPage:5,sortBy:"name",sortDesc:!1,loadingCategories:!1,loadingCourses:!1,loadingCurrentCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingMoreCourses:!1,tableHeaders:[{text:"CURSO",value:"name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],selectedCoursesPreview:[],existingCourses:[]}},computed:{filteredCourses(){const e=[...this.selectedCoursesPreview].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)},totalPages(){return Math.ceil(this.selectedCoursesPreview.length/this.perPage)},courseNoResultsText(){return this.loadingCourses?"Buscando cursos...":this.loadingMoreCourses?"Carregando mais cursos...":this.selectedCategory?this.courseOptions.length===0&&this.selectedCategory?"Todos os cursos já foram adicionados":"Nenhum curso encontrado":"Selecione uma categoria primeiro"}},watch:{modelValue(e){e?(this.loadCurrentCourses(),this.loadAllCategories()):(this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.categoryOptions=[],this.courseOptions=[],this.selectedCoursesPreview=[])},selectedCategory(e){e||(this.courseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null,this.loadCurrentCourses())},courseOptions(e){e.length<10&&this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&this.$nextTick(()=>{this.loadMoreCourses(),console.log("Carregando mais cursos... via watch")})}},methods:{closeModal(){this.$emit("update:modelValue",!1),this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.selectedCoursesPreview=[]},async confirm(){try{if(this.selectedCoursesPreview.length===0){console.warn("Nenhum curso selecionado para adicionar"),this.closeModal();return}const e=this.selectedCoursesPreview.map(t=>t.id);await n0(this.offerId,e),this.$emit("confirm",this.selectedCoursesPreview),this.closeModal()}catch(e){console.error("Erro ao salvar cursos:",e)}},async loadCurrentCourses(){console.log("loadCurrentCourses this.offerId:",this.offerId);try{this.loadingCurrentCourses=!0;const e=await Xi(this.offerId);console.log("loadCurrentCourses response:",e),e&&e.data&&(Array.isArray(e.data)?this.existingCourses=e.data.map(t=>({id:t.courseid,name:t.fullname,offerCourseId:t.id})):this.existingCourses=[])}catch(e){console.error("Erro ao carregar cursos da oferta:",e),this.existingCourses=[]}finally{this.loadingCurrentCourses=!1}},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Lo("");e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar todas as categorias:",e),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.courseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCoursesForCategory(e.value)},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e)return;const n=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.courseOptions=[])):this.loadingMoreCourses=!0;const a=await sp(this.offerId,n,i,t,this.coursesPerPage);let u=null,c=[];try{if(Array.isArray(a)&&a.length>0?a[0].error===!1&&a[0].data?a[0].data.courses?(u=a[0].data,c=u.courses||[]):Array.isArray(a[0].data)?(c=a[0].data,u={page:1,total_pages:1}):a[0].data.data&&Array.isArray(a[0].data.data)&&(c=a[0].data.data,u={page:a[0].data.page||1,total_pages:a[0].data.total_pages||1}):(c=a,u={page:1,total_pages:1}):a&&typeof a=="object"&&(a.data&&a.data.courses?(c=a.data.courses,u={page:a.data.page||1,total_pages:a.data.total_pages||1}):a.courses?(c=a.courses,u={page:a.page||1,total_pages:a.total_pages||1}):a.data&&Array.isArray(a.data)&&(c=a.data,u={page:1,total_pages:1})),c.length===0&&a&&typeof a=="object"&&!Array.isArray(a)){for(const h in a)if(Array.isArray(a[h])){c=a[h],u={page:1,total_pages:1};break}}}catch(h){console.error("Erro ao processar resposta:",h)}if(u){if(this.coursesPage=u.page||1,this.coursesTotalPages=u.total_pages||1,this.hasMoreCourses=(u.page||1)<(u.total_pages||1),c&&c.length>0){const m=c.filter(p=>!this.existingCourses.some(_=>_.id===p.id)&&!this.selectedCoursesPreview.some(_=>_.id===p.id)).map(p=>({value:p.id,label:p.fullname}));s?this.courseOptions=[...this.courseOptions,...m]:this.courseOptions=m}}else console.warn("Formato de resposta inesperado")}catch(a){console.error("Erro ao carregar cursos da categoria:",a),s||(this.courseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(console.log("Buscando cursos com termo:",e),this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.id===e.value)&&(this.selectedCoursesPreview.push({id:e.value,name:e.label}),this.courseOptions=this.courseOptions.filter(t=>t.value!==e.value),this.currentPage=1),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.id===e.id);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.currentPage>1&&this.currentPage>Math.ceil(this.selectedCoursesPreview.length/this.perPage)&&(this.currentPage=Math.max(1,this.currentPage-1)),this.selectedCategory?sp(this.offerId,this.selectedCategory,"",1,this.coursesPerPage).then(i=>{let n=[];try{if(Array.isArray(i)&&i.length>0?i[0].error===!1&&i[0].data?i[0].data.courses?n=i[0].data.courses||[]:Array.isArray(i[0].data)?n=i[0].data:i[0].data.data&&Array.isArray(i[0].data.data)&&(n=i[0].data.data):n=i:i&&typeof i=="object"&&(i.data&&i.data.courses?n=i.data.courses:i.courses?n=i.courses:i.data&&Array.isArray(i.data)&&(n=i.data)),n.length===0&&i&&typeof i=="object"&&!Array.isArray(i)){for(const a in i)if(Array.isArray(i[a])){n=i[a];break}}}catch(a){console.error("Erro ao processar resposta em removeCourse:",a)}n&&n.length>0&&n.find(u=>u.id===s.id)&&this.courseOptions.push({value:s.id,label:s.name})}).catch(i=>{console.error("Erro ao verificar categoria do curso:",i)}):this.courseOptions.push({value:s.id,label:s.name})}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handlePageChange(e){this.currentPage=e},handlePerPageChange(e){this.perPage=e,this.currentPage=1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.courseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCurrentCourses()}}},xO={class:"modal-header"},OO={class:"modal-body"},SO={class:"search-section"},TO={class:"search-group"},NO={class:"search-group"},AO={class:"table-container"},IO={key:0,class:"empty-preview-message"},MO={class:"action-buttons"},PO=["onClick"],kO={class:"modal-footer"};function VO(e,t,s,i,n,a){const u=ee("Autocomplete"),c=ee("CustomTable"),h=ee("Pagination"),m=ee("CustomButton");return s.modelValue?(O(),N("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...p)=>a.closeModal&&a.closeModal(...p))},[f("div",{class:"modal-content",onClick:t[5]||(t[5]=Lt(()=>{},["stop"]))},[f("div",xO,[t[8]||(t[8]=f("h2",null,"Adicionar curso",-1)),f("button",{class:"close-button",onClick:t[0]||(t[0]=(...p)=>a.closeModal&&a.closeModal(...p))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",OO,[t[11]||(t[11]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",SO,[f("div",TO,[k(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedCategory=p),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),f("div",NO,[k(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedCourse=p),items:n.courseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"keep-open-on-select":!0,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",AO,[n.selectedCoursesPreview.length===0?(O(),N("div",IO,t[9]||(t[9]=[f("p",null,"Selecione cursos acima para adicioná-los à oferta",-1)]))):(O(),Rt(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Ne(({item:p})=>[f("div",MO,[f("button",{class:"btn-action btn-delete",onClick:_=>a.removeCourse(p),title:"Remover da lista"},t[10]||(t[10]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,PO)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(O(),Rt(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":[t[3]||(t[3]=p=>n.currentPage=p),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":[t[4]||(t[4]=p=>n.perPage=p),a.handlePerPageChange],total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total","onUpdate:currentPage","onUpdate:perPage"])):ie("",!0)]),f("div",kO,[k(m,{variant:"primary",label:"Confirmar",disabled:n.selectedCoursesPreview.length===0,onClick:a.confirm},null,8,["disabled","onClick"]),k(m,{variant:"secondary",label:"Cancelar",onClick:a.closeModal},null,8,["onClick"])])])])):ie("",!0)}const RO=He(DO,[["render",VO],["__scopeId","data-v-0a88ee2a"]]),qR="",zR="",LO={name:"DuplicateClassModal",components:{Autocomplete:$o,CustomTable:hn,Pagination:pn},props:{show:{type:Boolean,default:!1},turma:{type:Object,default:null},parentCourse:{type:Object,default:null},offerId:{type:[Number,String],default:null}},emits:["close","success","loading","error"],data(){return{selectedCategory:null,selectedCategoryObject:null,categoryOptions:[],loadingCategories:!1,selectedCourse:null,targetCourseOptions:[],selectedCoursesPreview:[],loadingCourses:!1,loadingMoreCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,currentPage:1,perPage:5,sortBy:"label",sortDesc:!1,tableHeaders:[{text:"CURSO",value:"label",sortable:!0},{text:"CATEGORIA",value:"category_name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],duplicatingCourses:!1,duplicatedCount:0,totalToDuplicate:0,existingCourses:[]}},computed:{courseNoResultsText(){return this.selectedCategory?this.loadingCourses||this.loadingMoreCourses?"Carregando cursos...":this.targetCourseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado":"Selecione uma categoria primeiro"},filteredCourses(){const e=(this.currentPage-1)*this.perPage,t=e+this.perPage;return[...this.selectedCoursesPreview].sort((i,n)=>{const a=i[this.sortBy],u=n[this.sortBy];return a<u?this.sortDesc?1:-1:a>u?this.sortDesc?-1:1:0}).slice(e,t)}},watch:{show(e){e&&this.turma&&this.parentCourse?(this.resetForm(),this.$nextTick(()=>{this.loadAllCategories()})):this.resetForm()},turma(){this.show&&(this.resetForm(),this.loadAllCategories())},parentCourse(){this.show&&(this.resetForm(),this.loadAllCategories())},selectedCategory(e){e?this.loadCoursesForCategory(e):(this.targetCourseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null)}},methods:{resetForm(){this.selectedCategory=null,this.selectedCategoryObject=null,this.categoryOptions=[],this.loadingCategories=!1,this.selectedCourse=null,this.targetCourseOptions=[],this.selectedCoursesPreview=[],this.loadingCourses=!1,this.loadingMoreCourses=!1,this.coursesPage=1,this.coursesPerPage=20,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.currentPage=1,this.duplicatingCourses=!1,this.duplicatedCount=0,this.totalToDuplicate=0,this.existingCourses=[]},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Lo("",this.offerId);e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar categorias:",e),this.$emit("error","Erro ao carregar categorias. Por favor, tente novamente."),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.targetCourseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.targetCourseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e||!this.turma)return;const n=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.targetCourseOptions=[])):this.loadingMoreCourses=!0;const a=await h0(this.turma.id);let u=[];a&&Array.isArray(a)&&(u=a);const c=this.parentCourse.offerCourseId||this.parentCourse.id;u=u.filter(m=>{const p=m.categoryid,_=String(p)===String(n),w=m.offercourseid||m.id,D=String(w)!==String(c),V=!i||m.name&&m.name.toLowerCase().includes(i.toLowerCase())||m.fullname&&m.fullname.toLowerCase().includes(i.toLowerCase());return _&&D&&V}),u=u.filter(m=>{const p=m.offercourseid||m.id;return!this.selectedCoursesPreview.some(_=>String(_.value)===String(p))});const h=u.map(m=>{let p=m.offercourseid||m.id;return p==null?null:{value:p,label:m.name||m.fullname||m.coursename||`Curso ${p}`,categoryid:m.categoryid,category_name:m.category_name}}).filter(m=>m!==null);s?this.targetCourseOptions=[...this.targetCourseOptions,...h]:this.targetCourseOptions=h,this.hasMoreCourses=h.length>=this.coursesPerPage,t>this.coursesPage&&(this.coursesPage=t)}catch(a){console.error("Erro ao carregar cursos da categoria:",a),this.$emit("error","Erro ao carregar cursos. Por favor, tente novamente."),s||(this.targetCourseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.value===e.value)&&(this.selectedCoursesPreview.push({value:e.value,label:e.label,categoryid:e.categoryid,category_name:e.category_name}),this.targetCourseOptions=this.targetCourseOptions.filter(t=>t.value!==e.value)),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.value===e.value);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.targetCourseOptions.push(s)}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},async handleConfirm(){if(!(!this.turma||this.selectedCoursesPreview.length===0))try{this.$emit("loading",!0);const e=this.turma.nome,t=parseInt(this.turma.id,10);if(isNaN(t))throw new Error("ID da turma inválido");this.duplicatingCourses=!0,this.totalToDuplicate=this.selectedCoursesPreview.length,this.duplicatedCount=0;const s=[];for(const i of this.selectedCoursesPreview){const n=parseInt(i.value,10);if(isNaN(n)){console.error(`ID inválido para o curso ${i.label}`);continue}try{this.$emit("loading",!0,`Duplicando para ${i.label} (${this.duplicatedCount+1}/${this.totalToDuplicate})`);const a=await f0(t,n);s.push({turmaNome:e,targetCourseName:i.label,turmaId:t,targetCourseId:n,result:a}),this.duplicatedCount++}catch(a){console.error(`Erro ao duplicar para o curso ${i.label}:`,a),this.$emit("error",`Erro ao duplicar para o curso ${i.label}: ${a.message}`)}}if(this.duplicatingCourses=!1,s.length>0)this.$emit("success",{turmaNome:e,duplicatedCount:s.length,totalSelected:this.totalToDuplicate,results:s}),this.resetForm(),this.$emit("close");else throw new Error("Nenhuma turma foi duplicada com sucesso.")}catch(e){this.$emit("error",e.message||"Erro ao duplicar turmas.")}finally{this.duplicatingCourses=!1,this.$emit("loading",!1)}}}},UO={class:"modal-header"},FO={class:"modal-title"},BO={class:"modal-body"},$O={class:"search-section"},jO={class:"search-group"},HO={class:"search-group"},qO={class:"table-container"},zO={key:0,class:"empty-preview-message"},WO={class:"action-buttons"},GO=["onClick"],KO={class:"modal-footer"},YO=["disabled"];function QO(e,t,s,i,n,a){var m;const u=ee("Autocomplete"),c=ee("CustomTable"),h=ee("Pagination");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[8]||(t[8]=p=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[7]||(t[7]=Lt(()=>{},["stop"]))},[f("div",UO,[f("h3",FO,'Duplicar Turma "'+G((m=s.turma)==null?void 0:m.nome)+'"',1),f("button",{class:"close-button",onClick:t[0]||(t[0]=p=>e.$emit("close"))},t[9]||(t[9]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",BO,[t[12]||(t[12]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",$O,[f("div",jO,[k(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedCategory=p),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),f("div",HO,[k(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedCourse=p),items:n.targetCourseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",qO,[n.selectedCoursesPreview.length===0?(O(),N("div",zO,t[10]||(t[10]=[f("p",null,"Selecione cursos acima para duplicar a turma",-1)]))):(O(),Rt(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Ne(({item:p})=>[f("div",WO,[f("button",{class:"btn-action btn-delete",onClick:_=>a.removeCourse(p),title:"Remover da lista"},t[11]||(t[11]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,GO)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(O(),Rt(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=p=>n.currentPage=p),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=p=>n.perPage=p),total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total"])):ie("",!0)]),f("div",KO,[f("button",{class:"btn-primary",onClick:t[5]||(t[5]=(...p)=>a.handleConfirm&&a.handleConfirm(...p)),disabled:n.selectedCoursesPreview.length===0}," Duplicar ",8,YO),f("button",{class:"btn-secondary",onClick:t[6]||(t[6]=p=>e.$emit("close"))},"Cancelar")])])])):ie("",!0)}const ZO=He(LO,[["render",QO],["__scopeId","data-v-7ebf3397"]]),WR="",JO={name:"HelpIcon",props:{title:{type:String,default:"Ajuda"},text:{type:String,required:!0},postion:{type:String,default:"right"}},computed:{content(){return`<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`}}},XO=["data-content","aria-label"],eS=["title","aria-label"];function tS(e,t,s,i,n,a){return O(),N("a",{class:"btn btn-link p-0","data-container":"body","data-toggle":"popover","data-placement":"auto","data-content":a.content,"data-html":"true",tabindex:"0","data-trigger":"focus","aria-label":s.title,role:"button"},[f("i",{class:"icon fa fa-question-circle text-info fa-fw",title:s.title,"aria-label":s.title,role:"img"},null,8,eS)],8,XO)}const yu=He(JO,[["render",tS]]),GR="",sS={name:"EnrolTypeModal",components:{CustomSelect:mr,HelpIcon:yu},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Tipo de Inscrição"},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Continuar"},cancelButtonText:{type:String,default:"Cancelar"},offercourseid:{type:[Number,String],required:!0},offerid:{type:[Number,String],required:!1,default:"0"}},emits:["close","confirm"],data(){return{selectedEnrolType:"",enrolmentMethods:[],loading:!1}},mounted(){this.$nextTick(()=>{this.initializePopovers()})},watch:{show(e){e&&(this.loadEnrolmentMethods(),this.$nextTick(()=>{this.initializePopovers()}))}},methods:{async loadEnrolmentMethods(){this.loading=!0;const e=await op(!0);e&&Array.isArray(e.data)&&(this.enrolmentMethods=e.data.map(t=>({value:t.enrol,label:t.name}))),this.loading=!1},handleConfirm(){this.selectedEnrolType&&this.$emit("confirm",{enrolType:this.selectedEnrolType,offercourseid:this.offercourseid,offerid:this.offerid})},initializePopovers(){typeof $<"u"&&typeof $.fn.popover<"u"?$('[data-toggle="popover"]').popover({container:"body",trigger:"focus",html:!0}):console.warn("jQuery ou Bootstrap não estão disponíveis para inicializar popovers")}}},rS={class:"modal-header"},nS={class:"modal-title"},oS={class:"modal-body"},iS={class:"enrol-type-modal"},aS={class:"form-group mb-3"},lS={class:"label-with-help"},uS={class:"limited-width-input",style:{"max-width":"280px"}},cS={class:"modal-footer"},dS={class:"footer-buttons"},fS=["disabled"];function hS(e,t,s,i,n,a){const u=ee("HelpIcon"),c=ee("CustomSelect");return s.show?(O(),N("div",{key:0,class:"modal-backdrop",onClick:t[5]||(t[5]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:he(["modal-container",[`modal-${s.size}`]]),onClick:t[4]||(t[4]=Lt(()=>{},["stop"]))},[f("div",rS,[f("h3",nS,G(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[6]||(t[6]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",oS,[f("div",iS,[t[9]||(t[9]=f("p",{class:"modal-description"}," Selecione o tipo de inscrição para esta turma. Esta configuração não poderá ser alterada posteriormente. ",-1)),f("div",aS,[f("div",lS,[t[7]||(t[7]=f("label",{class:"form-label"},"Método de inscrição",-1)),t[8]||(t[8]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw m-0",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(u,{title:"Ajuda com método de inscrição",text:`Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.
Inscrição manual em ofertas: Nesta opção o usuário será matriculado manualmente (em lote ou individualmente) por um perfil autorizado, através da página 'Usuários matriculados' contida em cada turma.
Autoisncrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no curso para fazer sua matrícula em uma turma. Ou seja, ele não será inscrito automaticamente como no Tipo de inscrição 'Inscrição por público-alvo em ofertas'.`})]),f("div",uS,[k(c,{modelValue:n.selectedEnrolType,"onUpdate:modelValue":t[1]||(t[1]=h=>n.selectedEnrolType=h),options:[{value:"",label:"Selecione um método..."},...n.enrolmentMethods],width:280,required:""},null,8,["modelValue","options"])])])])]),f("div",cS,[t[10]||(t[10]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1)),f("div",dS,[f("button",{class:"btn btn-primary",onClick:t[2]||(t[2]=(...h)=>a.handleConfirm&&a.handleConfirm(...h)),disabled:!n.selectedEnrolType},G(s.confirmButtonText),9,fS),f("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=h=>e.$emit("close"))},G(s.cancelButtonText),1)])])],2)])):ie("",!0)}const pS=He(sS,[["render",hS],["__scopeId","data-v-4b89966a"]]),mS="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGQ9Ik03IDE0cy0xIDAtMS0xIDEtNCA1LTQgNSAzIDUgNC0xIDEtMSAxSDd6bTQtNmEzIDMgMCAxIDAgMC02IDMgMyAwIDAgMCAwIDZ6IiBmaWxsPSIjZmZmIi8+CiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTUuMjE2IDE0QTIuMjM4IDIuMjM4IDAgMCAxIDUgMTNjMC0xLjM1NS42OC0yLjc1IDEuOTM2LTMuNzJBNi4zMjUgNi4zMjUgMCAwIDAgNSA5Yy00IDAtNSAzLTUgNHMxIDEgMSAxaDQuMjE2eiIgZmlsbD0iI2ZmZiIvPgogICAgPHBhdGggZD0iTTQuNSA4YTIuNSAyLjUgMCAxIDAgMC01IDIuNSAyLjUgMCAwIDAgMCA1eiIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K",gS="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOCAyYS41LjUgMCAwIDEgLjUuNXY1aDVhLjUuNSAwIDAgMSAwIDFoLTV2NWEuNS41IDAgMCAxLTEgMHYtNWgtNWEuNS41IDAgMSAxIDAtMWg1di01QS41LjUgMCAwIDEgOCAyeiIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K",KR="",_S={name:"NewOfferView",components:{CustomTable:hn,CustomSelect:mr,CustomInput:Uo,CustomButton:Fn,Pagination:pn,CollapsibleTable:uO,PageHeader:ra,BackButton:vu,Autocomplete:$o,TextEditor:lp,CustomCheckbox:ea,FilterRow:ta,FilterGroup:sa,FilterTag:Bo,FilterTags:na,AddCourseModal:RO,ConfirmationModal:gu,Toast:Fo,HelpIcon:yu,DuplicateClassModal:ZO,EnrolTypeModal:pS,LFLoading:mu},setup(){const e=Zi(),t=Zh();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},data(){return{icons:{users:mS,plus:gS},showAddCourseModalVisible:!1,showCourseStatusModal:!1,selectedCourse:null,showWarning:!0,isEditing:!1,offerId:null,showDeleteCourseModal:!1,courseToDelete:null,showDeleteClassModal:!1,classToDelete:null,classParentCourse:null,showClassStatusModal:!1,showDuplicateClassModal:!1,showEnrolTypeModal:!1,selectedClass:null,classToDuplicate:null,classToDuplicateParentCourse:null,selectedCourseForClass:null,loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,offer:{id:null,name:"",offerType:"",description:"",status:0},formErrors:{name:{hasError:!1,message:"Nome da oferta é obrigatório"},audiences:{hasError:!1,message:"Selecione pelo menos um público-alvo"}},offerTypeOptions:[],typeOptionsEnabled:!1,audienceTypeOptions:[],allAudiences:[],selectedAudiences:[],categoryOptions:[],courseOptions:[],selectedCategory:null,selectedCourse:null,inputFilters:{course:"",category:"",onlyActive:!1},appliedFilters:{course:"",category:"",onlyActive:!1},courseTableHeaders:[{text:"NOME DO CURSO",value:"name",sortable:!0},{text:"CATEGORIA",value:"category",sortable:!0},{text:"NÚMERO DE TURMAS",value:"turmasCount",sortable:!0},{text:"STATUS DO CURSO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],selectedCourses:[],currentPage:1,perPage:5,totalItems:0,sortBy:"id",sortDesc:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingCourses:!1,loadingMoreCourses:!1,courseNoResultsText:"Nenhum curso encontrado"}},async mounted(){await this.loadInitialData(),this.offerId?(this.isEditing=!0,await this.loadOfferData(),await this.loadCategoryOptions(),await this.loadCourseOptions()):this.isEditing=!1,await this.loadTypeOptions(),await this.loadAllAudiences(),this.showWarning=!0},computed:{canManageCourses(){return this.isEditing&&this.offerId!==null},hasActiveFilters(){return this.appliedFilters.course}},methods:{getEnrolTypeLabel(e){if(!e)return"-";if(typeof e!="string")return String(e);const t=e.toLowerCase(),s={offer_manual:"Manual",offer_self:"Autoinscrição",manual:"Manual",self:"Autoinscrição",guest:"Visitante",cohort:"Coorte",database:"Base de dados",flatfile:"Arquivo plano",ldap:"LDAP",lti:"LTI",meta:"Meta-curso",mnet:"MNet",paypal:"PayPal",shibboleth:"Shibboleth"};if(s[e])return s[e];for(const[i,n]of Object.entries(s))if(i.toLowerCase()===t)return n;if(t==="offer_manual")return"Manual";if(t==="offer_self")return"Autoinscrição";for(const[i,n]of Object.entries(s))if(t.includes(i.toLowerCase()))return n;return e},formatDate(e){return e?new Date(e*1e3).toLocaleDateString("pt-BR"):"-"},async loadInitialData(){},async loadOfferData(){if(this.offerId){this.loading=!0;try{const e=await Xh(this.offerId);e&&e.data&&(this.offer={id:e.data.id,name:e.data.name,offerType:e.data.typeid,description:e.data.description||"",status:e.data.status||0},this.selectedAudiences=e.data.audiences?e.data.audiences.map(t=>({value:t.id,label:t.name})):[],await this.loadCourses())}catch{this.showErrorMessage("Erro ao carregar dados da oferta.")}finally{this.loading=!1}}},async loadTypeOptions(){try{const e=await fu();if(e&&e.data){const{enabled:t,types:s}=e.data;this.typeOptionsEnabled=!!t,t&&Array.isArray(s)?this.offerTypeOptions=s.map(i=>({value:i,label:i.charAt(0).toUpperCase()+i.slice(1)})):(this.offerTypeOptions=[],this.typeOptionsEnabled=!1)}else this.offerTypeOptions=[],this.typeOptionsEnabled=!1}catch{this.typeOptionsEnabled=!1}},async loadAllAudiences(){if(this.allAudiences.length>0){this.audienceTypeOptions=[...this.allAudiences];return}this.loading=!0;try{const e=await tp("");e&&e.items&&(this.allAudiences=e.items.map(t=>({value:t.id,label:t.name})),this.audienceTypeOptions=[...this.allAudiences])}catch{this.showErrorMessage("Erro ao carregar públicos-alvo.")}finally{this.loading=!1}},async applyFilters(){this.appliedFilters=JSON.parse(JSON.stringify(this.inputFilters));try{this.loading=!0,await this.loadCourses()}catch{this.showErrorMessage("Erro ao aplicar filtros. Por favor, tente novamente.")}finally{this.loading=!1}this.currentPage=1},clearFilters(){this.inputFilters={course:"",category:"",onlyActive:!1},this.appliedFilters={course:"",category:"",onlyActive:!1},this.selectedCategory=null,this.selectedCourse=null,this.loadCourses(),this.loadCourseOptions()},async removeFilter(e){try{this.loading=!0,this.inputFilters[e]="",this.appliedFilters[e]="",e==="category"?(this.selectedCategory=null,await this.loadCourseOptions()):e==="course"&&(this.selectedCourse=null),this.hasActiveFilters?await this.applyFilters():await this.loadCourses()}catch{this.showErrorMessage("Erro ao remover filtro. Por favor, tente novamente.")}finally{this.loading=!1}},async loadCategoryOptions(){if(this.offerId)try{this.loading=!0;const e=await Lo("",this.offerId);e&&e.data&&Array.isArray(e.data)?this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})):this.categoryOptions=[]}catch{this.showErrorMessage("Erro ao carregar categorias.")}finally{this.loading=!1}},async loadCourseOptions(e=null,t=!0){if(this.offerId)try{if(this.loading=!0,e){await this.updateCourseOptionsByCategory(e);return}t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;const s=await Xi(this.offerId,{onlyActive:!1,page:this.coursesPage,perPage:this.coursesPerPage,sortBy:this.sortBy,sortDesc:this.sortDesc});if(s&&s.data&&s.data.courses){const{page:i,total_pages:n,courses:a}=s.data;this.coursesPage=i||this.coursesPage,this.coursesTotalPages=n||1,this.hasMoreCourses=this.coursesPage<this.coursesTotalPages;const u=a.map(c=>({value:c.id||c.courseid,label:c.fullname}));t?this.courseOptions=u:this.courseOptions=[...this.courseOptions,...u],this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else if(s&&s.data&&Array.isArray(s.data)){const i=s.data.map(n=>({value:n.id||n.courseid,label:n.fullname}));t?this.courseOptions=i:this.courseOptions=[...this.courseOptions,...i],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else t&&(this.courseOptions=[]),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível"}catch{this.showErrorMessage("Erro ao carregar cursos."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}},async loadMoreCourses(){this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&(this.coursesPage+=1,this.selectedCategory?await this.updateCourseOptionsByCategory({value:this.selectedCategory,label:this.inputFilters.category},!1):await this.loadCourseOptions(null,!1))},async handleCourseSearch(e){this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[];try{this.selectedCategory&&(this.selectedCategory=null);const t=await rp(this.offerId,e);t&&t.data&&Array.isArray(t.data)&&(this.courseOptions=t.data.map(s=>({value:s.id||s.courseid,label:s.fullname}))),this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso encontrado para a busca":"Nenhum curso encontrado"}catch{this.showErrorMessage("Erro ao buscar cursos."),this.courseOptions=[]}finally{this.loadingCourses=!1}},async updateCourseOptionsByCategory(e,t=!0){if(!(!this.offerId||!e)){this.loading=!0;try{t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;let s=e.value;if(isNaN(parseInt(s))){const n=await Lo(e.label,this.offerId);if(n&&n.data&&n.data.length>0){const a=n.data.find(u=>u.name.toLowerCase()===e.label.toLowerCase());if(a)s=a.id;else{this.showErrorMessage("Erro ao identificar a categoria selecionada."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}else{this.showErrorMessage("Erro ao buscar categorias."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}const i=await np(this.offerId,s);if(i&&i.data&&Array.isArray(i.data)){const n=i.data.map(a=>({value:a.id||a.courseid,label:a.fullname}));t?(this.courseOptions=n,this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""):this.courseOptions=[...this.courseOptions,...n],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível nesta categoria":"Nenhum curso encontrado"}else t&&(this.courseOptions=[],this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível nesta categoria"}catch{this.showErrorMessage("Erro ao carregar cursos da categoria."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}}},async handleCategorySelect(e){e&&(this.inputFilters.category=e.label,this.currentPage=1,await this.applyFilters(),await this.updateCourseOptionsByCategory(e,!0))},async handleCourseSelect(e){e&&(this.inputFilters.course=e.label,this.currentPage=1,this.selectedCategory=null,await this.applyFilters())},handleOnlyActiveChange(){this.appliedFilters.onlyActive=this.inputFilters.onlyActive,this.currentPage=1,this.loadCourses()},goBack(){this.router.push({name:"listar-ofertas"})},async searchCourses(){if(this.offerId)try{this.loading=!0;const e=await rp(this.offerId,this.appliedFilters.course);if(e&&e.data&&e.data.length>0){const t=e.data.map(i=>i.id),s=await Xi(this.offerId,{courseIds:t});s&&s.data.courses?this.selectedCourses=s.data.courses.map(i=>({id:i.id||i.courseid,offerCourseId:i.id,name:i.fullname,category:i.category_name||"-",turmasCount:Array.isArray(i.turmas)?i.turmas.length:0,status:i.status===1||i.status==="1"?"Ativo":"Inativo",can_delete:i.can_delete!==void 0?i.can_delete:!0,can_activate:i.can_activate!==void 0?i.can_activate:!0,turmas:Array.isArray(i.turmas)?i.turmas.map(n=>({id:n.id,nome:n.name,enrol_type:n.enrol_type||"-",vagas:n.max_users?n.max_users:"Ilimitado",inscritos:n.enrolled_users||0,dataInicio:n.start_date||"-",dataFim:n.end_date||"-"})):[]})):this.selectedCourses=[]}else this.selectedCourses=[]}catch(e){console.log(e),this.showErrorMessage("Erro ao buscar cursos. Por favor, tente novamente.")}finally{this.loading=!1}},async searchCategories(){if(this.offerId)try{this.loading=!0;const e=await Lo(this.appliedFilters.category,this.offerId);if(e&&e.data&&e.data.length>0){let t=[];for(const s of e.data){const i=s.id;if(!i)continue;const n=await np(this.offerId,i);if(n&&n.data){const a=n.data.map(u=>({id:u.id||u.courseid,offerCourseId:u.id,name:u.fullname,category:s.name||"-",turmasCount:Array.isArray(u.turmas)?u.turmas.length:0,status:u.status===1||u.status==="1"?"Ativo":"Inativo",can_delete:u.can_delete!==void 0?u.can_delete:!0,can_activate:u.can_activate!==void 0?u.can_activate:!0,turmas:Array.isArray(u.turmas)?u.turmas.map(c=>({id:c.id,nome:c.name,enrol_type:c.enrol_type||"-",vagas:c.max_users?c.max_users:"Ilimitado",inscritos:c.enrolled_users||0,dataInicio:c.start_date||"-",dataFim:c.end_date||"-"})):[]}));t=[...t,...a]}}this.selectedCourses=t}else this.selectedCourses=[]}catch{this.showErrorMessage("Erro ao buscar categorias. Por favor, tente novamente.")}finally{this.loading=!1}},showAddCourseModal(){this.showAddCourseModalVisible=!0},async handleAddCourseConfirm(e){try{this.loading=!0;for(const t of e)await Xw(this.offerId,t);await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.")}catch(t){this.showErrorMessage(t.message||"Ocorreu um erro ao adicionar os cursos.")}finally{this.loading=!1}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadCourses()},handlePageChange(e){this.currentPage=e,this.loadCourses()},addTurma(e){this.selectedCourseForClass=e,this.showEnrolTypeModal=!0},handleEnrolTypeConfirm(e){this.showEnrolTypeModal=!1,this.router.push({name:"NewClass",params:{offercourseid:e.offercourseid,offerid:e.offerid||this.offerId||"0"},query:{enrol_type:e.enrolType}})},editTurma(e){const t=this.selectedCourses.find(s=>s.turmas&&s.turmas.some(i=>i.id===e.id));t?this.router.push({name:"EditClass",params:{offercourseid:t.offerCourseId,classid:e.id}}):this.showErrorMessage("Não foi possível editar a turma. Curso pai não encontrado.")},toggleClassStatus(e){this.selectedClass={...e,status:e.status||"Ativo"},this.showClassStatusModal=!0},async confirmToggleClassStatus(){if(this.selectedClass)try{this.loading=!0;const e=this.selectedClass.nome,t=this.selectedClass.status!=="Ativo";await p0(this.selectedClass.id,t);const s=this.selectedCourses.findIndex(i=>i.turmas.some(n=>n.id===this.selectedClass.id));if(s!==-1){const i=this.selectedCourses[s],n=i.turmas.findIndex(a=>a.id===this.selectedClass.id);n!==-1&&(i.turmas[n].status=t?"Ativo":"Inativo")}await this.loadCourses(),this.showSuccessMessage(t?`Turma "${e}" ativada com sucesso.`:`Turma "${e}" inativada com sucesso.`),this.selectedClass=null,this.showClassStatusModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status da turma.")}finally{this.loading=!1}},removeTurma(e,t){const s=e.turmas[t];s.can_delete&&(this.classToDelete=s,this.classParentCourse=e,this.showDeleteClassModal=!0)},viewRegisteredUsers(e){this.router.push({name:"usuarios-matriculados",params:{offerclassid:parseInt(e.id)}})},duplicateTurma(e,t){this.classToDuplicate=e,this.classToDuplicateParentCourse=t,this.showDuplicateClassModal=!0},async handleDuplicateSuccess(e){await this.loadCourses(),e.duplicatedCount?this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para ${e.duplicatedCount} curso(s).`):this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para o curso "${e.targetCourseName}".`)},async confirmDeleteClass(){if(!(!this.classToDelete||!this.classParentCourse))try{this.loading=!0;const e=this.classToDelete.nome;await u0(this.classToDelete.id);const t=this.classParentCourse.turmas.findIndex(s=>s.id===this.classToDelete.id);t!==-1&&(this.classParentCourse.turmas.splice(t,1),this.classParentCourse.turmasCount=this.classParentCourse.turmas.length),this.showSuccessMessage(`Turma ${e} excluída com sucesso.`),this.classToDelete=null,this.classParentCourse=null,this.showDeleteClassModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao excluir turma.")}finally{this.loading=!1}},updateTurmasCount(e){const t=this.selectedCourses.findIndex(s=>s.id===e);if(t!==-1){const s=this.selectedCourses[t];s.turmasCount=Array.isArray(s.turmas)?s.turmas.length:0,this.selectedCourses[t]={...s}}},async loadCourses(){if(this.offerId)try{this.loading=!0;const e={onlyActive:this.appliedFilters.onlyActive,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc};this.appliedFilters.course&&(e.courseSearch=this.appliedFilters.course),this.appliedFilters.category&&(e.categorySearch=this.appliedFilters.category);const t=await Xi(this.offerId,e);if(t&&t.data){let s=[],i=this.currentPage,n=1,a=0;t.data.courses&&({page:i,total_pages:n,total_items:a,courses:s}=t.data),this.currentPage=i||this.currentPage;const u=[];for(const c of s)try{const h=await a0(c.id);let m=[];h&&typeof h=="object"&&h.error===!1&&Array.isArray(h.data)&&h.data.length>0&&(m=h.data.map(p=>{let _=p.enrol_type||p.enrol||"-";return{id:p.id,nome:p.name,enrol_type:_,vagas:p.max_users?p.max_users:"Ilimitado",inscritos:p.enrolled_users||0,dataInicio:this.formatDate(p.startdate),dataFim:this.formatDate(p.enddate),status:p.status===0||p.status==="0"?"Inativo":"Ativo",can_activate:p.can_activate!==void 0?p.can_activate:!0,can_delete:p.can_delete!==void 0?p.can_delete:!0}})),u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:m.length,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:m})}catch{u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:0,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:[]})}this.selectedCourses=u,a!=null?this.totalItems=a:n>0?this.totalItems=n*this.perPage:this.totalItems=u.length,await this.loadCategoryOptions(),await this.loadCourseOptions()}else this.selectedCourses=[],this.totalItems=0,this.categoryOptions=[],this.courseOptions=[]}catch{this.showErrorMessage("Erro ao carregar cursos da oferta. Por favor, tente novamente."),this.selectedCourses=[],this.totalItems=0}finally{this.loading=!1}},async loadOffer(e){try{this.loading=!0,this.inputFilters={course:"",category:""},this.appliedFilters={course:"",category:""},this.selectedCategory=null,this.selectedCourse=null;const[t,s]=await Promise.all([Xh(e),tp("")]),i=Array.isArray(t)?t[0]:t;if(!i.error&&i.data){const n=i.data;if(this.offer={name:n.name,offerType:n.type,description:n.description,id:n.id,status:n.status},s&&Array.isArray(s.items)){const a=n.audiences||[];this.selectedAudiences=s.items.filter(u=>a.includes(u.id)).map(u=>({value:u.id,label:u.name.toUpperCase()}))}this.isEditing=!0,await this.loadCourses()}else throw new Error(i.message||"Erro ao carregar dados da oferta")}catch(t){this.showErrorMessage(t.message||"Erro ao carregar oferta.")}finally{this.loading=!1}},handleSelectAllAudiences(){const e=new Set(this.allAudiences.map(i=>i.value)),t=new Set(this.selectedAudiences.map(i=>i.value));let s=!0;for(const i of e)if(!t.has(i)){s=!1;break}s&&this.selectedAudiences.length===this.allAudiences.length?this.selectedAudiences=[]:this.selectedAudiences=[...this.allAudiences]},validate(){Object.keys(this.formErrors).forEach(t=>{this.formErrors[t].hasError=!1});let e=!1;return this.offer.name||(this.formErrors.name.hasError=!0,e=!0),this.selectedAudiences.length===0&&(this.formErrors.audiences.hasError=!0,e=!0),e&&this.showErrorMessage("Há campos obrigatórios a serem preenchidos."),!e},validateField(e){switch(e){case"name":this.formErrors.name.hasError=!this.offer.name;break;case"audiences":this.formErrors.audiences.hasError=this.selectedAudiences.length===0;break}return!this.formErrors[e].hasError},async saveOffer(){var e;if(this.loading=!0,!this.validate()){this.loading=!1;return}try{const t={name:this.offer.name,description:this.offer.description,type:this.offer.offerType,status:this.offer.status,audiences:this.selectedAudiences.map(i=>i.value)};let s;if(this.isEditing&&this.offerId)if(t.id=this.offerId,s=await ep(t),s&&!s.error)if(await this.updateOfferAudiences())this.showSuccessMessage("Oferta atualizada com sucesso!");else throw new Error("Oferta atualizada, mas houve falha ao atualizar públicos-alvo.");else{const i=(s==null?void 0:s.message)||(s==null?void 0:s.error)||"Falha ao atualizar oferta.";throw new Error(i)}else if(s=await ep(t),s&&s.data&&s.data.id){const i=s.data.id;this.offerId=i,this.offer.id=i,this.isEditing=!0,this.showSuccessMessage("Oferta salva com sucesso!");const n=`/edit-offer/${this.offerId}`;this.router.replace(n)}else{const i=(s==null?void 0:s.message)||((e=s==null?void 0:s[0])==null?void 0:e.message)||"Falha ao salvar a oferta.";throw new Error(i)}}catch(t){this.showErrorMessage(t.message||"Erro ao salvar oferta. Verifique os dados e tente novamente.")}finally{this.loading=!1}},async updateOfferAudiences(){if(!this.offerId)return!1;try{const e=this.selectedAudiences.map(s=>s.value),t=await s0(this.offerId,e);return!!(t&&!t.error)}catch{return this.showErrorMessage("Erro ao atualizar públicos-alvo."),!1}finally{}},toggleCourseStatus(e){e.can_activate&&(this.selectedCourse=e,this.showCourseStatusModal=!0)},getStatusButtonTitle(e){return e.status==="Ativo"?e.can_activate?"Inativar":"Não é possível inativar este curso":e.can_activate?"Ativar":"Não é possível ativar este curso"},async confirmToggleCourseStatus(){if(this.selectedCourse)try{this.loading=!0;const e=this.selectedCourse.status!=="Ativo",t=this.selectedCourse.name,s=this.selectedCourse.offerCourseId||this.selectedCourse.id;await t0(this.offerId,s,e);const i=this.selectedCourses.findIndex(n=>n.id===this.selectedCourse.id);i!==-1&&(this.selectedCourses[i].status=e?"Ativo":"Inativo"),this.showCourseStatusModal=!1,this.selectedCourse=null,await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(e?`Curso "${t}" ativado com sucesso.`:`Curso "${t}" inativado com sucesso.`)}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status do curso.")}finally{this.loading=!1}},deleteCourse(e){e.can_delete&&(this.courseToDelete=e,this.showDeleteCourseModal=!0)},async confirmDeleteCourse(){if(this.courseToDelete)try{this.loading=!0;const e=this.courseToDelete.name,t=this.courseToDelete.offerCourseId||this.courseToDelete.id;await e0(this.offerId,t),this.selectedCourses=this.selectedCourses.filter(s=>s.id!==this.courseToDelete.id),await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(`Curso "${e}" excluído com sucesso.`),this.courseToDelete=null,this.showDeleteCourseModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao remover curso.")}finally{this.loading=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}},async created(){try{const t=await fu();if(t&&t.data){const{enabled:s,types:i,default:n}=t.data;this.typeOptionsEnabled=s,s&&Array.isArray(i)&&(this.offerTypeOptions=i.map(a=>({value:a,label:a})),n&&!this.isEditing&&(this.offer.offerType=n))}}catch(t){this.showErrorMessage(t.message||"Erro ao carregar opções de tipos.")}const e=this.route.params.id;e&&(this.offerId=parseInt(e),this.loadOffer(this.offerId))},watch:{"inputFilters.course"(e,t){e.length===0&&t.length>0&&this.loadCourses()},"inputFilters.category"(e,t){e.length===0&&t.length>0&&this.loadCourses()},selectedCategory(e){e||(this.inputFilters.category="",this.appliedFilters.category&&(this.appliedFilters.category="",this.loadCourses()),this.loadCourseOptions())},selectedCourse(e){e||(this.inputFilters.course="",this.appliedFilters.course&&(this.appliedFilters.course="",this.loadCourses()))},currentPage(){this.loadCourses()},perPage(){this.currentPage=1,this.loadCourses()}}},vS={id:"new-offer-component",class:"new-offer"},yS={key:0,class:"alert alert-warning"},bS={class:"section-container"},wS={class:"form-row mb-3"},ES={class:"form-group"},CS={class:"input-container"},DS={key:0,class:"form-group"},xS={class:"input-container"},OS={class:"form-row mb-3",style:{"margin-bottom":"1.5rem"}},SS={class:"form-group"},TS={class:"label-container"},NS={class:"label-with-help"},AS={class:"input-container"},IS={class:"form-group text-editor-container"},MS={class:"limited-width-editor"},PS={key:0,class:"section-title"},kS={key:1,class:"message-container"},VS={key:2},RS={class:"filters-left-group"},LS={class:"filters-right-group"},US={class:"empty-state"},FS={class:"no-results"},BS=["title"],$S={class:"action-buttons"},jS=["onClick"],HS=["src"],qS=["onClick","disabled","title"],zS=["onClick","disabled","title"],WS={class:"turmas-container"},GS={class:"turmas-content"},KS={key:0},YS={class:"turma-col"},QS=["title"],ZS={class:"turma-col"},JS={class:"turma-col"},XS={class:"turma-col"},eT={class:"turma-col"},tT={class:"turma-col"},sT={class:"turma-col"},rT={class:"action-buttons"},nT=["onClick"],oT=["src"],iT=["onClick"],aT=["onClick"],lT=["title","onClick"],uT=["onClick","disabled","title"],cT={key:1,class:"empty-turmas"},dT={class:"d-flex justify-content-between align-items-center"},fT={class:"actions-container offer-actions"};function hT(e,t,s,i,n,a){var be,ue,Ge,_t,mt,ft,Oe,we,Ut,es,yt;const u=ee("BackButton"),c=ee("PageHeader"),h=ee("CustomInput"),m=ee("CustomSelect"),p=ee("HelpIcon"),_=ee("Autocomplete"),w=ee("TextEditor"),D=ee("FilterGroup"),V=ee("CustomCheckbox"),F=ee("FilterTag"),te=ee("FilterTags"),I=ee("FilterRow"),se=ee("CollapsibleTable"),K=ee("Pagination"),ye=ee("CustomButton"),Z=ee("AddCourseModal"),fe=ee("ConfirmationModal"),ve=ee("DuplicateClassModal"),Ae=ee("EnrolTypeModal"),ae=ee("LFLoading"),A=ee("Toast");return O(),N("div",vS,[k(c,{title:n.isEditing?`Editar oferta: ${n.offer.name}`:"Nova oferta"},{actions:Ne(()=>[k(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"]),n.showWarning?(O(),N("div",yS,t[21]||(t[21]=[f("i",{class:"fas fa-exclamation-triangle"},null,-1),nt(" Para que uma instância de oferta seja ativada e disponibilize os cursos para os públicos-alvo configurados, é necessário garantir que pelo menos um curso, um grupo de público-alvo, e uma turma estejam configurados à instância da oferta. ")]))):ie("",!0),f("div",bS,[t[27]||(t[27]=f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),f("div",wS,[f("div",ES,[t[22]||(t[22]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Nome da Oferta"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),f("div",CS,[k(h,{modelValue:n.offer.name,"onUpdate:modelValue":t[0]||(t[0]=ce=>n.offer.name=ce),placeholder:"Oferta 0001",width:280,required:"","has-error":n.formErrors.name.hasError,"error-message":n.formErrors.name.message,onValidate:t[1]||(t[1]=ce=>a.validateField("name"))},null,8,["modelValue","has-error","error-message"])])]),n.typeOptionsEnabled?(O(),N("div",DS,[t[23]||(t[23]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Tipo da oferta")])],-1)),f("div",xS,[k(m,{modelValue:n.offer.offerType,"onUpdate:modelValue":t[2]||(t[2]=ce=>n.offer.offerType=ce),options:n.offerTypeOptions,width:280},null,8,["modelValue","options"])])])):ie("",!0)]),f("div",OS,[f("div",SS,[f("div",TS,[f("div",NS,[t[24]||(t[24]=f("label",{class:"form-label"},"Público-alvo",-1)),t[25]||(t[25]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(p,{title:"Ajuda com público-alvo",text:"Selecione pelo menos um público-alvo para a oferta."})])]),f("div",AS,[k(_,{class:"autocomplete-audiences",modelValue:n.selectedAudiences,"onUpdate:modelValue":[t[3]||(t[3]=ce=>n.selectedAudiences=ce),t[4]||(t[4]=ce=>a.validateField("audiences"))],items:n.audienceTypeOptions,placeholder:"Pesquisar público-alvo...","input-max-width":218,required:!0,"show-all-option":!0,"has-error":n.formErrors.audiences.hasError,"error-message":n.formErrors.audiences.message,onSelectAll:a.handleSelectAllAudiences},null,8,["modelValue","items","has-error","error-message","onSelectAll"])])])]),f("div",IS,[t[26]||(t[26]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Descrição da oferta")])],-1)),f("div",MS,[k(w,{modelValue:n.offer.description,"onUpdate:modelValue":t[5]||(t[5]=ce=>n.offer.description=ce),placeholder:"Digite a descrição da oferta aqui...",rows:5},null,8,["modelValue"])])])]),f("div",{class:he(["section-container",{"no-title-section":!n.isEditing}])},[n.isEditing?(O(),N("h2",PS,"CURSOS")):ie("",!0),!a.canManageCourses||!n.isEditing?(O(),N("div",kS,t[28]||(t[28]=[f("div",{class:"lock-message"},[f("i",{class:"fas fa-lock lock-icon"}),f("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)]))):ie("",!0),n.isEditing&&a.canManageCourses?(O(),N("div",VS,[k(I,{inline:"",class:"courses-filter-row"},{default:Ne(()=>[f("div",RS,[k(D,null,{default:Ne(()=>[k(_,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[6]||(t[6]=ce=>n.selectedCategory=ce),items:n.categoryOptions,placeholder:"Pesquisar...",label:"Categoria","input-max-width":218,"has-search-icon":!0,"auto-open":!1,"show-filter-tags":!1,"show-selected-in-input":!0,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","no-results-text","onSelect"])]),_:1}),k(D,null,{default:Ne(()=>[k(_,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[7]||(t[7]=ce=>n.selectedCourse=ce),items:n.courseOptions,placeholder:"Pesquisar...",label:"Curso","input-max-width":218,"has-search-icon":!0,"auto-open":!0,loading:n.loadingCourses||n.loadingMoreCourses,"no-results-text":n.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","loading","no-results-text","onSelect","onLoadMore","onSearch"])]),_:1}),k(D,{isCheckbox:!0,class:"checkbox-filter-group"},{default:Ne(()=>[k(V,{modelValue:n.inputFilters.onlyActive,"onUpdate:modelValue":t[8]||(t[8]=ce=>n.inputFilters.onlyActive=ce),id:"onlyActive",label:"Não exibir inativos",onChange:a.handleOnlyActiveChange},null,8,["modelValue","onChange"])]),_:1}),n.appliedFilters.course?(O(),Rt(te,{key:0,class:"mt-3"},{default:Ne(()=>[k(F,{onRemove:t[9]||(t[9]=ce=>a.removeFilter("course"))},{default:Ne(()=>[nt(" Curso: "+G(n.appliedFilters.course),1)]),_:1})]),_:1})):ie("",!0)]),f("div",LS,[f("button",{class:"btn btn-primary",onClick:t[10]||(t[10]=(...ce)=>a.showAddCourseModal&&a.showAddCourseModal(...ce))}," Adicionar curso ")])]),_:1}),k(se,{headers:n.courseTableHeaders,items:n.selectedCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort,expandable:!0},{"empty-state":Ne(()=>[f("div",US,[f("span",FS,G(n.loading?"Carregando registros...":"Não existem registros"),1)])]),"item-name":Ne(({item:ce})=>[f("span",{title:ce.name},G(ce.name.length>50?ce.name.slice(0,50)+"...":ce.name),9,BS)]),"item-actions":Ne(({item:ce})=>[f("div",$S,[f("button",{class:"btn-action btn-add",onClick:ze=>a.addTurma(ce),title:"Adicionar turma"},[f("img",{src:n.icons.plus,alt:"Adicionar turma"},null,8,HS)],8,jS),f("button",{class:he(["btn-action",ce.status==="Ativo"?"btn-deactivate":"btn-activate"]),onClick:ze=>a.toggleCourseStatus(ce),disabled:ce.status==="Inativo"&&!ce.can_activate||!ce.can_activate,title:a.getStatusButtonTitle(ce)},[f("i",{class:he(ce.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,qS),f("button",{class:"btn-action btn-delete",onClick:ze=>a.deleteCourse(ce),disabled:!ce.can_delete,title:ce.can_delete?"Excluir":"Não é possível excluir este curso"},t[29]||(t[29]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,zS)])]),"expanded-content":Ne(({item:ce})=>[f("div",WS,[t[34]||(t[34]=f("div",{class:"turmas-header"},[f("div",{class:"turma-col"},"NOME DA TURMA"),f("div",{class:"turma-col"},"TIPO DE INSCRIÇÃO"),f("div",{class:"turma-col"},"QTD. DE VAGAS"),f("div",{class:"turma-col"},"QTD. DE ALUNOS INSCRITOS"),f("div",{class:"turma-col"},"DATA INÍCIO DA TURMA"),f("div",{class:"turma-col"},"DATA FIM DA TURMA"),f("div",{class:"turma-col"},"AÇÕES")],-1)),f("div",GS,[ce.turmas&&ce.turmas.length>0?(O(),N("div",KS,[(O(!0),N(Me,null,dt(ce.turmas,(ze,hs)=>(O(),N("div",{class:"turmas-row",key:hs},[f("div",YS,[f("span",{title:ze.nome},G(ze.nome.length>20?ze.nome.slice(0,20)+"...":ze.nome),9,QS)]),f("div",ZS,G(a.getEnrolTypeLabel(ze.enrol_type)),1),f("div",JS,G(ze.vagas),1),f("div",XS,G(ze.inscritos),1),f("div",eT,G(ze.dataInicio),1),f("div",tT,G(ze.dataFim),1),f("div",sT,[f("div",rT,[f("button",{class:"btn-action btn-users",onClick:zt=>a.viewRegisteredUsers(ze),title:"Usuários Matriculados"},[f("img",{src:n.icons.users,alt:"Usuários Matriculados"},null,8,oT)],8,nT),f("button",{class:"btn-action btn-edit",onClick:zt=>a.editTurma(ze),title:"Editar"},t[30]||(t[30]=[f("i",{class:"fas fa-pencil-alt"},null,-1)]),8,iT),f("button",{class:"btn-action btn-duplicate",onClick:zt=>a.duplicateTurma(ze,ce),title:"Duplicar Turma"},t[31]||(t[31]=[f("i",{class:"fas fa-copy"},null,-1)]),8,aT),f("button",{class:he(["btn-action",ze.status==="Ativo"?"btn-deactivate":"btn-activate"]),title:ze.status==="Ativo"?"Inativar":"Ativar",onClick:zt=>a.toggleClassStatus(ze)},[f("i",{class:he(ze.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,lT),f("button",{class:"btn-action btn-delete",onClick:zt=>a.removeTurma(ce,hs),disabled:!ze.can_delete,title:ze.can_delete?"Excluir":"Não é possível excluir esta turma"},t[32]||(t[32]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,uT)])])]))),128))])):(O(),N("div",cT,t[33]||(t[33]=[f("span",null,"Nenhuma turma encontrada para este curso",-1)])))])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),k(K,{ref:"pagination","current-page":n.currentPage,"onUpdate:currentPage":[t[11]||(t[11]=ce=>n.currentPage=ce),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":t[12]||(t[12]=ce=>n.perPage=ce),total:n.totalItems},null,8,["current-page","per-page","total","onUpdate:currentPage"])])):ie("",!0)],2),t[36]||(t[36]=f("hr",null,null,-1)),f("div",dT,[t[35]||(t[35]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[nt(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),f("div",fT,[k(ye,{variant:"primary",label:"Salvar",onClick:a.saveOffer},null,8,["onClick"]),k(ye,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])])]),k(Z,{modelValue:n.showAddCourseModalVisible,"onUpdate:modelValue":t[13]||(t[13]=ce=>n.showAddCourseModalVisible=ce),"offer-id":n.offerId,onConfirm:a.handleAddCourseConfirm},null,8,["modelValue","offer-id","onConfirm"]),k(fe,{show:n.showCourseStatusModal,title:((be=n.selectedCourse)==null?void 0:be.status)==="Ativo"?"Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((ue=n.selectedCourse)==null?void 0:ue.status)==="Ativo"?"":"Tem certeza que deseja ativar este curso?","list-items":((Ge=n.selectedCourse)==null?void 0:Ge.status)==="Ativo"?["O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((_t=n.selectedCourse)==null?void 0:_t.status)==="Ativo"?"Inativar curso":"Ativar","cancel-button-text":"Cancelar",icon:((mt=n.selectedCourse)==null?void 0:mt.status)==="Ativo"?"warning":"question",onClose:t[14]||(t[14]=ce=>n.showCourseStatusModal=!1),onConfirm:a.confirmToggleCourseStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),k(fe,{show:n.showDeleteCourseModal,title:"A exclusão deste curso da instância de oferta é uma ação irreversível",message:"Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir curso","cancel-button-text":"Cancelar",icon:"warning",onClose:t[15]||(t[15]=ce=>n.showDeleteCourseModal=!1),onConfirm:a.confirmDeleteCourse},null,8,["show","onConfirm"]),k(fe,{show:n.showDeleteClassModal,title:"A exclusão desta turma é uma ação irreversível",message:"Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Turma","cancel-button-text":"Cancelar",icon:"warning",onClose:t[16]||(t[16]=ce=>n.showDeleteClassModal=!1),onConfirm:a.confirmDeleteClass},null,8,["show","onConfirm"]),k(fe,{show:n.showClassStatusModal,title:((ft=n.selectedClass)==null?void 0:ft.status)==="Ativo"?"Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:":"Confirmar Ativação",message:((Oe=n.selectedClass)==null?void 0:Oe.status)==="Ativo"?"":"Tem certeza que deseja ativar esta turma?","list-items":((we=n.selectedClass)==null?void 0:we.status)==="Ativo"?["Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.","Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.","Novos alunos não poderão ser matriculados através da oferta."]:[],"confirm-button-text":((Ut=n.selectedClass)==null?void 0:Ut.status)==="Ativo"?"Inativar Turma":"Ativar","cancel-button-text":"Cancelar",icon:((es=n.selectedClass)==null?void 0:es.status)==="Ativo"?"warning":"question",onClose:t[17]||(t[17]=ce=>n.showClassStatusModal=!1),onConfirm:a.confirmToggleClassStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),k(ve,{show:n.showDuplicateClassModal,turma:n.classToDuplicate,parentCourse:n.classToDuplicateParentCourse,offerId:n.offerId,onClose:t[18]||(t[18]=ce=>n.showDuplicateClassModal=!1),onSuccess:a.handleDuplicateSuccess,onLoading:t[19]||(t[19]=ce=>n.loading=ce),onError:a.showErrorMessage},null,8,["show","turma","parentCourse","offerId","onSuccess","onError"]),k(Ae,{show:n.showEnrolTypeModal,offercourseid:(yt=n.selectedCourseForClass)==null?void 0:yt.offerCourseId,offerid:n.offerId||"0",onClose:t[20]||(t[20]=ce=>n.showEnrolTypeModal=!1),onConfirm:a.handleEnrolTypeConfirm},null,8,["show","offercourseid","offerid","onConfirm"]),k(ae,{"is-loading":n.loading},null,8,["is-loading"]),k(A,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const up=He(_S,[["render",hT],["__scopeId","data-v-540f42da"]]),YR="",pT={name:"NewClassView",components:{CustomInput:Uo,CustomSelect:mr,CustomButton:Fn,PageHeader:ra,BackButton:vu,Autocomplete:$o,TextEditor:lp,CustomCheckbox:ea,FilterRow:ta,FilterGroup:sa,Toast:Fo,HelpIcon:yu,FilterTag:Bo,FilterTags:na},setup(){const e=Zi(),t=Zh();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},props:{offercourseid:{type:Number,required:!0},classid:{type:Number,required:!1,default:null}},data(){return{loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,isEditing:!1,enrolmentMethods:[],classData:{enrol:"",offercourseid:null,classname:"",startdate:"",teachers:[],optional_fields:{enableenddate:!1,enddate:"",enablepreenrolment:!1,preenrolmentstartdate:"",preenrolmentenddate:"",description:"",enableenrolperiod:!1,enrolperiod:null,minusers:null,maxusers:null,roleid:null,enablereenrol:!1,reenrolmentsituations:[],enableextension:!1,extensionperiod:null,extensiondaysavailable:null,extensionmaxrequests:null,extensionallowedsituations:[]}},selectedTeachers:[],teacherSearchTerm:"",debounceTimer:null,teacherList:[],showTeacherDropdown:!1,highlightedIndex:-1,extensionSituations:[],reenrolSituations:[],roleOptions:[],situationList:[],offerCourse:null,formErrors:{enrol:{hasError:!1,message:"Método de inscrição é obrigatório"},classname:{hasError:!1,message:"Nome da turma é obrigatório"},startdate:{hasError:!1,message:"Data de início é obrigatória"},roleid:{hasError:!1,message:"Papel padrão é obrigatório"},enddate:{hasError:!1,message:"Data fim da turma é obrigatória quando habilitada"},preenrolmentstartdate:{hasError:!1,message:"Data início de pré-inscrição é obrigatória quando habilitada"},preenrolmentenddate:{hasError:!1,message:"Data fim de pré-inscrição é obrigatória quando habilitada"},enrolperiod:{hasError:!1,message:"Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma"},extensionperiod:{hasError:!1,message:"Dias para prorrogação é obrigatório quando habilitada"},extensiondaysavailable:{hasError:!1,message:"Dias antes do término é obrigatório quando habilitada"},extensionmaxrequests:{hasError:!1,message:"Máximo de solicitações é obrigatório quando habilitada"}},validationAlert:{show:!1,message:"Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados."}}},async created(){if(!this.offercourseid)throw new Error("offercourseid não foi definido.");this.classid?this.isEditing=!0:this.route.query.classid&&this.route.query.edit==="true"&&(this.isEditing=!0,this.$nextTick(()=>{this.router.replace({name:"EditClass",params:{offercourseid:this.offercourseid,classid:this.classid}})})),await this.loadInitialData(),this.isEditing&&this.classid&&(await this.loadClassData(),this.$nextTick(()=>{this.restartComponent()}))},mounted(){window.scrollTo(0,0),document.addEventListener("click",this.handleClickOutside)},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside),this.debounceTimer&&clearTimeout(this.debounceTimer)},computed:{extensionSituationList(){let e=[0,1];return this.situationList.filter(t=>e.includes(t.value))},reenrolSituationList(){let e=[6,7,8,4,5];return this.situationList.filter(t=>e.includes(t.value))},maxEnrolPeriod(){if(this.classData.startdate&&this.classData.optional_fields.enddate&&this.classData.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.classData.startdate,this.classData.optional_fields.enddate);return e>=1?e:1}return null},isOneDayClass(){return this.classData.startdate&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.classData.startdate===this.classData.optional_fields.enddate},shouldDisableEnrolPeriod(){return this.isOneDayClass}},watch:{extensionSituations:{handler(e){this.classData.optional_fields.extensionallowedsituations=e.map(t=>t.value)},deep:!0},reenrolSituations:{handler(e){this.classData.optional_fields.reenrolmentsituations=e.map(t=>t.value)},deep:!0},"classData.optional_fields.enableenrolperiod":function(e){!e&&this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.showWarningMessage("Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado."))},"classData.startdate":function(){this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enddate":function(){this.classData.optional_fields.enableenddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.classData.optional_fields.enableenddate&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enableenddate":function(e){e&&this.classData.optional_fields.enddate&&this.validateField("enddate"),!e&&this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enrolperiod":function(e){this.classData.optional_fields.enableenrolperiod&&e&&this.validateField("enrolperiod")},"classData.optional_fields.preenrolmentstartdate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.preenrolmentenddate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()}},methods:{calculateDaysDifference(e,t){if(!e||!t)return 0;const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;if(s.getTime()===i.getTime())return 1;const n=Math.abs(i-s);return Math.ceil(n/(1e3*60*60*24))},checkAndDisableEnrolPeriodForOneDayClass(){this.isOneDayClass&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enableenrolperiod=!1,this.classData.optional_fields.enrolperiod=null,this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[]),this.showWarningMessage("Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."))},async loadInitialData(){try{this.loading=!0,this.isEditing||(this.classData.offercourseid=this.offercourseid),await this.loadOfferCourse(),await this.loadRoles(),await this.loadSituations(),await this.loadEnrolmentMethods()}catch{this.showErrorMessage("Alguns dados não puderam ser carregados.")}finally{this.loading=!1}},async loadOfferCourse(){try{const e=await i0(this.offercourseid);this.offerCourse=e==null?void 0:e.data}catch{this.showErrorMessage("Erro ao carregar informações do curso da oferta.")}},async loadRoles(){const e=await pu(this.offercourseid);if(e.data&&(this.roleOptions=e.data.map(t=>({value:t.id,label:t.name})),!this.classData.optional_fields.roleid)){const t=this.roleOptions.find(s=>s.value===5);this.classData.optional_fields.roleid=(t==null?void 0:t.value)??this.roleOptions[0].value}},async loadSituations(){const e=await d0();e.data&&(this.situationList=e.data.map(t=>({value:t.id,label:t.name})))},async loadEnrolmentMethods(){const e=this.route.query.enrol_type;!this.isEditing&&e&&(this.classData.enrol=e);const t=await op(!0);t&&Array.isArray(t)&&(this.enrolmentMethods=t.map(s=>({value:s.enrol,label:s.name})))},validate(){Object.keys(this.formErrors).forEach(s=>{this.formErrors[s].hasError=!1}),this.validationAlert.show=!1;let e=!1;return this.classData.classname||(this.formErrors.classname.hasError=!0,e=!0),this.classData.startdate?this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate)?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0,e=!0):this.formErrors.startdate.hasError=!1:(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0,e=!0),this.classData.optional_fields.roleid||(this.formErrors.roleid.hasError=!0,e=!0),this.classData.optional_fields.enableenddate&&(this.classData.optional_fields.enddate?this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate)?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0,e=!0):this.formErrors.enddate.hasError=!1:(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0,e=!0)),this.classData.optional_fields.enablepreenrolment&&(this.classData.optional_fields.preenrolmentstartdate||(this.formErrors.preenrolmentstartdate.hasError=!0,e=!0),this.classData.optional_fields.preenrolmentenddate||(this.formErrors.preenrolmentenddate.hasError=!0,e=!0)),this.validatePreenrolmentDates()||(e=!0),this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enrolperiod?this.maxEnrolPeriod!==null&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod&&(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0,e=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0,e=!0)),this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.extensionperiod||(this.formErrors.extensionperiod.hasError=!0,e=!0),this.classData.optional_fields.extensiondaysavailable||(this.formErrors.extensiondaysavailable.hasError=!0,e=!0),this.classData.optional_fields.extensionmaxrequests||(this.formErrors.extensionmaxrequests.hasError=!0,e=!0)),e&&(this.validationAlert.show=!0,this.showErrorMessage(this.validationAlert.message),window.scrollTo({top:0,behavior:"smooth"})),!e},validateField(e){switch(e){case"enrol":this.formErrors.enrol.hasError=!1;break;case"classname":this.formErrors.classname.hasError=!this.classData.classname;break;case"startdate":const s=this.classData.startdate,i=s&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate);s?i?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!1):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0);break;case"roleid":this.formErrors.roleid.hasError=!this.classData.optional_fields.roleid;break;case"enddate":const n=this.classData.optional_fields.enableenddate,a=this.classData.optional_fields.enddate,u=n&&a&&this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate);n&&!a?(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0):u?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0):(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!1);break;case"preenrolmentstartdate":this.formErrors.preenrolmentstartdate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentstartdate,this.validatePreenrolmentDates();break;case"preenrolmentenddate":this.formErrors.preenrolmentenddate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentenddate,this.validatePreenrolmentDates();break;case"enrolperiod":const c=this.classData.optional_fields.enableenrolperiod,h=this.classData.optional_fields.enrolperiod!==null&&this.classData.optional_fields.enrolperiod!==void 0&&this.classData.optional_fields.enrolperiod!=="",m=this.maxEnrolPeriod!==null&&h&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod;c&&!h?(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0):c&&m?(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",this.formErrors.enrolperiod.hasError=!1);break;case"extensionperiod":this.formErrors.extensionperiod.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionperiod;break;case"extensiondaysavailable":this.formErrors.extensiondaysavailable.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensiondaysavailable;break;case"extensionmaxrequests":this.formErrors.extensionmaxrequests.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionmaxrequests;break}const t=Object.values(this.formErrors).some(s=>s.hasError);return this.validationAlert.show=t,!this.formErrors[e].hasError},validatePreenrolmentDates(){let e=!0;if(this.formErrors.preenrolmentstartdate.hasError=!1,this.formErrors.preenrolmentenddate.hasError=!1,this.classData.optional_fields.enablepreenrolment){const t=this.classData.startdate,s=this.classData.optional_fields.enableenddate?this.classData.optional_fields.enddate:null,i=this.classData.optional_fields.preenrolmentstartdate,n=this.classData.optional_fields.preenrolmentenddate,a=this.offerCourse.startdate,u=this.offerCourse.enddate;i||(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início de pré-inscrição é obrigatória",e=!1),n||(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim de pré-inscrição é obrigatória",e=!1),new Date(n)<new Date(i)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser posterior à data início",e=!1),new Date(i)>new Date(t)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou anterior à data início da turma",e=!1),new Date(i)<new Date(a)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou posterior à data início do curso",e=!1),u&&new Date(i)>new Date(u)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data de início deve ser igual ou anterior à data fim do curso",e=!1),s&&new Date(n)>=new Date(s)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser anterior à data fim da turma",e=!1),u&&new Date(n)>new Date(u)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser igual ou anterior à data fim do curso",e=!1)}return e},async loadClassData(){this.loading=!0;const e=await hu(this.classid);if(e.error==!0)throw new Error(e.exception);this.classData=e.data,e.data.optional_fields&&this.processOptionalFields(e.data.optional_fields),e.data.teachers&&Array.isArray(e.data.teachers)&&(this.selectedTeachers=e.data.teachers),this.updateUIAfterLoading(),document.title=`Editar Turma: ${this.classData.classname}`,this.loading=!1},processOptionalFields(e){this.processDateFields(e),this.processEnrolmentFields(e),this.processUserLimits(e),this.processDescriptionAndRole(e),this.processReenrolment(e),this.processExtensionFields(e)},processDateFields(e){e.enableenddate&&(this.classData.optional_fields.enableenddate=!0,this.classData.optional_fields.enddate=e.enddate||null),e.enablepreenrolment&&(this.classData.optional_fields.enablepreenrolment=!0,this.classData.optional_fields.preenrolmentstartdate=e.preenrolmentstartdate||null,this.classData.optional_fields.preenrolmentenddate=e.preenrolmentenddate||null)},processEnrolmentFields(e){e.enableenrolperiod?(this.classData.optional_fields.enableenrolperiod=!0,this.classData.optional_fields.enrolperiod=e.enrolperiod>0?e.enrolperiod:null):this.classData.optional_fields.enrolperiod=null},processUserLimits(e){this.classData.optional_fields.minusers=e.minusers>0?e.minusers:null,this.classData.optional_fields.maxusers=e.maxusers>0?e.maxusers:null},processDescriptionAndRole(e){this.classData.optional_fields.roleid=e.roleid||null,this.classData.optional_fields.description=e.description||""},processReenrolment(e){e.enablereenrol?(this.classData.optional_fields.enablereenrol=!0,this.classData.optional_fields.reenrolmentsituations=e.reenrolmentsituations||[],Array.isArray(e.reenrolmentsituations)&&(this.reenrolSituations=e.reenrolmentsituations.map(t=>this.situationList.find(s=>s.value===parseInt(t))))):this.classData.optional_fields.reenrolmentsituations=[]},processExtensionFields(e){e.enableextension&&e.enableenrolperiod?(this.classData.optional_fields.enableextension=!0,this.processExtensionPeriods(e),this.processExtensionSituations(e)):this.resetExtensionFields()},processExtensionPeriods(e){this.classData.optional_fields.extensionperiod=e.extensionperiod>0?e.extensionperiod:null,this.classData.optional_fields.extensiondaysavailable=e.extensiondaysavailable>0?e.extensiondaysavailable:null,this.classData.optional_fields.extensionmaxrequests=e.extensionmaxrequests>0?e.extensionmaxrequests:null},processExtensionSituations(e){Array.isArray(e.extensionallowedsituations)&&(this.extensionSituations=e.extensionallowedsituations.map(t=>this.situationList.find(s=>s.value===t)))},resetExtensionFields(){this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[]},handleTeacherInput(){const e=this.teacherSearchTerm.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?(this.showTeacherDropdown=!0,this.highlightedIndex=-1,this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialTeachers(e)},500)):(this.showTeacherDropdown=!1,this.teacherList=[])},async fetchPotentialTeachers(e){let t=this.selectedTeachers.map(i=>i.id||i.value)??[];const s=await c0(this.offercourseid,this.classid,e,t);this.teacherList=!s.error&&s.data?s.data:[]},removeTeacher(e){this.selectedTeachers=this.selectedTeachers.filter(t=>t.id!==e)},handleTeacherInputFocus(){this.teacherSearchTerm.length>=3&&this.teacherList.length>0&&(this.showTeacherDropdown=!0)},selectTeacher(e){this.selectedTeachers.push({id:e.id,value:e.id,fullname:e.fullname,email:e.email}),this.teacherSearchTerm="",this.showTeacherDropdown=!1,this.highlightedIndex=-1,this.teacherList=[],this.$nextTick(()=>{this.$refs.teacherSearchInput&&this.$refs.teacherSearchInput.focus()})},handleKeydown(e){if(!(!this.showTeacherDropdown||this.teacherList.length===0))switch(e.key){case"ArrowDown":e.preventDefault(),this.highlightedIndex=Math.min(this.highlightedIndex+1,this.teacherList.length-1);break;case"ArrowUp":e.preventDefault(),this.highlightedIndex=Math.max(this.highlightedIndex-1,0);break;case"Enter":e.preventDefault(),this.highlightedIndex>=0&&this.highlightedIndex<this.teacherList.length&&this.selectTeacher(this.teacherList[this.highlightedIndex]);break;case"Escape":e.preventDefault(),this.showTeacherDropdown=!1,this.highlightedIndex=-1;break}},handleClickOutside(e){this.$refs.teacherSearchContainer&&!this.$refs.teacherSearchContainer.contains(e.target)&&(this.showTeacherDropdown=!1,this.highlightedIndex=-1)},updateUIAfterLoading(){this.$nextTick(()=>{this.updateFormFields(),this.$forceUpdate(),(!this.classData.classname||!this.classData.enrol)&&this.showErrorMessage("Dados incompletos após carregamento.")})},updateFormFields(){this.updateSelectField("enrolSelect",this.classData.enrol),this.updateInputField("classnameInput",this.classData.classname),this.updateInputField("startdateInput",this.classData.startdate)},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},async saveClass(){if(!this.validate())return;this.loading=!0;const e=JSON.parse(JSON.stringify(this.classData));e.teachers=this.selectedTeachers.map(n=>n.id),!e.optional_fields.enableextension||!e.optional_fields.enableenrolperiod?(e.optional_fields.extensionperiod=void 0,e.optional_fields.extensiondaysavailable=void 0,e.optional_fields.extensionmaxrequests=void 0,e.optional_fields.extensionallowedsituations=[],e.optional_fields.enableenrolperiod||(e.optional_fields.enableextension=!1)):e.optional_fields.extensionallowedsituations=this.extensionSituations.map(n=>n.value),e.optional_fields.enableenrolperiod||(e.optional_fields.enrolperiod=void 0),e.optional_fields.enablereenrol?e.optional_fields.reenrolmentsituations=this.reenrolSituations.map(n=>n.value):e.optional_fields.reenrolmentsituations=[],["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].forEach(n=>{const a=e.optional_fields[n];(a===0||a===null||a===""||a===void 0)&&(e.optional_fields[n]=void 0)}),this.isEditing&&"enrol"in e&&delete e.enrol;const i=(this.isEditing?["offercourseid","classname","startdate"]:["offercourseid","classname","startdate","enrol"]).filter(n=>!e[n]);if(i.length>0){this.showErrorMessage(`Campos obrigatórios ausentes: ${i.join(", ")}`),this.loading=!1;return}if(e.offercourseid=parseInt(e.offercourseid),this.isEditing&&this.classid){e.offerclassid=this.classid;let n=await l0(e);!n.error&&n.data?(this.showSuccessMessage(n.data.message),this.loadClassData()):this.showErrorMessage(n.exception.message)}else{let n=await o0(e);!n.error&&n.data?(this.showSuccessMessage(n.data.message),this.isEditing=!0,this.router.push({name:"EditClass",params:{offercourseid:this.offercourseid,classid:n.data.offerclassid}})):this.showErrorMessage(n.exception.message)}this.loading=!1},goBack(){this.offerCourse.offerid?this.router.push({name:"editar-oferta",params:{id:this.offerCourse.offerid}}):this.router.push({name:"listar-ofertas"})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},updateFormFields(){if(this.$refs.enrolSelect&&this.classData.enrol)try{this.$refs.enrolSelect.value=this.classData.enrol;const e=new Event("change");this.$refs.enrolSelect.$el.dispatchEvent(e),this.$refs.enrolSelect.$emit("input",this.classData.enrol),this.$refs.enrolSelect.$forceUpdate()}catch{}if(this.$refs.classnameInput&&this.classData.classname)try{this.$refs.classnameInput.value=this.classData.classname;const e=new Event("input");this.$refs.classnameInput.$el.dispatchEvent(e),this.$refs.classnameInput.$emit("input",this.classData.classname),this.$refs.classnameInput.$forceUpdate()}catch{}if(this.$refs.startdateInput&&this.classData.startdate)try{this.$refs.startdateInput.value=this.classData.startdate;const e=new Event("input");this.$refs.startdateInput.$el.dispatchEvent(e),this.$refs.startdateInput.$emit("input",this.classData.startdate),this.$refs.startdateInput.$forceUpdate()}catch{}this.$forceUpdate()},handleSelectAllExtensionSituations(){const e=this.extensionSituationList.every(t=>this.extensionSituations.some(s=>s.value===t.value));this.extensionSituations=e?[]:[...this.extensionSituationList],this.classData.optional_fields.extensionallowedsituations=this.extensionSituations.map(t=>t.value)},handleSelectAllReenrolSituations(){const e=this.reenrolSituationList.every(t=>this.reenrolSituations.some(s=>s.value===t.value));this.reenrolSituations=e?[]:[...this.reenrolSituationList],this.classData.optional_fields.reenrolmentsituations=this.reenrolSituations.map(t=>t.value)},restartComponent(){window.scrollTo(0,0),this.updateFormFields(),this.$forceUpdate(),setTimeout(()=>{this.updateFormFields(),this.$forceUpdate(),window.scrollTo(0,0)},500)}}},mT={class:"new-class",ref:"classView"},gT={class:"page-header-container"},_T={key:0,class:"validation-alert"},vT={class:"section-container"},yT={class:"form-group mb-3"},bT={class:"label-with-help"},wT={class:"limited-width-input",style:{"max-width":"280px"}},ET={class:"form-row mb-3"},CT={class:"form-group"},DT={class:"label-with-help"},xT={class:"limited-width-input"},OT={class:"label-with-help"},ST={class:"input-with-checkbox"},TT={class:"limited-width-input"},NT={class:"form-row mb-3"},AT={class:"label-with-help"},IT={class:"label-with-help"},MT={key:2,class:"form-group"},PT={class:"form-group mb-3"},kT={class:"label-with-help"},VT={class:"limited-width-editor"},RT={class:"form-row mb-3"},LT={key:0,class:"form-group"},UT={class:"label-with-help"},FT={class:"limited-width-input"},BT={key:1,class:"form-group"},$T={class:"label-with-help"},jT={class:"limited-width-input"},HT={class:"form-group"},qT={class:"label-with-help"},zT={class:"limited-width-input"},WT={class:"form-row mb-3"},GT={class:"label-with-help"},KT={class:"input-with-checkbox"},YT={class:"limited-width-input"},QT={class:"section-container"},ZT={class:"form-row mb-3"},JT={class:"label-with-help"},XT={class:"form-row mb-3"},eN={class:"limited-width-input"},tN={class:"form-row mb-3"},sN={class:"limited-width-input"},rN={class:"form-row mb-3"},nN={class:"limited-width-input"},oN={class:"limited-width-select"},iN={key:1,class:"section-container"},aN={class:"form-row mb-3"},lN={class:"form-group"},uN={class:"label-with-help"},cN={class:"limited-width-select"},dN={class:"section-container"},fN={class:"form-group mb-3"},hN={class:"label-with-help"},pN={class:"limited-width-select"},mN={class:"position-relative",ref:"teacherSearchContainer"},gN={class:"input-wrapper with-icon"},_N={key:0,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","max-height":"200px","overflow-y":"auto","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"},ref:"teacherDropdown"},vN=["onClick","onMouseenter"],yN={key:0,class:"text-muted small"},bN={key:1,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"}},wN={class:"my-4"},EN=["onClick"],CN={class:"actions-container"},DN={key:2,class:"loading"};function xN(e,t,s,i,n,a){const u=ee("BackButton"),c=ee("PageHeader"),h=ee("HelpIcon"),m=ee("CustomInput"),p=ee("CustomCheckbox"),_=ee("TextEditor"),w=ee("CustomSelect"),D=ee("Autocomplete"),V=ee("CustomButton"),F=ee("Toast"),te=mv("tooltip");return O(),N("div",mT,[f("div",gT,[k(c,{title:n.isEditing?"Editar turma":"Nova turma"},{actions:Ne(()=>[k(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"])]),n.validationAlert.show?(O(),N("div",_T,[t[34]||(t[34]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),f("span",null,G(n.validationAlert.message),1)])):ie("",!0),f("div",vT,[t[51]||(t[51]=f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),f("div",yT,[f("div",bT,[t[35]||(t[35]=f("label",{class:"form-label"},"Nome da turma",-1)),t[36]||(t[36]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(h,{title:"Ajuda com nome da turma",text:"Insira um nome para a turma. Exemplo: Turma ADM 2025."})]),f("div",wT,[k(m,{modelValue:n.classData.classname,"onUpdate:modelValue":t[0]||(t[0]=I=>n.classData.classname=I),placeholder:"Digite o nome da turma",width:280,required:"",ref:"classnameInput","has-error":n.formErrors.classname.hasError,"error-message":n.formErrors.classname.message,onValidate:t[1]||(t[1]=I=>a.validateField("classname"))},null,8,["modelValue","has-error","error-message"])])]),f("div",ET,[f("div",CT,[f("div",DT,[t[37]||(t[37]=f("label",{class:"form-label"},"Data início da turma",-1)),t[38]||(t[38]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(h,{title:"Ajuda com data início da turma",text:"Insira uma data de início para a turma. Exemplo: 16/03/2025."})]),f("div",xT,[k(m,{modelValue:n.classData.startdate,"onUpdate:modelValue":t[2]||(t[2]=I=>n.classData.startdate=I),type:"date",width:180,required:"",class:"date-input",ref:"startdateInput","has-error":n.formErrors.startdate.hasError,"error-message":n.formErrors.startdate.message,onValidate:t[3]||(t[3]=I=>a.validateField("startdate"))},null,8,["modelValue","has-error","error-message"])])]),f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableenddate}])},[f("div",OT,[t[39]||(t[39]=f("label",{class:"form-label"},"Data fim da turma",-1)),t[40]||(t[40]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(h,{title:"Ajuda com data fim da turma",text:"Insira uma data fim para a turma. Exemplo: 16/12/2025."})]),f("div",ST,[f("div",TT,[k(m,{modelValue:n.classData.optional_fields.enddate,"onUpdate:modelValue":t[4]||(t[4]=I=>n.classData.optional_fields.enddate=I),type:"date",width:180,disabled:!n.classData.optional_fields.enableenddate,required:"",class:"date-input","has-error":n.formErrors.enddate.hasError,"error-message":n.formErrors.enddate.message,onValidate:t[5]||(t[5]=I=>a.validateField("enddate"))},null,8,["modelValue","disabled","has-error","error-message"])]),k(p,{modelValue:n.classData.optional_fields.enableenddate,"onUpdate:modelValue":t[6]||(t[6]=I=>n.classData.optional_fields.enableenddate=I),id:"enableEndDate",label:"Habilitar data fim da turma",class:"inline-checkbox",disabled:!1},null,8,["modelValue"])])],2)]),f("div",NT,[n.classData.enrol=="offer_self"?(O(),N("div",{key:0,class:he(["form-group",{disabled:!n.classData.optional_fields.enablepreenrolment}])},[f("div",AT,[t[41]||(t[41]=f("label",{class:"form-label"},"Data início pré-inscrição",-1)),k(h,{title:"Ajuda com data início pré-inscrição",text:"Data de início do período de pré-inscrição."})]),k(m,{modelValue:n.classData.optional_fields.preenrolmentstartdate,"onUpdate:modelValue":t[7]||(t[7]=I=>n.classData.optional_fields.preenrolmentstartdate=I),type:"date",width:180,disabled:!n.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":n.formErrors.preenrolmentstartdate.hasError,"error-message":n.formErrors.preenrolmentstartdate.message,onValidate:t[8]||(t[8]=I=>a.validateField("preenrolmentstartdate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):ie("",!0),n.classData.enrol=="offer_self"?(O(),N("div",{key:1,class:he(["form-group",{disabled:!n.classData.optional_fields.enablepreenrolment}])},[f("div",IT,[t[42]||(t[42]=f("label",{class:"form-label"},"Data fim pré-inscrição",-1)),k(h,{title:"Ajuda com data fim pré-inscrição",text:"Data de término do período de pré-inscrição."})]),k(m,{modelValue:n.classData.optional_fields.preenrolmentenddate,"onUpdate:modelValue":t[9]||(t[9]=I=>n.classData.optional_fields.preenrolmentenddate=I),type:"date",width:180,disabled:!n.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":n.formErrors.preenrolmentenddate.hasError,"error-message":n.formErrors.preenrolmentenddate.message,onValidate:t[10]||(t[10]=I=>a.validateField("preenrolmentenddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):ie("",!0),n.classData.enrol=="offer_self"?(O(),N("div",MT,[t[43]||(t[43]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"}," ")],-1)),k(p,{modelValue:n.classData.optional_fields.enablepreenrolment,"onUpdate:modelValue":t[11]||(t[11]=I=>n.classData.optional_fields.enablepreenrolment=I),id:"enablePreEnrolment",label:"Habilitar pré-inscrição",disabled:!1},null,8,["modelValue"])])):ie("",!0)]),f("div",PT,[f("div",kT,[t[44]||(t[44]=f("label",{class:"form-label"},"Descrição da turma",-1)),k(h,{title:"Ajuda com descrição da turma",text:"Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025."})]),f("div",VT,[k(_,{modelValue:n.classData.optional_fields.description,"onUpdate:modelValue":t[12]||(t[12]=I=>n.classData.optional_fields.description=I),placeholder:"Digite a descrição da turma aqui...",rows:5,disabled:!1},null,8,["modelValue"])])]),f("div",RT,[n.classData.enrol=="offer_self"?(O(),N("div",LT,[f("div",UT,[t[45]||(t[45]=f("label",{class:"form-label"},"Mínimo de usuários inscritos",-1)),k(h,{title:"Ajuda com mínimo de usuários",text:"Número mínimo de usuários para a turma."})]),f("div",FT,[k(m,{modelValue:n.classData.optional_fields.minusers,"onUpdate:modelValue":t[13]||(t[13]=I=>n.classData.optional_fields.minusers=I),type:"number",width:180},null,8,["modelValue"])])])):ie("",!0),n.classData.enrol=="offer_self"?(O(),N("div",BT,[f("div",$T,[t[46]||(t[46]=f("label",{class:"form-label"},"Máximo de usuários inscritos",-1)),k(h,{title:"Ajuda com máximo de usuários",text:"Número máximo de usuários para a turma."})]),f("div",jT,[k(m,{modelValue:n.classData.optional_fields.maxusers,"onUpdate:modelValue":t[14]||(t[14]=I=>n.classData.optional_fields.maxusers=I),type:"number",width:180},null,8,["modelValue"])])])):ie("",!0),f("div",HT,[f("div",qT,[t[47]||(t[47]=f("label",{class:"form-label"},"Papel atribuído por padrão",-1)),t[48]||(t[48]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(h,{title:"Ajuda com papel atribuído por padrão",text:"O papel atribuído será o papel que o usuário receberá dentro do curso após sua matrícula na turma. Exemplo: Estudante, Professor, Coordenador, etc…"})]),f("div",zT,[k(w,{modelValue:n.classData.optional_fields.roleid,"onUpdate:modelValue":t[15]||(t[15]=I=>n.classData.optional_fields.roleid=I),options:n.roleOptions,width:180,required:"","has-error":n.formErrors.roleid.hasError,"error-message":n.formErrors.roleid.message,onValidate:t[16]||(t[16]=I=>a.validateField("roleid"))},null,8,["modelValue","options","has-error","error-message"])])])]),f("div",WT,[f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableenrolperiod}])},[f("div",GT,[t[49]||(t[49]=f("label",{class:"form-label"},"Prazo de conclusão da turma",-1)),t[50]||(t[50]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(h,{title:"Ajuda com prazo de conclusão da turma",text:"O prazo de conclusão refere-se a quantidade de dias que um usuário terá para realizar o curso. Após esse período, se o usuário não obtiver uma situação de êxito na matrícula (Aprovado ou Concluído) durante seu progresso no curso, o sistema encerrará sua matrícula."+(a.maxEnrolPeriod?` O valor máximo permitido é de ${a.maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`:"")},null,8,["text"])]),f("div",KT,[f("div",YT,[k(m,{modelValue:n.classData.optional_fields.enrolperiod,"onUpdate:modelValue":t[17]||(t[17]=I=>n.classData.optional_fields.enrolperiod=I),type:"number",width:180,disabled:!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.enrolperiod.hasError,"error-message":n.formErrors.enrolperiod.message,max:a.maxEnrolPeriod,onValidate:t[18]||(t[18]=I=>a.validateField("enrolperiod"))},null,8,["modelValue","disabled","has-error","error-message","max"])]),lt(k(p,{modelValue:n.classData.optional_fields.enableenrolperiod,"onUpdate:modelValue":t[19]||(t[19]=I=>n.classData.optional_fields.enableenrolperiod=I),id:"enableEnrolPeriod",label:"Habilitar prazo de conclusão",class:"inline-checkbox",disabled:a.shouldDisableEnrolPeriod},null,8,["modelValue","disabled"]),[[te,a.shouldDisableEnrolPeriod?"Não é possível habilitar prazo de conclusão para turmas de um dia (data início = data fim)":""]])])],2)])]),f("div",QT,[f("div",ZT,[f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[f("div",JT,[t[52]||(t[52]=f("label",{class:"form-label"},"Prorrogação de matrícula",-1)),k(h,{title:"Ajuda com prorrogação de matrícula",text:"A prorrogação estende o Prazo de conclusão do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou. Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado."})]),lt(k(p,{modelValue:n.classData.optional_fields.enableextension,"onUpdate:modelValue":t[20]||(t[20]=I=>n.classData.optional_fields.enableextension=I),id:"enableExtension",label:"Habilitar Prorrogação de matrícula",disabled:!n.classData.optional_fields.enableenrolperiod},null,8,["modelValue","disabled"]),[[te,n.classData.optional_fields.enableenrolperiod?"":"É necessário habilitar o Prazo de conclusão da turma primeiro"]])],2)]),f("div",XT,[f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[53]||(t[53]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantos dias serão acrescentados para prorrogação?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",eN,[k(m,{modelValue:n.classData.optional_fields.extensionperiod,"onUpdate:modelValue":t[21]||(t[21]=I=>n.classData.optional_fields.extensionperiod=I),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensionperiod.hasError,"error-message":n.formErrors.extensionperiod.message,onValidate:t[22]||(t[22]=I=>a.validateField("extensionperiod"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",tN,[f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[54]||(t[54]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantos dias antes do término do prazo de matrícula o botão de prorrogação deve ser exibido?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",sN,[k(m,{modelValue:n.classData.optional_fields.extensiondaysavailable,"onUpdate:modelValue":t[23]||(t[23]=I=>n.classData.optional_fields.extensiondaysavailable=I),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensiondaysavailable.hasError,"error-message":n.formErrors.extensiondaysavailable.message,onValidate:t[24]||(t[24]=I=>a.validateField("extensiondaysavailable"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",rN,[f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[55]||(t[55]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantas vezes o usuário pode pedir prorrogação?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",nN,[k(m,{modelValue:n.classData.optional_fields.extensionmaxrequests,"onUpdate:modelValue":t[25]||(t[25]=I=>n.classData.optional_fields.extensionmaxrequests=I),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensionmaxrequests.hasError,"error-message":n.formErrors.extensionmaxrequests.message,onValidate:t[26]||(t[26]=I=>a.validateField("extensionmaxrequests"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",{class:he(["form-group mb-3",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[56]||(t[56]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Para quais situações de matrícula é permitida a prorrogação?")],-1)),f("div",oN,[k(D,{modelValue:n.extensionSituations,"onUpdate:modelValue":t[27]||(t[27]=I=>n.extensionSituations=I),items:a.extensionSituationList,placeholder:"Selecione as situações...",disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllExtensionSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)]),n.classData.enrol=="offer_self"?(O(),N("div",iN,[f("div",aN,[f("div",lN,[f("div",uN,[t[57]||(t[57]=f("label",{class:"form-label"},"Habilitar rematrícula",-1)),k(h,{title:"Ajuda com habilitar rematrícula",text:"Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela."})]),k(p,{modelValue:n.classData.optional_fields.enablereenrol,"onUpdate:modelValue":t[28]||(t[28]=I=>n.classData.optional_fields.enablereenrol=I),id:"enableReenrol",label:"Habilitar rematrícula",disabled:!1},null,8,["modelValue"])])]),f("div",{class:he(["form-group mb-3",{disabled:!n.classData.optional_fields.enablereenrol}])},[t[58]||(t[58]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quais situações de matrícula permitem rematrícula?")],-1)),f("div",cN,[k(D,{modelValue:n.reenrolSituations,"onUpdate:modelValue":t[29]||(t[29]=I=>n.reenrolSituations=I),items:a.reenrolSituationList,placeholder:"Selecione as situações...",disabled:!n.classData.optional_fields.enablereenrol,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllReenrolSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)])):ie("",!0),f("div",dN,[f("div",fN,[f("div",hN,[t[59]||(t[59]=f("label",{class:"form-label"},"Atribuir corpo docente",-1)),k(h,{title:"Ajuda com atribuir corpo docente",text:"Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."})]),f("div",pN,[f("div",mN,[f("div",gN,[lt(f("input",{type:"text","onUpdate:modelValue":t[30]||(t[30]=I=>n.teacherSearchTerm=I),placeholder:"Pesquisar ...",class:"form-control custom-input",onInput:t[31]||(t[31]=(...I)=>a.handleTeacherInput&&a.handleTeacherInput(...I)),onFocus:t[32]||(t[32]=(...I)=>a.handleTeacherInputFocus&&a.handleTeacherInputFocus(...I)),onKeydown:t[33]||(t[33]=(...I)=>a.handleKeydown&&a.handleKeydown(...I)),ref:"teacherSearchInput"},null,544),[[Xt,n.teacherSearchTerm]])]),n.showTeacherDropdown&&n.teacherList.length>0?(O(),N("div",_N,[(O(!0),N(Me,null,dt(n.teacherList,(I,se)=>(O(),N("div",{key:I.id,class:he(["dropdown-item",{active:n.highlightedIndex===se}]),onClick:K=>a.selectTeacher(I),onMouseenter:K=>n.highlightedIndex=se},[f("div",null,[f("div",null,G(I.fullname),1),I.email?(O(),N("div",yN,G(I.email),1)):ie("",!0)])],42,vN))),128))],512)):ie("",!0),n.showTeacherDropdown&&n.teacherSearchTerm.length>=3&&n.teacherList.length===0?(O(),N("div",bN,t[60]||(t[60]=[f("div",{class:"dropdown-item-text text-center fst-italic"},"Nenhum professor encontrado",-1)]))):ie("",!0)],512),f("div",wN,[(O(!0),N(Me,null,dt(n.selectedTeachers,(I,se)=>(O(),N("a",{key:I.id,class:"tag badge bg-primary text-white p-2 cursor-pointer mr-2",onClick:K=>a.removeTeacher(I.id)},[t[61]||(t[61]=f("i",{class:"fas fa-times mr-1"},null,-1)),nt(" "+G(I.fullname),1)],8,EN))),128))])])])]),t[63]||(t[63]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[nt(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),f("div",CN,[k(V,{variant:"primary",label:"Salvar",loading:n.loading,onClick:a.saveClass},null,8,["loading","onClick"]),k(V,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])]),n.loading?(O(),N("div",DN,t[62]||(t[62]=[f("div",{class:"spinner-border",role:"status"},[f("span",{class:"sr-only"},"Carregando...")],-1)]))):ie("",!0),k(F,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],512)}const cp=He(pT,[["render",xN],["__scopeId","data-v-64469837"]]),ON=[{path:"/",name:"listar-ofertas",component:Z1,meta:{title:"Gerenciar Ofertas"}},{path:"/new-offer",name:"nova-oferta",component:up,meta:{title:"Nova Oferta"}},{path:"/edit-offer/:id",name:"editar-oferta",component:up,props:!0,meta:{title:"Editar Oferta"}},{path:"/new-class/:offercourseid",name:"NewClass",component:cp,props:!0,meta:{title:"Nova Turma"}},{path:"/edit-class/:offercourseid/:classid",name:"EditClass",component:cp,props:!0,meta:{title:"Editar Turma"}},{path:"/new-subscribed-users/:offerclassid",name:"usuarios-matriculados",component:Kx,props:!0,meta:{title:"Usuários matriculados"}},{path:"/:pathMatch(.*)*",redirect:"/"}],jo=Kw({history:ow("/local/offermanager/"),routes:ON,scrollBehavior(){return{top:0}}});jo.beforeEach((e,t,s)=>{document.title=e.meta.title||"Gerenciar Ofertas",s()}),jo.onError(e=>{console.error("Erro de navegação:",e),(e.name==="NavigationDuplicated"||e.message.includes("No match")||e.message.includes("missing required param"))&&jo.push("/")});const QR="",SN=()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)};return{init:(e,t={})=>{SN();const s=Gy(Nb);if(s.use(xb()),s.use(jo),t&&t.route){let n={"new-offer":"/new-offer","edit-offer":"/edit-offer","new-subscribed-users":"/new-subscribed-users","new-class":"/new-class","edit-class":"/edit-class"}[t.route]||"/";t.route==="edit-offer"&&t.offerId&&(n=`/edit-offer/${t.offerId}`),t.route==="new-subscribed-users"&&t.subscribeId&&(n=`/new-subscribed-users/${t.subscribeId}`),t.route==="new-class"&&t.offercourseid&&(n=`/new-class/${t.offercourseid}`),t.route==="edit-class"&&t.offercourseid&&t.classid&&(n=`/edit-class/${t.offercourseid}/${t.classid}`),jo.replace(n)}return s.mount(e),s}}});
//# sourceMappingURL=app-lazy.min.js.map
